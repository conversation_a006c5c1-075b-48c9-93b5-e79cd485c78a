package com.snbc.bbpf.bus.product.manager.config.payconfig;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.snbc.bbpf.bus.product.manager.config.CommonResp;

import java.util.Objects;

/**
 * 功能描述: <br>
 * 〈微信配置响应信息〉
 *
 * @param: null
 * @return:
 * @exception
 * @author: gs
 * @date: 2019-08-08 16:28
 */

@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2017-05-16T10:40:30.367Z")

public class WechatInfoResp {
    @JsonProperty("header")
    private CommonResp header;

    @JsonProperty("body")
    private WechatConfigDto body;

    public WechatInfoResp header(CommonResp header) {
        this.header = header;
        return this;
    }

    /**
     * 响应信息头
     *
     * @return header
     **/
    
    public CommonResp getHeader() {
        return header;
    }

    public void setHeader(CommonResp header) {
        this.header = header;
    }

    public WechatInfoResp body(WechatConfigDto body) {
        this.body = body;
        return this;
    }

    /**
     * 响应内容
     *
     * @return body
     **/
    
    public WechatConfigDto getBody() {
        return body;
    }

    public void setBody(WechatConfigDto body) {
        this.body = body;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WechatInfoResp wechatInfoResp = (WechatInfoResp) o;
        return Objects.equals(this.header, wechatInfoResp.header) &&
                Objects.equals(this.body, wechatInfoResp.body);
    }

}

