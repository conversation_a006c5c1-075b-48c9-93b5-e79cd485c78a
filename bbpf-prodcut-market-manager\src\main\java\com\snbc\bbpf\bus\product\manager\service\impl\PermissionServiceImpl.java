/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.converter.PermissionToPermissionVoConverter;
import com.snbc.bbpf.bus.product.manager.converter.PermissionToPermissionNodeConverter;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.service.PermissionService;
import com.snbc.bbpf.bus.product.manager.utils.CurrentUser;
import com.snbc.bbpf.bus.product.market.common.dto.product.MenuPermissionDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.PermissionNode;
import com.snbc.bbpf.bus.product.market.common.dto.product.PermissionSortVo;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import com.snbc.bbpf.bus.product.market.common.entity.ProductServices;
import com.snbc.bbpf.bus.product.market.common.mapper.PermissionMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPermissionMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductServicesMapper;
import com.snbc.bbpf.bus.product.market.common.vo.PermissionVo;
import com.snbc.bbpf.bus.product.market.common.vo.PermissionsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * @ClassName: PermissionServiceImpl
 * 权限定义实现类
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Service
@Transactional
@Slf4j
public class PermissionServiceImpl implements PermissionService {

    public static final String PATHSPLIT = "/";
    public static final String PERMISSION_LEVEL = "permissionLevel";
    public static final String LEVEL = "level";
    public static final String PERMISSION_DESC = "permissionDesc";
    public static final String PERMISSION_IMAGE = "permissionImage";
    public static final String REMARKS = "remarks";
    public static final String PERMISSION_ICON = "permissionIcon";
    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionServiceImpl.class);


    @Autowired
    private PermissionMapper permissionMapper;
    @Autowired
    private ProductPermissionMapper productPermissionMapper;

    @Autowired
    private ProductServicesMapper productServicesMapper;
    //缓存过期时间
    @Value("${bbpf.system.security.catch.expiration}")
    private Integer expireTime;

    //系统获取菜单的默认父级节点
    @Value("${bbpf.system.menu.parentid}")
    private String menuParentid;

    public Integer getExpireTime() {
        if(null==expireTime){
            expireTime= Constant.NO_THREE_K_SIXH;
        }
        return expireTime;
    }
    /**
     * 查询所有权限树节点
     *
     * @return
     */
    @Override
    public List<Permission> getAllPermission() {
        return permissionMapper.getAllPermission();
    }
    public PageInfo<PermissionVo> getAllPermissionContainRoot(Integer pageNum,Integer pageSize) {
        pageNum = Math.abs(pageNum);
        pageSize = Math.abs(pageSize);
        List<Permission> resultList = permissionMapper.getAllPermissionContainRoot();
        return installPage(resultList,pageNum,pageSize,null);
    }
    private PageInfo<PermissionVo> installPage(List<Permission> resultList,Integer pageNum,Integer pageSize,String serachKey){
        //搜索条件不为空时 不进行树状排序 因为可能存在没有根节点的情况 已和产品确认 jiafei20211117
        List<Permission> installList = null;
        if(StringUtils.isNotBlank(serachKey)){
            installList = resultList;
        }else{
            //全部数据排序
            installList = installList(resultList);
            LOGGER.info("getAllPermissionContainRootById installList:{}",installList.get(0).getPermissionName());
            //右侧列表不展示根节点 移除
            if(!CollectionUtils.isEmpty(installList) && "-1".equalsIgnoreCase(installList.get(0).getPermissionId())){
                installList = installList.subList(1,installList.size());
            }
        }
        //数据分页
        List<Permission> subList = installList.subList((pageNum-1)*pageSize > installList.size() ? installList.size() : ((pageNum-1)*pageSize),
                pageNum*pageSize > installList.size() ? installList.size() : (pageNum*pageSize));
        //封装VO数据
        List<PermissionVo> perVoList = new ArrayList<>();
        subList.forEach(item->{
            PermissionVo permissionVo = PermissionToPermissionVoConverter.INSTANCE.to(item);
            perVoList.add(permissionVo);

        });
        //封装page数据
        PageInfo<PermissionVo> page = new PageInfo<>(perVoList);
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotal(resultList.size());
        LOGGER.info("getAllPermissionContainRootById perVoList:{}",perVoList.size());
        return page;
    }
    private List<Permission> installList(List<Permission> perList){
        Permission root = perList.get(0);
        return buildTable(root, perList);
    }
    public PageInfo<PermissionVo> getAllPermissionContainRootById(String permissionId,Integer pageNum,Integer pageSize,String serachKey) {
        pageNum = Math.abs(pageNum);
        pageSize = Math.abs(pageSize);
        //全部数据查询
        List<Permission> resultList = permissionMapper.getAllPermissionById(permissionId,serachKey);
        LOGGER.info("getAllPermissionContainRootById resultList:{}",resultList.size());
        if(!resultList.isEmpty()) {
            return installPage(resultList, pageNum, pageSize, serachKey);
        }
        PageInfo<PermissionVo> objectPageInfo = new PageInfo<>(new ArrayList<>());
        objectPageInfo.setPageSize(pageSize);
        return objectPageInfo;
    }
    /**
     *
     * @param parentPer
     * @param permissionList
     * @return
     */
    private List<Permission>  buildTable(Permission parentPer,List<Permission> permissionList) {
        List<Permission> resultList = new ArrayList<>();
        resultList.add(parentPer);
        List<Permission> childen = permissionList.stream()
                .filter(item -> item.getParentId().equals(parentPer.getPermissionId()))
                .sorted(Comparator.comparing(Permission::getOrderBy)).collect(Collectors.toList());
        if (childen == null || childen.isEmpty()) {
            return resultList;
        }
        childen.stream().forEach(item -> resultList.addAll(buildTable(item, permissionList)));
        return resultList;
    }
    /**
     * 查询所有权限树节点
     * @return
     */
    @Override
    public List<PermissionsVo> getEnableAllPermission(String productId,String sysType ){
        //先获取所有的
        List<Permission> list= permissionMapper.getEnableAllPermission();
        List<PermissionsVo> permissionVoList=new ArrayList<>();
        List<String> permissionIds=permissionMapper.selectResourcePermissionListByRoleId(productId.split(","),sysType);
        if(list!=null&& !list.isEmpty()){
            //全局的缓存的对象
            Map<String, PermissionsVo> collect = new HashMap<>();
            Map<String, String> dicMap = new HashMap<>();
            //权限类型  1系统，2菜单，3按钮，4接口
            dicMap.put("4","接口");
            dicMap.put("1","系统");
            dicMap.put("2","菜单");
            dicMap.put("3","按钮");
            ///   开始根据情况进行处理,如果父节点为为LEVEL 则进行初始化添加
            list.forEach(ej -> {
                //当其父节点等于，筛选值的时候，开始初始化如果要包含父节点，则把条件设置为当前节点
                if (menuParentid.equals(ej.getParentId())&&
                        !collect.containsKey(ej.getPermissionId())) {
                    PermissionsVo resultc = changePermissionVo(ej,permissionIds,dicMap);
                    //给父节点进行赋值
                    collect.put(ej.getPermissionId(), resultc);
                    permissionVoList.add(resultc);
                }
                if(!menuParentid.equals(ej.getParentId())&&
                        collect.containsKey(ej.getParentId())) {
                    //当缓存里有数据才开始进行插入处理，否则认为条件不允许
                    PermissionsVo curp = collect.get(ej.getParentId());
                    //从缓存里获取值
                    PermissionsVo curb = changePermissionVo(ej,permissionIds,dicMap);
                    //生成一个当前对象
                    curp.getChildren().add(curb);
                    collect.put(ej.getPermissionId(), curb);
                }
            });
        }
        return permissionVoList;
    }
    /**
     * 特殊对象转换
     * @return
     */
    private PermissionsVo changePermissionVo(Permission po,List<String> permissionIds,Map<String, String> dicMap){
        //权限类型  1系统，2菜单，3按钮，4接口
        return PermissionsVo.builder().children(new ArrayList<>()    )
                .hasChecked((null!=permissionIds&&!permissionIds.isEmpty())&&
                        permissionIds.contains(po.getPermissionId()))
                .orderBy(po.getOrderBy())
                .parentId(po.getParentId())
                .permissionCode(po.getPermissionCode())
                .permissionId(po.getPermissionId())
                .permissionImage(po.getPermissionImage())
                .permissionName(po.getPermissionName())
                .permissionType(po.getPermissionType())
                .sysType(po.getSysType())
                .permissionTypeName(dicMap.containsKey(po.getPermissionType())?
                        dicMap.get(po.getPermissionType()):"")
                .routingUrl(po.getRoutingUrl()).build();
    }

    /**
     * 根据父节点获取所有子节点以及下属节点
     *
     * @param parentId
     * @return
     */
    @Override
    public List<Permission> getAllPermissionByParentId(String parentId) {
        return permissionMapper.getPermissionByParentId(parentId);
    }

    /**
     * 获取所有子节点以及下属节点
     *
     * @param permissionId
     * @return
     */
    @Override
    public List<Permission> getAllPermissionById(String permissionId) {
        return permissionMapper.getAllPermissionById(permissionId,"");
    }

    /**
     * 插入资源权限
     * 当父节点为空的时候不让添加
     */
    @Override
    @CacheEvict(value = Constant.PERMISSION_CACHE_CATEGORY, allEntries = true)
    public int insertPermission(Permission permission) throws Exception {
        Permission parent=permissionMapper.selectByPrimaryKey(permission.getParentId());
        String permissionId= UUID.randomUUID().toString();
        permission.setPermissionId(permissionId);
        String permissionPath=parent.getPermissionPath()+ Constant.PERMISSION_FILTER_CHAR+permissionId;
        permission.setPermissionPath(permissionPath);
        permission.setSysType(0);
        permission.setPermissionLevel(parent.getPermissionLevel()+1);
        permission.setParentName(parent.getPermissionName());
        int maxOrderby = this.getMaxOrderByParentId(permission.getParentId());
        permission.setCreateUserId(CurrentUser.getUserId());
        permission.setCreateTime(LocalDateTime.now());
        permission.setUpdateTime(LocalDateTime.now());
        permission.setOrderBy(maxOrderby + 1);
        return permissionMapper.insertSelective(permission);
    }
    @Override
    @CacheEvict(value = Constant.PERMISSION_CACHE_CATEGORY, allEntries = true)
    public int changeStatus(Permission permission) throws Exception {
        permission.setUpdateTime(LocalDateTime.now());
        return permissionMapper.updateByPrimaryKeySelective(permission);
    }
    /**
     * 更新资源权限代码
     */
    @Override
    @CacheEvict(value = Constant.PERMISSION_CACHE_CATEGORY, allEntries = true)
    public int updatePermission(Permission permission) throws Exception {
        List<String> roleIds=permissionMapper.selectRoleListByPermissionId(permission.getPermissionId());
        Permission per = permissionMapper.selectByPrimaryKey(permission.getPermissionId());
        if(roleIds!=null&&!roleIds.isEmpty()){
            ProductServices ps = productServicesMapper.selectByPrimaryKey(roleIds.get(0));
            String msg = per.getPermissionName()+"功能以绑定"+ps.getProductName()+"产品，需先解绑产品服务再进行编辑。";
            throw  new BusinessException(msg,"995402");
        }
        if(StringUtils.isNotBlank(permission.getPermissionCode())){
            Permission perCode = permissionMapper.selectByPermissionCode(permission.getPermissionCode());
            if (null != perCode && !perCode.getPermissionId().equalsIgnoreCase(permission.getPermissionId())) {
                throw  new BusinessException("功能编码已存在","995401");
            }
        }
        permission.setUpdateTime(LocalDateTime.now());
        return permissionMapper.updateByPrimaryKeySelective(permission);
    }
    /**
     * 权限树
     *
     * @return
     */
    @Override
    public PermissionNode getPermissionTree(int level) {
        List<Permission> permissions = permissionMapper.getAllPermissionWithRoot();
        Optional<Permission> list = permissions.stream().filter(item -> "-1".equals(item.getPermissionId())).findFirst();
        Permission root = null;
        if (list.isPresent()) {
            root = list.get();
        }
        PermissionNode node = convertNode(root);
        if (level == 0) {
            level = permissions.stream().mapToInt(Permission::getPermissionLevel).max().getAsInt();
        }
        buildTree(node, permissions, level);
        return node;
    }

    /**
     *
     * @param permission
     * @return
     */
    private static PermissionNode convertNode(Permission permission){
        return PermissionToPermissionNodeConverter.INSTANCE.to(permission);
    }
    /**
     *
     * @param parentNode
     * @param permissionList
     * @param level
     */
    private void  buildTree(PermissionNode parentNode,List<Permission> permissionList,int level) {
        List<PermissionNode> childen = permissionList.stream()
                .filter(item -> item.getParentId().equals(parentNode.getPermissionId()))
                .map(PermissionServiceImpl::convertNode).sorted(Comparator.comparing(PermissionNode::getOrderBy)).collect(Collectors.toList());
        if (childen == null || childen.isEmpty()) {
            return;
        }
        parentNode.setChildren(childen);
        int currentLevel = childen.stream().mapToInt(PermissionNode::getLevel).max().getAsInt();
        if (currentLevel <= level) {
            childen.stream().forEach(item -> buildTree(item, permissionList, level));
        }
    }
    @Override
    public Boolean permissionSort(PermissionSortVo perSort) throws Exception {
        try {
            if (Constant.INNER.equals(perSort.getType())) {
                int maxOrderByParentId = this.getMaxOrderByParentId(perSort.getTargetNodeId());
                PermissionVo targetNode = this.queryPermission(perSort.getTargetNodeId());
                Permission permission = permissionMapper.selectByPrimaryKey(perSort.getCurrentNodeId());
                //根据父节点ID获取系统类型
                Permission parentPermission = permissionMapper.selectByPrimaryKey(perSort.getTargetNodeId());
                permission.setPermissionId(perSort.getCurrentNodeId());
                permission.setOrderBy(maxOrderByParentId + 1);
                permission.setParentId(perSort.getTargetNodeId());

                permission.setSysType(parentPermission.getSysType());
                permission.setPermissionLevel(targetNode.getPermissionLevel() + 1);
                String newPath=parentPermission.getPermissionPath()+ PATHSPLIT +perSort.getCurrentNodeId();
                permission.setPermissionPath(newPath);
                permissionMapper.updatePermissionPath(permission.getPermissionPath(),newPath);
                this.updatePermission(permission);
                permissionMapper.updatePermissionLevel();
                return true;
            } else {
                // 根据父节点查询所有子节点
                List<Permission> permissionList = permissionMapper.getPermissionByParentId(perSort.getParentId());
                // 对子节点重新排序
                List<Permission> perSortList = sort(permissionList, perSort);
                permissionMapper.updatePermissionList(perSortList);
                permissionMapper.updatePermissionLevel();
                return true;
            }
        }catch (Exception ex){
            log.error("Drag sort exception，params：{}",perSort,ex);
            throw  ex;
        }
    }
    /**
     * 权限树排序操作
     *
     * @param perList
     * @param permissionSortVo
     * @return
     */
    private List<Permission> sort(List<Permission> perList, PermissionSortVo permissionSortVo) {
        // 目标节点的下标
        int targetNodeSubscript = 0;
        Permission perSort = permissionMapper.selectByPrimaryKey(permissionSortVo.getCurrentNodeId());
        perSort.setPermissionId(permissionSortVo.getCurrentNodeId());
        perSort.setParentId(permissionSortVo.getParentId());
        int flag=-1;
        //
        for (int i = 0; i < perList.size(); i++) {
            if (perList.get(i).getPermissionId().equals(permissionSortVo.getCurrentNodeId())) {
                perSort=perList.get(i);
                flag=i;
            }
            if (perList.get(i).getPermissionId().equals(permissionSortVo.getTargetNodeId())) {
                if (permissionSortVo.getType().equals(Constant.PREV)) {
                    targetNodeSubscript = i;
                } else {
                    targetNodeSubscript = i + 1;
                }
            }
        }
        if(flag>-1){
            perList.remove(flag);
            if(flag<targetNodeSubscript){
                targetNodeSubscript=targetNodeSubscript-1;
            }
        }
        perList.add(targetNodeSubscript, perSort);
        String oldPath=perSort.getPermissionPath();
        Permission parentPermission=permissionMapper.selectByPrimaryKey(permissionSortVo.getParentId());
        String newPath=parentPermission.getPermissionPath()+ PATHSPLIT +perSort.getPermissionId();
        for (int i = 0; i < perList.size(); i++) {
            Permission permission = perList.get(i);
            permission.setPermissionPath(parentPermission.getPermissionPath()+ PATHSPLIT +permission.getPermissionId());
            permission.setOrderBy(i);
        }
        permissionMapper.updatePermissionPath(oldPath,newPath);
        return perList;
    }

    /**
     * 根据父节点获取子节点最大的排序号
     *
     * @param parentId 父节点id
     * @return 返回最大序号值
     */
    private int getMaxOrderByParentId(String parentId) {
        return permissionMapper.queryMaxOrderByParentId(parentId);
    }

    /**
     * 获取资源权限
     *
     * @param roleIds
     * @param sysType
     * @return
     * @throws IOException
     */
    @Override
    public List<String> getResourcePermissionsByRoleIds(String roleIds, String sysType) throws IOException {
        return  permissionMapper.selectResourcePermissionListByRoleId(roleIds.split(","),sysType);
    }


    /**
     * 根据资源权限IDs删除资源权限
     *
     * @param permissionIds 资源权限ids
     */
    @Override
    @CacheEvict(value = Constant.PERMISSION_CACHE_CATEGORY, allEntries = true)
    public void deletePermissions(String[] permissionIds) throws Exception {
        Arrays.stream(permissionIds).forEach(permissionId->{
            List<Permission> permissions=permissionMapper.getPermissionByParentId(permissionId);
            Permission permission = permissionMapper.selectByPrimaryKey(permissionId);
            if(permissions!=null&&!permissions.isEmpty()){
                String msg = permission.getPermissionName()+"功能存在子节点，请删除子节点后再删除。";
                throw  new BusinessException(msg,"995403");
            }
            List<String> roleIds=permissionMapper.selectRoleListByPermissionId(permissionId);
            if(roleIds!=null&&!roleIds.isEmpty()){
                ProductServices ps = productServicesMapper.selectByPrimaryKey(roleIds.get(0));
                String msg = permission.getPermissionName()+"功能已绑定"+ps.getProductName()+"产品，需先解绑产品服务权限关联再删除。";
                throw  new BusinessException(msg,"995404");
            }
        });
        permissionMapper.deleteByPermissionIds(permissionIds);
    }


    /**
     * 根据资源权限Id获取资源权限信息
     */
    @Override
    public PermissionVo queryPermission(String permissionId) throws Exception {
        List<String> roleIds=permissionMapper.selectRoleListByPermissionId(permissionId);
        Permission permission = permissionMapper.selectByPrimaryKey(permissionId);
        if(roleIds!=null&&!roleIds.isEmpty()){
            ProductServices ps = productServicesMapper.selectByPrimaryKey(roleIds.get(0));
            String msg = permission.getPermissionName()+"功能已绑定"+ps.getProductName()+"产品，需先解绑产品服务再进行编辑。";
            throw  new BusinessException(msg,"995402");
        }
        return PermissionToPermissionVoConverter.INSTANCE.to(permission);
    }

    /**
     * 查询系统资源信息
     *
     * @param permissionCode
     * @return
     * @throws Exception
     */
    @Override
    public Permission selectByPermissionCode(String permissionCode) throws Exception {
        return  permissionMapper.selectByPermissionCode(permissionCode);
    }

    /***
      * @Description:    查询最后一级的菜单及下属功能接口
      * @Author:         wangsong
      * @CreateDate:     2021/11/24 16:13
      * @UpdateDate:     2021/11/24 16:13
      * @return :        java.util.List<com.snbc.bbpf.bus.product.market.common.dto.product.MenuPermissionDto>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public List<MenuPermissionDto> getAllPermissionList() {
        //获取所有菜单权限
        List<Permission> menuPermissionList = permissionMapper.getMenuPermission();
        //菜单根据父id分组
        Map<String, List<Permission>> permissionMap = menuPermissionList.stream().collect(Collectors.groupingBy(Permission::getParentId));
        Iterator<Map.Entry<String, List<Permission>>> iterator = permissionMap.entrySet().iterator();
        List<String> leafNodePermissionIdList = new ArrayList<>();
        List<String> parentIdList = new ArrayList<>();
        //循环分组后的权限，取子节点的id
        while (iterator.hasNext()){
            Map.Entry<String, List<Permission>> next = iterator.next();
            parentIdList.add(next.getKey());
            for (Permission permission : next.getValue()) {
                leafNodePermissionIdList.add(permission.getPermissionId());
            }
        }
        //删除父节点菜单的id。只查询最后一层菜单的id
        leafNodePermissionIdList.removeAll(parentIdList);
        //permission子节点id去重
        List<String> funcationIds = leafNodePermissionIdList.stream().distinct().collect(Collectors.toList());
        return permissionMapper.getAllPermissionForProductDetil(funcationIds);
    }
}
