package com.snbc.bbpf.bus.product.market.common.dto.product;

import com.snbc.bbpf.bus.product.market.common.entity.ProductVemsBase;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 服务可用情况类型实体类
 */
@Data
public class ProductAvailableDto extends ProductVemsBase {
    /**
     * ID
     */
    private String availableId;

    /**
     * 售货机name
     */
    private String vemsName;
    /**
     * 服务开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 服务结束时间
     */
    private LocalDateTime dueTime;
    /**
     * 有效值
     */
    private Double availableValue;
    /**
     * 有效值单位
     */
    private String availableUnit;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private String productCode;
}
