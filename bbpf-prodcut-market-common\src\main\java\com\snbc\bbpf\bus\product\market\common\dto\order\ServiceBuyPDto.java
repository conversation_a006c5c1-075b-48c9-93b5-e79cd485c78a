package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/*
* @author: liangJB
     * 功能描述: <br>
     * 设备标准列表
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
productId	String	是	服务编号
vemsId	String	是	售货机编号
vemsName	String	是	售货机名称
beginTime	date	是	激活时间
dueTime	date	是	到期时间
serviceStatus	int	是	服务状态，0表示已过期其它值表示剩下几个月
vemsPic	String	是	售货机图片
vemsStatus	String	是	设备售卖情况1可售，2不可售
vemsAddress	String	是	售货机地址



* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceBuyPDto {

    
    private String productId;
    
    private String productCode;
    
    private String vemsId;
    
    private String vemsName;
    
    private String vemsPic;
    
    private String vemsStatus;
    
    private String vemsAddress;

    
    private Integer serviceStatus;

    
    private double servicePercent;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime beginTime;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime dueTime;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime activationTime;
}
