package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 产品服务阶梯
 */
@Data
public class ProductGradeVo {
    //阶梯id

    private  String productGradeId;
    //产品服务Id

    @NotEmpty(message = "产品服务id不能为空")
    private  String productId;
    //阶梯

    @NotEmpty(message = "阶梯不能为空")
    private  String grade;
    //阶梯单位

    private  String gradeUnit;
    //阶梯折扣

    private  Long gradeDiscount;
    //价格

    private  String  price;
    //是否默认

    private  Integer isDefault;

}
