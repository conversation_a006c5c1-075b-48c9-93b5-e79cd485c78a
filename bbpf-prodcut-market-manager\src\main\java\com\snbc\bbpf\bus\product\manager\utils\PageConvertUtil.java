package com.snbc.bbpf.bus.product.manager.utils;

import com.github.pagehelper.PageInfo;

public class PageConvertUtil {
    public static PageInfo convert(PageInfo page, PageInfo pageDO) {
        page.setPageNum(pageDO.getPageNum());
        page.setPageSize(pageDO.getPageSize());
        page.setSize(pageDO.getSize());
        page.setStartRow(pageDO.getStartRow());
        page.setEndRow(pageDO.getEndRow());
        page.setTotal(pageDO.getTotal());
        page.setPages(pageDO.getPages());
        page.setPrePage(pageDO.getPrePage());
        page.setNextPage(pageDO.getNextPage());
        page.setIsFirstPage(pageDO.isIsFirstPage());
        page.setIsLastPage(pageDO.isIsLastPage());
        page.setHasPreviousPage(pageDO.isHasPreviousPage());
        page.setHasNextPage(pageDO.isHasNextPage());
        page.setNavigatePages(pageDO.getNavigatePages());
        page.setNavigatepageNums(pageDO.getNavigatepageNums());
        return page;
    }
}
