package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.ProductGrade;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: LiangJB
 * 功能描述: <br>
 * 阶梯记录表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface ProductGradeMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(ProductGrade productGrade);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(ProductGrade productGrade);

    /**
     *
     * @param productId
     * @return
     */
    int deleteByProductId(@Param("productId") String productId);

}
