package com.snbc.bbpf.bus.product.market.common.dto.invoice;

import com.snbc.bbpf.bus.product.market.common.entity.InvoiceTitle;
import lombok.Data;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.dto.invoice
 * @ClassName: InvoiceTitleDto
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/10/26 18:32
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/26 18:32
 */
@Data
public class InvoiceTitleDto extends InvoiceTitle {
    //发票类型
    private String invoiceType;
    //发票介质
    private String invoiceCarrier;
}
