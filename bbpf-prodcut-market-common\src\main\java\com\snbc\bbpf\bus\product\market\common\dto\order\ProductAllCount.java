package com.snbc.bbpf.bus.product.market.common.dto.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/*
* @author: liangJB
     * 功能描述: <br>
     * 服务整体统计
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
serviceCount	Decimal	是	服务总数
serviceCountAmount	Decimal	是	原价总金额
serviceCountDisAmount	Decimal	是	优惠总金额
serviceCountPayAmount	Decimal	是	实付总金额
* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductAllCount {
    
    private int serviceCount;
    
    private BigDecimal serviceCountAmount;
    
    private BigDecimal serviceCountDisAmount;
    
    private BigDecimal serviceCountPayAmount;
}
