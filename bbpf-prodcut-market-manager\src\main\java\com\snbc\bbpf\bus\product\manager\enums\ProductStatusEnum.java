package com.snbc.bbpf.bus.product.manager.enums;

/**
 * @ClassName: ProductStatusEnum
 * @Description: 产品服务枚举类
 * @module: SI-bbpf-product-manage
 * @Author: wangsong
 * @date: 2021/11/17
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public enum ProductStatusEnum {
    UNPUBLISHED("0", "待发布"),
    PUBLISHED("1", "已发布"),
    OFFSHELF("2", "已下架"),
    ;

    private String productStatus;
    private String productStatusName;

    ProductStatusEnum(String productStatus, String productStatusName) {
        this.productStatus = productStatus;
        this.productStatusName = productStatusName;
    }

    public static String getProductStatusName(String productStatus) {
        for (ProductStatusEnum p : ProductStatusEnum.values()) {
            if (p.productStatus.equals(productStatus)) {
                return p.productStatusName;
            }
        }
        return null;
    }
}
