package com.snbc.bbpf.bus.product.manager.service.impl;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.service.DictValueService;
import com.snbc.bbpf.bus.product.market.common.dto.dict.DictProductDto;
import com.snbc.bbpf.bus.product.market.common.dto.dict.DictValueDto;
import com.snbc.bbpf.bus.product.market.common.entity.DictValue;
import com.snbc.bbpf.bus.product.market.common.mapper.DictValueMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.snbc.bbpf.bus.product.manager.config.Constant.CHARGETYPE;
import static com.snbc.bbpf.bus.product.manager.config.Constant.PRODUCTCATEGORY;
import static com.snbc.bbpf.bus.product.manager.config.Constant.PRODUCTSTATUS;


/**
 * 系统字典数值
 *
 * <AUTHOR>
 */
@Service
@Transactional
@CacheConfig(cacheNames = "dictValue")
public class DictValueServiceImpl implements DictValueService {
    //日志打印
    private static final Logger LOGGER = LoggerFactory.getLogger(DictValueServiceImpl.class);

    private RedisTemplate redisTemplate;
    private DictValueMapper dictValueMapper;
    private static final Integer BEGIN = 0;
    private static final Integer END = -1;
    private static final Integer TIMEOUT = 1;
    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    @Autowired
    public void setDictValueMapper(DictValueMapper dictValueMapper) {
        this.dictValueMapper = dictValueMapper;
    }

    /**
     * 添加字典值
     *
     * @param dictValue
     * @return
     * @throws Exception
     */
    @Override
    @CacheEvict(allEntries = true)
    public int insertDictValue(DictValue dictValue) throws Exception {
        LOGGER.info("Succeeded in adding the dictionary value");
        return dictValueMapper.insertSelective(dictValue);
    }

    /**
     * 修改字典值信息
     *
     * @param dictValue
     * @return
     * @throws Exception
     */
    @Override
    @CacheEvict(allEntries = true)
    public int updateDictValue(DictValue dictValue) throws Exception {
        return dictValueMapper.updateByPrimaryKeySelective(dictValue);
    }

    /**
     * 删除字典数值信息
     *
     * @param dictValueId
     * @return
     * @throws Exception
     */
    @Override
    @CacheEvict(allEntries = true)
    public int deleteDictValue(int dictValueId) throws Exception {
        return dictValueMapper.deleteByPrimaryKey(dictValueId);
    }

    /**
     * selectByPrimary
     *
     * @param valueId
     * @return
     * @throws Exception
     */
    @Override
    public DictValue selectByPrimary(Integer valueId) throws Exception {
        return dictValueMapper.selectByPrimaryKey(valueId);
    }

    /**
     * getAll
     *
     * @return
     * @throws Exception
     */
    @Override
    public List<DictValue> getAll() throws Exception {
        return dictValueMapper.getAll();
    }

    /**
     * 根据parentId数据字典值
     *
     * @return
     * @throws Exception
     */
    @Override
    public List<DictValue> getDictValueByParentId(Integer parentId) throws Exception {
        return dictValueMapper.getDictValueByParentId(parentId);
    }

    /**
     * selectDictValueByCondition
     *
     * @param map map字典类型typeCode和字典值valueCode
     * @return
     */
    @Override
    public DictValue selectDictValueByCondition(Map<String, String> map) {
        return dictValueMapper.selectDictValueByCondition(map);
    }

    /**
     * queryDictValueByTypeCode
     *
     * @param typeCode
     * @return
     */
    @Override
    public List<DictValue> queryDictValueByTypeCode(String typeCode) {
        return dictValueMapper.queryDictValueByTypeCode(typeCode);
    }

    /**
     * getDictValueByMap
     *
     * @param map
     * @return
     */
    @Override
    public List<DictValue> getDictValueByMap(Map<String, Object> map) {
        return dictValueMapper.getDictValueByMap(map);
    }

    /**
     * getDictValueByTypeCodeAndValueCode
     *
     * @param dictTypeCode
     * @param dictValueCode
     * @return
     */
    @Override
    public String getDictValueByTypeCodeAndValueCode(String dictTypeCode, String dictValueCode) {
        DictValue dictValue = new DictValue();
        dictValue.setTypeCode(dictTypeCode);
        dictValue.setValueCode(dictValueCode);
        return dictValueMapper.getDictValueByTypeCodeAndValueCode(dictValue);
    }

    /**
     * selectDictValueByTypeCode
     * 由于从数据库查出来的顺序与放入redis的顺序可能不一样wjc1 20211202修改：存字符串
     * @param typeCode
     * @return
     */
    @Override
    public List<DictValue> selectDictValueByTypeCode(String typeCode) throws IOException {
        String dictK = "dictValueListByTypeCode-" + typeCode;
        if(redisTemplate.hasKey(dictK)){
            //如果有key那么就获取
            String redisStr=(String)redisTemplate.opsForValue().get(dictK);
            return JSON.parseArray(redisStr,DictValue.class);
        } else {
            List<DictValue> dictValues = dictValueMapper.selectDictValueByTypeCode(typeCode);
            if(!CollectionUtils.isEmpty(dictValues)){
                redisTemplate.opsForValue().set(dictK, JSON.toJSONString(dictValues));
                redisTemplate.expire(dictK, TIMEOUT, TimeUnit.HOURS);
            }
            return dictValues;

        }
    }

    @Override
    public DictProductDto dictInfoForProductList() {
        List<DictValueDto> dictValueDtoList = dictValueMapper.selelctDictInfo();
        List<DictValueDto> productCategorys = new ArrayList<>();
        List<DictValueDto> productStatus = new ArrayList<>();
        List<DictValueDto> chargeTypes = new ArrayList<>();
        dictValueDtoList.forEach(dictValueDto -> {
            if (PRODUCTCATEGORY.equals(dictValueDto.getTypeCode())) {
                productCategorys.add(dictValueDto);
            }
            if (PRODUCTSTATUS.equals(dictValueDto.getTypeCode())) {
                productStatus.add(dictValueDto);
            }
            if (CHARGETYPE.equals(dictValueDto.getTypeCode())) {
                chargeTypes.add(dictValueDto);
            }
        });
        return DictProductDto.builder().chargeTypes(chargeTypes).productCategorys(productCategorys)
                .productStatus(productStatus).build();
    }

    @Override
    public Map<String, List<DictValueDto>> getMultipleDictValues(List<String> dictTypeCodes) {
        List<DictValueDto> dictValueDtoList = dictValueMapper.getMultipleDictValues(dictTypeCodes);
        return dictValueDtoList.stream().collect(Collectors.groupingBy(DictValueDto::getTypeCode));
    }
}
