/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.market.common.vo.productex;

import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderPageQueryBase;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR> LiangJb
 * @ClassName : ProductOrderPageQuery
 * @Description : 服务订单查询入参
 * purchaseNo	查询条件	String	订单号	否	1111
 * shopName	查询条件	String	商户名称	是	1983
 * payType	查询条件	int	支付方式	是	全部传-1
 * purchaseStatus	查询条件	int	订单状态	是	全部传-1
 * startTime	查询条件	string	支付开始时间	否	2020-01-01
 * endTime	查询条件	string	支付结束时间	否	2020-01-02
 * <p>
 * <p>
 * productName	查询条件	String	服务名称	否	物联网卡
 * productType	查询条件	int	服务类型	是	全部传-1
 * chargeType	查询条件	int	收费方式	是	全部传-1
 * tenantId	查询条件	String		是	租户ID
 * <p>
 * <p>
 * <p>
 * pageNum	查询条件	string	页数	是	1
 * pageSize	查询条件	string	每页行数	是	10
 * @Date: 2020-08-12 15:51
 */



@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T06:49:02.983Z")
@Data
public class ProductOrderPageQueryEx extends ProductOrderPageQueryBase {
    
    private String payType = "-1";
    
    private String purchaseStatus = "-1";
    
    private String productType;
    
    private String chargeType;
}
