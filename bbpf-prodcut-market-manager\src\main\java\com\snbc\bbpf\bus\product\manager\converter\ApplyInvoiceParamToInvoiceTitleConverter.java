package com.snbc.bbpf.bus.product.manager.converter;

import com.snbc.bbpf.bus.product.market.common.entity.InvoiceTitle;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ApplyInvoiceParam;
import com.snbc.bbpf.commons.objs.IConvert;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * ApplyInvoiceParam 到 InvoiceTitle 的转换器
 */
@Mapper
public interface ApplyInvoiceParamToInvoiceTitleConverter extends IConvert<ApplyInvoiceParam, InvoiceTitle> {
    ApplyInvoiceParamToInvoiceTitleConverter INSTANCE = Mappers.getMapper(ApplyInvoiceParamToInvoiceTitleConverter.class);
    
    @Mappings({
        @Mapping(source = "invoiceTitle", target = "companyName")
    })
    @Override
    InvoiceTitle to(ApplyInvoiceParam source);
    
    @Mappings({
        @Mapping(source = "companyName", target = "invoiceTitle")
    })
    @Override
    ApplyInvoiceParam from(InvoiceTitle source);
}