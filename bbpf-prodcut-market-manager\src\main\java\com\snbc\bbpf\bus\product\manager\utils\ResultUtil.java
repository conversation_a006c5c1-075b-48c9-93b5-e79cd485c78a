/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.utils;


import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import com.snbc.bbpf.bus.product.manager.exception.Errors;

import java.text.MessageFormat;
import java.util.List;

/***
 * @Description:    私有化工具类
 * @Author:         wangsong
 * @CreateDate:     2021/5/17 16:07
 * @UpdateDate:     2021/5/17 16:07
 * @return :        null
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public final class ResultUtil {

    private ResultUtil() {
        throw new IllegalStateException("ResultUtil class");
    }

    /***
      * @Description:    成功返回信息
      * @Author:         wangsong
      * @CreateDate:     2021/5/17 16:08
      * @UpdateDate:     2021/5/17 16:08
      * @return :        com.snbc.bbpf.component.config.CallResponse
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static CallResponse success() {
        CallResponse result = new CallResponse();
        result.setCode(Errors.SUCCESS.getCode());
        result.setMessage(Errors.SUCCESS.getMessage());
        return result;
    }

    /***
      * @Description:    错位返回信息
      * @Author:         wangsong
      * @param :         code
      * @param :         msg
      * @CreateDate:     2021/5/17 16:08
      * @UpdateDate:     2021/5/17 16:08
      * @return :        com.snbc.bbpf.component.config.CallResponse
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static CallResponse error(String code, String msg) {
        CallResponse result = new CallResponse();
        result.setCode(code);
        result.setMessage(msg);
        return result;
    }

    /**
     * 错误
     * @param msg 错误信息
     * @return CallResponse
     */
    public static CallResponse error(String msg) {
        return error(Errors.FAILED.getCode(), msg);
    }


    /**
     * @description: 错误
     * @param errors 错误信息
     * @return: com.snbc.bbpf.component.config.CallResponse
     * @author: liuyi
     * @time: 2021/5/25 16:06
     */
    public static CallResponse error(Errors errors){
        return error(errors.getCode(),errors.getMessage());
    }

    /**
     * @description: 错误
     * @param errors 错误信息
     * @param value 占位服值
     * @return: com.snbc.bbpf.component.config.CallResponse
     * @author: liuyi
     * @time: 2021/5/25 16:07
     */
    public static CallResponse error(Errors errors, String value){
        return error(errors.getCode(), MessageFormat.format(errors.getMessage(), value));
    }

    public static Integer getListSize(List list){
        if (null != list){
            return list.size();
        } else {
            return null;
        }
    }
}
