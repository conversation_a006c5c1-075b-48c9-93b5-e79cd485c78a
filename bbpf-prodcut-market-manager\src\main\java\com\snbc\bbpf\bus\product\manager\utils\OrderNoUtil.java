package com.snbc.bbpf.bus.product.manager.utils;

import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 生成订单号
 * <AUTHOR>
 */
@Component
public class OrderNoUtil {
    /**
     * 保存24小时
     */
    public static final int LIVE_TIME = 24;
    public static final int ZERO = 0;
    @Autowired
    private ProductPurchaseMapper productPurchaseMapper;
    @Autowired
    private RedisUtil redisUtil;
    /**
     * 生成不重复的订单号
     *
     * @return
     */
    public String getOrderNo() {
        //生成时间格式
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dateString = LocalDate.now().format(df);
        Long incr = redisUtil.incr("productPurchase" + dateString, LIVE_TIME);
        //如果取出数据为零，接着取一次
        if (incr == ZERO) {
            incr = redisUtil.incr("productPurchase" + dateString, LIVE_TIME);
        }
        DecimalFormat decimalFormat = new DecimalFormat("000000");
        String order = "PD" + dateString + decimalFormat.format(incr);
        ProductPurchase productPurchase = productPurchaseMapper.selectByPurchaseNo(order);
        //如果数据库中不存在，直接返回订单号，如果已经存在递归取
        if (null == productPurchase) {
            return order;
        } else {
            return getOrderNo();
        }
    }
}
