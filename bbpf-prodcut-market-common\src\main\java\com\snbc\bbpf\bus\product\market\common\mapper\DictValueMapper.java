package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.dto.dict.DictValueDto;
import com.snbc.bbpf.bus.product.market.common.entity.DictValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 字典值持久层接口
 */
@Mapper
public interface DictValueMapper {
    int deleteByPrimaryKey(Integer valueId);

    int insert(DictValue dictValue);

    int insertSelective(DictValue dictValue);

    DictValue selectByPrimaryKey(Integer valueId);

    int updateByPrimaryKeySelective(DictValue dictValue);

    int updateByPrimaryKey(DictValue dictValue);

    /**
     * 获取所有的字典值
     *
     * @return
     */
    List<DictValue> getAll();

    /**
     * 根据map的值查询dictValue  typeCode valueCode
     *
     * @param map
     * @return
     */
    DictValue selectDictValueByCondition(Map<String, String> map);

    /**
     * 根据typecode查询dictvalueList
     *
     * @param typeCode
     * @return
     */
    List<DictValue> queryDictValueByTypeCode(String typeCode);

    /**
     * 根据map获取dictValueList
     *
     * @param map
     * @return
     */
    List<DictValue> getDictValueByMap(Map<String, Object> map);

    /**
     * 根据parnetId获取dictValueList
     *
     * @param parentId
     * @return
     */
    List<DictValue> getDictValueByParentId(@Param("parentId") Integer parentId);
    /**
     * 根据dictTypeCode 和dictValueCode 获取dictValueNameStr
     *
     * @param dictValue
     * @return
     */
    String getDictValueByTypeCodeAndValueCode(DictValue dictValue);

    /**
     * 根据typecode查询dictvalueList
     *
     * @param typeCode
     * @return
     */
    List<DictValue> selectDictValueByTypeCode(String typeCode);

    List<DictValueDto> selelctDictInfo();

    List<DictValueDto> getMultipleDictValues(List<String> dictTypeCodes);
}
