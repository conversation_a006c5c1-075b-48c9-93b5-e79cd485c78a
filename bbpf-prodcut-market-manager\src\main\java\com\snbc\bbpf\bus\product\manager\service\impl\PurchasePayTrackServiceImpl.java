package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.service.PurchasePayTrackService;
import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchasePayTrackMapper;
import org.apache.commons.lang.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: RemittanceServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 支付流水
 * @CreateDate: 2020/8/24 15:21
 * @UpdateDate: 2020/8/24 15:21
 */
@Service
@Transactional
public class PurchasePayTrackServiceImpl implements PurchasePayTrackService {
    public static final int SUCCESS = 1;
    public static final int EIGHT_NUM = 8;

    /**
     * 流水mapper
     */
    @Autowired
    private PurchasePayTrackMapper purchasePayTrackMapper;

    //日志记录
    private static final Logger LOGGER = LoggerFactory.getLogger(PurchasePayTrackServiceImpl.class);

    /**
     * 插入
     * @param record
     * @return
     */
    @Override
    public int insert(PurchasePayTrack record) {
        return  purchasePayTrackMapper.insert(record);
    }

    /**
     * 插入
     * @param record
     * @return
     */
    @Override
    public int insertSelective(PurchasePayTrack record) {
        return purchasePayTrackMapper.insertSelective(record);
    }

    /**
     * 查询
     * @param purchasePayTrackId
     * @return
     */
    @Override
    public PurchasePayTrack selectByPrimaryKey(String purchasePayTrackId) {
        return purchasePayTrackMapper.selectByPrimaryKey(purchasePayTrackId);
    }

    /**
     * 条件查询
     * @param record
     * @return
     */
    @Override
    public List<PurchasePayTrack> selectPayTracks(PurchasePayTrack record) {
        return purchasePayTrackMapper.selectPayTracks(record);
    }

    /**
     * 修改
     * @param record
     * @return
     */
    @Override
    public int updateByPrimaryKeySelective(PurchasePayTrack record) {
        return purchasePayTrackMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 废弃旧流水 生成新流水
     * @param oldTrack
     * @param payType
     * @return
     */
    @Override
    public String cancelOldAndNew(PurchasePayTrack oldTrack,String payType) {
        PurchasePayTrack track = new PurchasePayTrack();
        track.setPurchasePayTrackId(oldTrack.getPurchasePayTrackId());
        track.setPayStatus(Constant.PAY_TRACK_CLOSE);
        track.setUpdateTime(LocalDateTime.now());
        purchasePayTrackMapper.updateByPrimaryKeySelective(track);

        return createNewTrack(oldTrack.getPurchaseNo(),payType);
    }

    /**
     * 生成新流水
     * @param purchaseNo
     * @param payType
     * @return
     */
    @Override
    public String createNewTrack(String purchaseNo,String payType) {
        //流水号 订单号+6位随机数字 产品定
        String newTrackNo = purchaseNo+ RandomStringUtils.random(EIGHT_NUM,false,true);
        PurchasePayTrack insert = new PurchasePayTrack();
        insert.setPurchasePayTrackId(UUID.randomUUID().toString());
        insert.setPurchaseNo(purchaseNo);
        insert.setPayStatus(Constant.PAY_TRACK_WAIT);
        insert.setPayTrackTime(LocalDateTime.now());
        insert.setPayTrackNo(newTrackNo);
        insert.setPayType(payType);
        purchasePayTrackMapper.insertSelective(insert);
        return newTrackNo;
    }
}
