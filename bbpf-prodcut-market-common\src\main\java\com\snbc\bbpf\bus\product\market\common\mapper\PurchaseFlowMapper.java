package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.PurchaseFlow;
import org.apache.ibatis.annotations.Mapper;
/**
 * @author: LiangJB
 * 功能描述: <br>
 * 购买生命周期表表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface PurchaseFlowMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 删除数据
     * @date: 2020/8/12 13:21
     */
    int deleteByPrimaryKey(String flowId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(PurchaseFlow purchaseFlow);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(PurchaseFlow purchaseFlow);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    PurchaseFlow selectByPrimaryKey(String flowId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 修改订单编号
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKeySelective(PurchaseFlow purchaseFlow);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 修改关联关系
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKey(PurchaseFlow purchaseFlow);
}
