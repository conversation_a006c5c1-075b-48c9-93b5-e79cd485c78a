package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;

import java.util.List;

/**
 * @author: LiangJB
 * 功能描述: <br>
 * 线下支付表数据解析
 * @date: 2020/8/12 13:21
 */
public interface PurchasePayTrackService {

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(PurchasePayTrack record);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(PurchasePayTrack record);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    PurchasePayTrack selectByPrimaryKey(String purchasePayTrackId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    List<PurchasePayTrack> selectPayTracks(PurchasePayTrack record);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKeySelective(PurchasePayTrack record);


    String cancelOldAndNew(PurchasePayTrack oldTrack,String payType);

    String createNewTrack(String purchaseNo,String payType);
}
