package com.snbc.bbpf.bus.product.manager.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductAvailablePageDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductLOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductShopOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceBuyPDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceShopOrderDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductAvailablePageQueryBase;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderVemsPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductShopVemsPageQuery;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: ProductOrderService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 负责产品订单查询先关的接口
 * @Author: LiangJb
 * @CreateDate: 2020/9/28 15:59
 */
public interface ProductOrderService {

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 1.3.3. 统计服务订单记录
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: VemsProductOrderDto
     */
    PageInfo<ProductShopOrderDto> productOrderListWithPage(ProductOrderPageQuery productOrderPageQuery);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 1.6.1. 服务订单记录
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: VemsProductOrderDto
     */
    PageInfo<ProductOrderDto> serviceOrderListWithPage(ProductOrderPageQuery productOrderPageQuery);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 2.3.1. 服务订单列表
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: VemsProductOrderDto
     */
    PageInfo<ServiceShopOrderDto> rrqserviceOrderListWithPage(ProductOrderPageQuery productOrderPageQuery);
    PageInfo<ProductAvailablePageDto> seviceAvailableListWithPage(ProductAvailablePageQueryBase productAvailablePageQueryBase);

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 获取订单详情
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: VemsProductOrderDto
     */
    ProductOrderDetailDto orderDetail(String purchaseNo);

    ProductAvailablePageDto availableDetail(String availableId,String tenantId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 1.6.2.2. 服务订单设备详细记录（修改）
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: VemsProductOrderDto
     */
    PageInfo<ProductLOrderDto> seviceVemsOrderListWithPage(ProductOrderVemsPageQuery productOrderVemsPageQuery);

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 2.5.2.1. 设备标准列表
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: VemsProductOrderDto
     */
    PageInfo<ServiceBuyPDto> sevicesShopListWithPage(ProductShopVemsPageQuery productOrderVemsPageQuery);
}
