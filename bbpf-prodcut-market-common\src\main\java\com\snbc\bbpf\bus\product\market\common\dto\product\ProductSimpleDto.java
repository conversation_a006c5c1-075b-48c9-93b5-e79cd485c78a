package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/*
* @author: liangJB
     * 功能描述: <br>
     * 产品编辑类
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
productId	String	是	服务编号
productName	String	是	服务名称
productCategory	int	是	服务类目编号
productCategoryName	String	是	服务类目名称
productType	int	是	服务类型编号
productTypeName	String	是	服务类型名称
chargeType	int	是	收费方式编号
chargeTypeName	String	是	收费方式名称

* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSimpleDto extends ProductDto {
    
    private Integer productCategory;

    private Integer productType;
    
    private Integer chargeType;

    private List<PermissionLevelDto> permissionInfo;



}
