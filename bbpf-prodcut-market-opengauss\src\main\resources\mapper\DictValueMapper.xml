<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.DictValueMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.DictValue" >
    <id column="value_id" property="valueId" jdbcType="INTEGER" />
    <result column="type_code" property="typeCode" jdbcType="VARCHAR" />
    <result column="value_name" property="valueName" jdbcType="VARCHAR" />
    <result column="value_code" property="valueCode" jdbcType="VARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="value_desc" property="valueDesc" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    value_id, type_code, value_name, value_code, parent_id, value_desc
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from t_dict_value
    where value_id = #{valueId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from t_dict_value
    where value_id = #{valueId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.DictValue" >
    insert into t_dict_value (value_id, type_code, value_name,
      value_code, parent_id, value_desc
      )
    values (#{valueId,jdbcType=INTEGER}, #{typeCode,jdbcType=VARCHAR}, #{valueName,jdbcType=VARCHAR},
      #{valueCode,jdbcType=VARCHAR}, #{parentId,jdbcType=INTEGER}, #{valueDesc,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.DictValue" >
    insert into t_dict_value
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="valueId != null" >
        value_id,
      </if>
      <if test="typeCode != null" >
        type_code,
      </if>
      <if test="valueName != null" >
        value_name,
      </if>
      <if test="valueCode != null" >
        value_code,
      </if>
      <if test="parentId != null" >
        parent_id,
      </if>
      <if test="valueDesc != null" >
        value_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="valueId != null" >
        #{valueId,jdbcType=INTEGER},
      </if>
      <if test="typeCode != null" >
        #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="valueName != null" >
        #{valueName,jdbcType=VARCHAR},
      </if>
      <if test="valueCode != null" >
        #{valueCode,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="valueDesc != null" >
        #{valueDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.DictValue" >
    update t_dict_value
    <set >
      <if test="typeCode != null" >
        type_code = #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="valueName != null" >
        value_name = #{valueName,jdbcType=VARCHAR},
      </if>
      <if test="valueCode != null" >
        value_code = #{valueCode,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="valueDesc != null" >
        value_desc = #{valueDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where value_id = #{valueId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.bus.product.market.common.entity.DictValue" >
    update t_dict_value
    set type_code = #{typeCode,jdbcType=VARCHAR},
      value_name = #{valueName,jdbcType=VARCHAR},
      value_code = #{valueCode,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=INTEGER},
      value_desc = #{valueDesc,jdbcType=VARCHAR}
    where value_id = #{valueId,jdbcType=INTEGER}
  </update>
  <select id="getAll" resultMap="BaseResultMap">
  	select
    <include refid="Base_Column_List" />
    from t_dict_value
  </select>
   <select id="selectDictValueByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
  	select
    <include refid="Base_Column_List" />
    from t_dict_value
    where type_code = #{typeCode,jdbcType=VARCHAR}
    and value_code = #{valueCode,jdbcType=VARCHAR}
  </select>
  <select id="queryDictValueByTypeCode" resultMap="BaseResultMap" parameterType="java.lang.String">
  	select
    <include refid="Base_Column_List" />
    from t_dict_value where 1=1
    <if test="_parameter != null" >
      AND type_code = #{_parameter,jdbcType=VARCHAR}
    </if>
    order by value_id asc
  </select>
  <select id="getDictValueByParentId" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_dict_value where 1=1
    <if test="parentId != null" >
      AND parent_id = #{parentId,jdbcType=VARCHAR}
    </if>
    order by value_id asc
  </select>
  <select id="getDictValueByMap" resultMap="BaseResultMap" parameterType="map" >
  	select
    <include refid="Base_Column_List" />
    from t_dict_value where 1=1
    <if test="typeCode != null" >
      AND type_code = #{typeCode,jdbcType=VARCHAR}
    </if>
    <if test="valueCode != null">
			AND value_code in
			<foreach item="item" index="index" collection="valueCode"
                         open="(" separator="," close=")">
                        #{item}
            </foreach>
	</if>
  </select>
  <select id="getDictValueByTypeCodeAndValueCode" parameterType="com.snbc.bbpf.bus.product.market.common.entity.DictValue" resultType="java.lang.String">
  	select
    value_name
    from t_dict_value where 1=1
    <if test="typeCode != null" >
      AND type_code = #{typeCode,jdbcType=VARCHAR}
    </if>
    <if test="valueCode != null" >
      AND value_code = #{valueCode,jdbcType=VARCHAR}
    </if>
  </select>
   <select id="selectDictValueByTypeCode" resultType="com.snbc.bbpf.bus.product.market.common.entity.DictValue" parameterType="java.lang.String">
  	select d.value_id as valueId, d.type_code as typeCode, d.value_name as valueName,
  	d.value_code as valueCode, d.parent_id as parentId, d.value_desc as valueDesc, dv.value_name as parentName
    from t_dict_value d LEFT JOIN t_dict_value dv on d.parent_id = dv.value_id where 1=1
    <if test="_parameter != null" >
      AND d.type_code = #{_parameter,jdbcType=VARCHAR}
    </if>
    order by d.value_id asc
  </select>
    <select id="selelctDictInfo" resultType="com.snbc.bbpf.bus.product.market.common.dto.dict.DictValueDto">
      SELECT
          t1.value_code valueCode,
          t1.value_name valueName,
          t2.type_code typeCode
      FROM
          t_dict_value t1
      LEFT JOIN t_dict_type t2 ON t1.type_code = t2.type_code
      WHERE
          t2.type_code = 'product_category'
      OR t2.type_code = 'product_type'
      OR t2.type_code = 'product_status'
      OR t2.type_code = 'charge_type'
    </select>

  <select id="getMultipleDictValues" resultType="com.snbc.bbpf.bus.product.market.common.dto.dict.DictValueDto">
    SELECT
    t1.value_code valueCode,
    t1.value_name valueName,
    t2.type_code typeCode
    FROM
    t_dict_value t1
    LEFT JOIN t_dict_type t2 ON t1.type_code = t2.type_code
    WHERE 1=1
    <if test="dictTypeCodes != null and dictTypeCodes.size() != 0">
      AND t1.type_code in
      <foreach item="item" index="index" collection="dictTypeCodes"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>
</mapper>
