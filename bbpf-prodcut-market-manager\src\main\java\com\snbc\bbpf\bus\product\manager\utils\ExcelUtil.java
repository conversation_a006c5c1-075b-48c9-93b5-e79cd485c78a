package com.snbc.bbpf.bus.product.manager.utils;

import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.utils
 * @ClassName: ExcelUtil
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/8/26 10:41
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/26 10:41
 */
public class ExcelUtil {
    /**
     * @Author: yh
     * @Description: excel导出轮子
     * @Date: Created in 14:29  2018/11/27.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelUtil.class);
    //设置列的公式
    private static final Integer TWOHUNDREDFIFTYSIX = 256;
    private static final Integer THIRTYTHOUSAND = 30000;
    private static final short TWELVE = 12;

    /**
     * xlsx方式
     *
     * @param response
     * @param fileName
     * @param titleColumn
     * @param titleName
     * @param titleSize
     * @param dataList
     */
    public void writeBigExcel(HttpServletResponse response, String fileName, String[] titleColumn,
                              String[] titleName, int[] titleSize, List<?> dataList,String[] sumData) throws BusinessException {
        try (HSSFWorkbook swbook = new HSSFWorkbook()) {
            OutputStream out = response.getOutputStream();
            String nowTime = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
            String lastFileName = fileName + nowTime + ".xls";
            response.setContentType("application/msexcel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(lastFileName, "UTF-8"));
            int k = 0;
            int rowNum=0;
            Sheet sheet = swbook.createSheet(fileName + (k + 1));
            Row titleNameRow;
            if(sumData!=null){
                rowNum=1;
                Row sumRow=sheet.createRow(0);
                for (int index=0;index<sumData.length;index++){
                    Cell cell =sumRow.createCell(index);
                    cell.setCellValue(sumData[index]);
                }
                //写入excel的表头
                titleNameRow = sheet.createRow(1);
            } else {
                titleNameRow = swbook.getSheet(fileName + (k + 1)).createRow(0);
            }
            rowNum += 1;
            HSSFCellStyle style = cellStyle(swbook);
            //设置图案背景色
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
            //字体加粗
            style.getFont(swbook).setBold(true);
            for (int i = 0; i < titleName.length; i++) {
                sheet.setColumnWidth(i, titleSize[i] * TWOHUNDREDFIFTYSIX);
                //设置宽度
                Cell cell = titleNameRow.createCell(i);
                cell.setCellValue(titleName[i]);
                cell.setCellStyle(style);
            }
            //写入到excel中
            createExcel(dataList, titleColumn, fileName, titleName, titleSize, swbook,rowNum);
            swbook.write(out);
        } catch (Exception e) {
            LOGGER.error("Exporting Excel Exceptions", e);
            throw new BusinessException(Errors.WRITEEXCELERROR.getMessage(),Errors.WRITEEXCELERROR.getCode(),null);
        }
    }

    private static HSSFCellStyle cellStyle(HSSFWorkbook swbook){
        HSSFCellStyle style = swbook.createCellStyle();
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        HSSFFont font = swbook.createFont();
        //设置字号
        font.setFontHeightInPoints(TWELVE);
        //设置字体名称
        font.setFontName("宋体");
        style.setFont(font);
        return style;
    }

    /***
     * @Description: 创建excel
     * @Author: wangsong
     * @param :         dataList
     * @param :         titleColumn
     * @param :         fileName
     * @param :         titleName
     * @param :         titleSize
     * @param :         workbook
     * @CreateDate: 2020/8/27 14:27
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 14:27
     * @return :        void
     */
    private static void createExcel(List<?> dataList, String[] titleColumn, String fileName,
                             String[] titleName, int[] titleSize, HSSFWorkbook workbook,int num)
            throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        int rowIndex;
        int k = 0;
        if (!dataList.isEmpty() && titleColumn.length > 0) {
            HSSFCellStyle style = cellStyle(workbook);
            for (int index = 0; index < dataList.size(); index++) {
                bigDataProcess(index, workbook, fileName, titleName, titleSize);
                if (index < THIRTYTHOUSAND) {
                    rowIndex = index + num;
                } else {
                    rowIndex = index - THIRTYTHOUSAND * ((index) / THIRTYTHOUSAND) + num;
                }
                Object obj = dataList.get(index);
                Class clazz = obj.getClass();
                Row dataRow = workbook.getSheet(fileName + (k + 1)).createRow(rowIndex);
                createColumn(titleColumn, clazz, obj, dataRow,style);
            }
        }
    }

    /***
     * @Description: 写入excel列数据
     * @Author: wangsong
     * @param :         titleColumn
     * @param :         clazz
     * @param :         obj
     * @param :         dataRow
     * @param :         rowIndex
     * @param style
     * @CreateDate: 2020/8/27 14:28
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 14:28
     * @return :        void
     */
    private static void createColumn(String[] titleColumn, Class clazz, Object obj, Row dataRow, HSSFCellStyle style)
            throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        for (int columnIndex = 0; columnIndex < titleColumn.length; columnIndex++) {
            String title = titleColumn[columnIndex].trim();
            if (!"".equals(title)) {
                // 获取返回类型// 使其首字母大写
                String upperCaseTitle = Character.toUpperCase(title.charAt(0)) + title.substring(1);
                String methodName = "get" + upperCaseTitle;
                Method method = clazz.getDeclaredMethod(methodName);
                method.getReturnType().getName();
                Object object = method.invoke(obj);
                String data = method.invoke(obj) == null ? "" : object.toString();
                Cell cell = dataRow.createCell(columnIndex);
                cell.setCellValue(data);
                cell.setCellStyle(style);
            }
        }
    }

    /***
     * @Description: 每个sheet3万数据处理
     * @Author: wangsong
     * @param :         index
     * @param :         workbook
     * @param :         fileName
     * @param :         titleName
     * @param :         titleSize
     * @CreateDate: 2020/8/27 14:28
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 14:28
     * @return :        void
     */
    private static void bigDataProcess(int index, HSSFWorkbook workbook, String fileName, String[] titleName, int[] titleSize) {
        //每个sheet3W条数据
        int k = 0;
        if (index != 0 && (index) % THIRTYTHOUSAND == 0) {
            k = k + 1;
            Sheet sheet = workbook.createSheet(fileName + (k + 1));
            //写入excel的表头
            Row titleNameRow = workbook.getSheet(fileName + (k + 1)).createRow(0);
            for (int i = 0; i < titleName.length; i++) {
                sheet.setColumnWidth(i, titleSize[i] * TWOHUNDREDFIFTYSIX);    //设置宽度
                Cell cell = titleNameRow.createCell(i);
                cell.setCellValue(titleName[i]);
            }
        }
    }
}
