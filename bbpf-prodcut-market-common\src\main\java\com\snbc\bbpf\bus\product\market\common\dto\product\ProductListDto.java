package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/*
* @author: liangJB
     * 功能描述: <br>
     * 产品状态类
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
productId	String	是	服务编号
productName	String	是	服务名称
productCategoryName	String	是	服务类目名称
productTypeName	String	是	服务类型名称
chargeTypeName	String	是	收费方式名称
productStatus	int	是	服务状态0：待发布 1：已发布
createTime	Date	是	添加时间
productBrief	String	是	服务简介
productImage	String	是	服务图片

* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductListDto extends ProductStatusDto {
    private String productBrief;
    private String productImage;
    private String gradeUnit;
}
