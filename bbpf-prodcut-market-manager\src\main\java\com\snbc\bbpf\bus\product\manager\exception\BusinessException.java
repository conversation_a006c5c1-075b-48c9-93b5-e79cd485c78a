/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.manager.exception;

/**
 * @className BusinessException
 * @description 业务异常类
 * <AUTHOR> newbeiyang.com
 * @date 2019年11月13日 上午11:15:05
 * @version V1.0
 */
@SuppressWarnings("serial")
public class BusinessException extends RuntimeException {
    /**
     *
     */
    // 异常编码
    private final String code;
    // 异常内容
    private final Exception data;

    public BusinessException(String msg, String code, Exception data) {
        super(msg);
        this.code = code;
        this.data = data;
    }
    public BusinessException(IResponseEnum responseEnum,String message) {
        super(message);
        throw new BusinessException(message,responseEnum.getCode());
    }
    public BusinessException(String msg, String code) {
        super(msg);
        this.code = code;
        data = null;
    }
    public String getCode() {
        return code;
    }

    public Object getData() {
        return data;
    }


}
