package com.snbc.bbpf.bus.product.manager.config.payconfig;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.snbc.bbpf.bus.product.manager.config.CommonResp;

import jakarta.validation.Valid;
import java.util.Objects;

/**
 * 功能描述: <br>
 * 〈支付宝配置响应信息〉
 *
 * @param: null
 * @return:
 * @exception
 * @author: gs
 * @date: 2019-08-08 18:39
 */

@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2017-09-08T08:14:27.030Z")

public class AlipayConfigInfoResp {
    @JsonProperty("header")
    private CommonResp header;

    @JsonProperty("body")
    private AlipayConfigDto body;

    public AlipayConfigInfoResp header(CommonResp header) {
        this.header = header;
        return this;
    }

    /**
     * 响应信息头
     *
     * @return header
     **/
    

    @Valid

    public CommonResp getHeader() {
        return header;
    }

    public void setHeader(CommonResp header) {
        this.header = header;
    }

    public AlipayConfigInfoResp body(AlipayConfigDto body) {
        this.body = body;
        return this;
    }

    /**
     * 支付宝配置信息
     *
     * @return body
     **/
    

    @Valid

    public AlipayConfigDto getBody() {
        return body;
    }

    public void setBody(AlipayConfigDto body) {
        this.body = body;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AlipayConfigInfoResp alipayConfigInfoResp = (AlipayConfigInfoResp) o;
        return Objects.equals(this.header, alipayConfigInfoResp.header) &&
                Objects.equals(this.body, alipayConfigInfoResp.body);
    }

    @Override
    public int hashCode() {
        return Objects.hash(header, body);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class AlipayConfigInfoResp {\n");

        sb.append("    header: ").append(toIndentedString(header)).append("\n");
        sb.append("    body: ").append(toIndentedString(body)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

