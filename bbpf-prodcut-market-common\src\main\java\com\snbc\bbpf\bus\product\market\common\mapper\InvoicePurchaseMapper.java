package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.InvoicePurchase;
import org.apache.ibatis.annotations.Mapper;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.mapper
 * @ClassName: InvoicePurchaseMapper
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票订单关系持久层
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:21
 */
@Mapper
public interface InvoicePurchaseMapper {
    int deleteByPrimaryKey(String invoicePurchaseId);

    int insert(InvoicePurchase invoicePurchase);

    int insertSelective(InvoicePurchase invoicePurchase);

    InvoicePurchase selectByPrimaryKey(String invoicePurchaseId);

    int updateByPrimaryKeySelective(InvoicePurchase invoicePurchase);

    int updateByPrimaryKey(InvoicePurchase invoicePurchase);
}
