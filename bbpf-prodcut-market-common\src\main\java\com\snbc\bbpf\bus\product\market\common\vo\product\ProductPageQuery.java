/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.market.common.vo.product;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR> LiangJb
 * @ClassName : ProductPageQuery
 * @Description : 参数服务列表
 * productName	查询条件	String	服务名称	否	物联网卡
 * pageNum	查询条件	string	页数	是	1
 * pageSize	查询条件	string	每页行数	是	10
 * startTime	查询条件	string	开始时间	否	2020-01-01
 * endTime	查询条件	string	结束时间	否	2020-01-02
 * productType	查询条件	String	服务类型	否	全部为-1
 * gradeTypeNo	查询条件	int	收费方式	否 	全部传-1

 * @Date: 2020-08-12 15:51
 */



@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T06:49:02.983Z")
@Data
public class ProductPageQuery extends  ProductPageQueryBase{
    
    private Integer productType = 0;
    
    private Integer gradeTypeNo = 0;
    
    private String productCode;
    
    private Integer productCategory = 0;
    
    private String createStartTime;
    
    private String createEndTime;
    
    private String publishStartTime;
    
    private String publishEndTime;
}
