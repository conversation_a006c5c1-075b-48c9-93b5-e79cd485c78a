/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: ProductOrderExServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 服务设备使用时长相关接口实现
 * @Author: LiangJb
 * @CreateDate: 2020/9/28 15:59
 */
package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.AuthorityService;
import com.snbc.bbpf.bus.product.manager.service.ProductOrderExService;
import com.snbc.bbpf.bus.product.manager.service.RemittanceService;
import com.snbc.bbpf.bus.product.manager.utils.OrderNoUtil;
import com.snbc.bbpf.bus.product.manager.utils.OrderServiceUtils;
import com.snbc.bbpf.bus.product.manager.utils.ProductQueryUtil;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductDRPAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductDRPCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsUseTimeDto;
import com.snbc.bbpf.bus.product.market.common.entity.DictValue;
import com.snbc.bbpf.bus.product.market.common.entity.ProductAvailable;
import com.snbc.bbpf.bus.product.market.common.entity.ProductDetail;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.mapper.DictValueMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductAvailableMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductDetailMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductOrderMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import com.snbc.bbpf.bus.product.market.common.vo.product.VemsOrderDayQueryBase;
import com.snbc.bbpf.bus.product.market.common.vo.product.VemsOrderPageQueryBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;


@Service
public class ProductOrderExServiceImpl implements ProductOrderExService {

    @Autowired
    private ProductOrderMapper productOrderMapper;
    @Autowired
    private RemittanceService remittanceService;
    @Autowired
    private OrderServiceUtils orderServiceUtils;
    @Autowired
    private ProductDetailMapper productDetailMapper;
    @Autowired
    private ProductPurchaseMapper productPurchaseMapper;
    @Autowired
    private DictValueMapper dictValueMapper;
    @Autowired
    private ProductAvailableMapper productAvailableMapper;
    @Autowired
    private OrderNoUtil orderNoUtil;
    @Autowired
    private AuthorityService authorityService;
    @Value("${product.present.price}")
    private BigDecimal productPrice;
    private static final BigDecimal PRODUCT_PRICE =BigDecimal.valueOf(0.0);
    private static final int PAY_TYPE = 98;
    private static final int ZERO = 0;
    private static final int ONE = 1;
    private static final int TWO = 2;
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 2.5.2.1. //获取已购设备订单列表
     * dateType	查询条件	int	时间查询方式	是	1为默认查询 2为已到期3 为不足一个月
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: VemsProductOrderDto
     */
    @Override
    public PageInfo<VemsProductOrderDto> getOrderLstByVemsId(VemsOrderPageQueryBase vemPageResp){
        PageHelper.startPage(vemPageResp.getPageNum(), vemPageResp.getPageSize());
        List<VemsProductOrderDto> productList = productOrderMapper.getOrderLstByVemsId(vemPageResp);
        return  new PageInfo<>(productList);
    }


    /**
     * @author: OY
     * 单个售货机添加时长
     *
     * @param vemsUseTimeDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public String addServiceTime(VemsUseTimeDto vemsUseTimeDto) throws BusinessException {
        try {
            ProductPurchase productPurchase = getProductPurchase(vemsUseTimeDto);
            ProductDetail productDetail = getProductDetail(vemsUseTimeDto, productPurchase.getProductPurchaseId());
            productDetailMapper.insert(productDetail);
            productPurchaseMapper.insert(productPurchase);
            //更新可用表
            orderServiceUtils.updateProductAvailable(productPurchase.getPurchaseNo());
            //授权
            authorityService.authority(null,productPurchase.getPurchaseNo(),vemsUseTimeDto.getTenantId());
            remittanceService.updateProductStatusOnRedis(productPurchase.getProductId(),productPurchase.getPurchaseNo());
            return productPurchase.getPurchaseNo();
        } catch (Exception ex) {
            throw new BusinessException(Errors.USERTIME_ERROR.getMessage(), Errors.USERTIME_ERROR.getCode(), ex);
        }
    }

    /**
     * @author: OY
     * 批量添加售货机时长
     *
     * @param vemsList
     * @return
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public List<String> addServiceTimeBatch(List<VemsUseTimeDto> vemsList) throws BusinessException {
        try {
            //按租户id分组生成订单
            Map<String, List<VemsUseTimeDto>> group = vemsList.stream().collect(Collectors.groupingBy(VemsUseTimeDto::getTenantId));
            List<String> resultList = new ArrayList<>();
            group.forEach((tenantId, vemsUseTimeList) -> {
                ProductPurchase productPurchase = getProductPurchase(vemsUseTimeList.get(0));
                productPurchase.setProductQuantity(vemsUseTimeList.size());
                BigDecimal count=BigDecimal.valueOf(vemsUseTimeList.size());
                productPurchase.setDiscountAmount(count.multiply(productPurchase.getDiscountAmount()));
                productPurchase.setPurchaseAmount(count.multiply(productPurchase.getPurchaseAmount()));
                resultList.add(productPurchase.getPurchaseNo());
                for (VemsUseTimeDto vems : vemsUseTimeList) {
                    ProductDetail productDetail = getProductDetail(vems, productPurchase.getProductPurchaseId());
                    productDetailMapper.insert(productDetail);
                }
                productPurchaseMapper.insert(productPurchase);
                //授权
                authorityService.authority(null,productPurchase.getPurchaseNo(),tenantId);
                //更新可用表
                orderServiceUtils.updateProductAvailable(productPurchase.getPurchaseNo());
                remittanceService.updateProductStatusOnRedis(productPurchase.getProductId(),productPurchase.getPurchaseNo());
            });
            return resultList;
        } catch (Exception ex) {
            throw new BusinessException(Errors.USERTIME_BATCH_ERROR.getMessage(), Errors.USERTIME_BATCH_ERROR.getCode(), ex);
        }
    }

    /**
     * @author: OY
     * 获取产品id
     * @return
     */
    private String getProductId(){
        DictValue dictValue = new DictValue();
        dictValue.setTypeCode("contract_product_code");
        dictValue.setValueCode("product_id");
        return dictValueMapper.getDictValueByTypeCodeAndValueCode(dictValue);
    }
    /**
     * @author: OY
     * 构建产品详情实体
     * @param vemsUseTimeDto
     * @param purchaseId
     * @return
     */
    private ProductDetail getProductDetail(VemsUseTimeDto vemsUseTimeDto, String purchaseId) {
        ProductDetail productDetail = new ProductDetail();
        String detailId = UUID.randomUUID().toString();
        String productId= getProductId();
        productDetail.setDetailId(detailId);
        productDetail.setProductQuantity(1);
        productDetail.setPaymentAmount(BigDecimal.valueOf(0.0));
        BigDecimal days=BigDecimal.valueOf(vemsUseTimeDto.getUseTime());
        productDetail.setPurchaseAmount(days.multiply(productPrice));
        productDetail.setDiscountAmount(days.multiply(productPrice));
        productDetail.setProductPurchaseId(purchaseId);
        productDetail.setAvailableUnit( ProductQueryUtil.PRODUCT_CTYPE_VEMDAY_UNIT);
        productDetail.setAvailableValue(vemsUseTimeDto.getUseTime());
        productDetail.setCreateTime(LocalDateTime.now());
        productDetail.setProductId(productId);
        productDetail.setTenantId(vemsUseTimeDto.getTenantId());
        productDetail.setVemsId(vemsUseTimeDto.getVemsId());
        productDetail.setVemsName(vemsUseTimeDto.getVemsName());

        ProductAvailable productAvailable=productAvailableMapper.selectByTenantAndProductId(vemsUseTimeDto.getTenantId(),vemsUseTimeDto.getVemsId(),productId);
        //可用表中有记录，已经激活的设备
        if(productAvailable!=null&&productAvailable.getActivationTime()!=null){
            if (productAvailable.getDueTime().isAfter(LocalDateTime.now())) {
                productDetail.setBeginTime(productAvailable.getDueTime());
                productDetail.setActivationTime(productAvailable.getActivationTime());
                productDetail.setDueTime(productAvailable.getDueTime().plusDays(vemsUseTimeDto.getUseTime() + 1));
            }else{
                productDetail.setBeginTime(LocalDate.now().atStartOfDay());
                productDetail.setDueTime(LocalDate.now().plusDays(vemsUseTimeDto.getUseTime()+1).atStartOfDay());
            }
        }
        return productDetail;
    }

    /**
     * @author: OY
     * 构建服务订单实体
     * @param vemsUseTimeDto
     * @return
     */
    private ProductPurchase getProductPurchase(VemsUseTimeDto vemsUseTimeDto) {
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setProductPrice(productPrice);
        productPurchase.setPayTime(LocalDateTime.now());
        productPurchase.setPurchaseTime(LocalDateTime.now());
        productPurchase.setProductQuantity(ONE);
        productPurchase.setPurchaseAmount(BigDecimal.valueOf(ONE));
        productPurchase.setPayType(PAY_TYPE);
        productPurchase.setPaymentAmount(PRODUCT_PRICE);
        BigDecimal days=BigDecimal.valueOf(vemsUseTimeDto.getUseTime());
        productPurchase.setPurchaseAmount(days.multiply(productPrice));
        productPurchase.setDiscountAmount(days.multiply(productPrice));
        productPurchase.setSuccessTime(LocalDateTime.now());
        productPurchase.setProductGrade(vemsUseTimeDto.getUseTime().toString());
        productPurchase.setIsRenew(ZERO);
        productPurchase.setProductId(getProductId());
        productPurchase.setTenantName(vemsUseTimeDto.getTenantName());
        productPurchase.setPurchaseNo(orderNoUtil.getOrderNo());
        productPurchase.setTenantId(vemsUseTimeDto.getTenantId());
        //获取租户管理员id
        productPurchase.setProductPurchaseId(UUID.randomUUID().toString());
        productPurchase.setGiveUserName(vemsUseTimeDto.getGiveUserName());
        productPurchase.setGiveReason(vemsUseTimeDto.getGiveReason());
        //未开票
        productPurchase.setInvoiceStatus(ZERO);
        //购买成功
        productPurchase.setPurchaseStatus(TWO);
        return productPurchase;
    }

    /**
     * @author: liangJB
     * 功能描述: <br>
     * 合作运营需要的日报表订单数据对象
     *      * 以设备ID为查询条件，找出对应的购买服务记录以订单倒叙时间排序
     * @date: 2020/8/12 13:26
     * @param: tenantId
     * @return:  VemsProductOrderDto
     */

    @Override
    public   List<VemsProductDRPCountDto> getOrderLstByPayType(VemsOrderDayQueryBase vemPageResp) {
        return productOrderMapper.getOrderLstByPayType(vemPageResp);
    }

    /**
     * @author: liangJB
     * 功能描述: <br>
     * 合作运营需要的日报表统计数据
     *      * 以设备ID为查询条件，找出对应的购买服务记录以订单倒叙时间排序
     * @date: 2020/8/12 13:26
     * @param: tenantId
     * @return:  VemsProductOrderDto
     */
    @Override
    public VemsProductDRPAllCount getOrderCountByPayType(VemsOrderDayQueryBase vemPageResp){
        return productOrderMapper.getOrderCountByPayType(vemPageResp);
    }
}
