package com.snbc.bbpf.bus.product.market.common.vo.invoice;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.vo.invoice
 * @ClassName: OpenerInvoice
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/10/15 14:42
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/15 14:42
 */
@Data
public class OpenerInvoice {
    
    @NotBlank(message = "发票id不能为空")
    private String invoiceApplyId;
}
