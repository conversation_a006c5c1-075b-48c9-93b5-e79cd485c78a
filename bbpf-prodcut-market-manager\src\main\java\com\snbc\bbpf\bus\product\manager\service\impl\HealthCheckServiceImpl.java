/*
 * 版权所有 2009-2025山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.product.manager.service.impl;


import com.snbc.bbpf.bus.product.manager.service.HealthCheckService;
import com.snbc.bbpf.bus.product.market.common.mapper.HealthCheckMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 健康检查服务实现类
 *
 * <AUTHOR>
 * @module bbpf-umis-manager
 * @date 2025-07-03
 * @version 2.0.0
 */
@Slf4j
@Service
public class HealthCheckServiceImpl implements HealthCheckService {

    /** 健康检查数据访问接口 */
    @Autowired
    private HealthCheckMapper healthCheckMapper;

    /**
     * 检查数据库连接状态
     * 通过执行SELECT 1查询来验证数据库连接是否正常
     *
     * <AUTHOR>
     * @date 2025-07-03
     * @since 2.0.0
     */
    @Override
    public void checkDatabaseConnection() {
        Integer result = healthCheckMapper.checkDatabaseConnection();
        log.debug("Database connection check result: {}", result);
    }
}