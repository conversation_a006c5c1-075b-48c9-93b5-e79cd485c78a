package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/*
* @author: liangJB
     * 功能描述: <br>
     * 产品购买列表类
     * @date: 2020/8/12 13:13
* 字段名称	类型	是否必填	描述
productId	String	是	服务编号
productName	String	是	服务名称
productBrief	String	是	服务简介
productImage	String	是	服务图片
productTypeName	String	是	服务类型
chargeTypeName	String	是	收费方式

* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServicePPDto extends ServicePDto {
    
    private String productTypeName;
    
    private String chargeTypeName;

}
