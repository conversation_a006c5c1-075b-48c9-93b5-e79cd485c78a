package com.snbc.bbpf.bus.product.market.common.entity;
/**
 * 收费梯度
 */
public class ProductGrade {
    /**
     * ID
     */
    private String productGradeId;
    /**
     * 服务Id
     */
    private String productId;
    /**
     * 阶梯
     */
    private String grade;
    /**
     * 阶梯单位
     */
    private String gradeUnit;
    /**
     * 阶梯折扣
     */
    private Long gradeDiscount;
    /**
     * 价格
     */
    private Double price;
    /**
     * 是否默认
     */
    private Integer isDefault;

    public String getProductGradeId() {
        return productGradeId;
    }
    /**
     * 设置productGradeId
     * @param productGradeId
     */
    public void setProductGradeId(String productGradeId) {
        this.productGradeId = productGradeId == null ? null : productGradeId.trim();
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade == null ? null : grade.trim();
    }

    public String getGradeUnit() {
        return gradeUnit;
    }
    /**
     * 设置gradeUnit
     * @param gradeUnit
     */
    public void setGradeUnit(String gradeUnit) {
        this.gradeUnit = gradeUnit == null ? null : gradeUnit.trim();
    }

    public Long getGradeDiscount() {
        return gradeDiscount;
    }
    /**
     * 设置gradeDiscount
     * @param gradeDiscount
     */
    public void setGradeDiscount(Long gradeDiscount) {
        this.gradeDiscount = gradeDiscount;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Integer getIsDefault() {
        return isDefault;
    }
    /**
     * 设置isDefault
     * @param isDefault
     */
    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }
}
