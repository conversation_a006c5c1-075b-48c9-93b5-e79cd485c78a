package com.snbc.bbpf.bus.product.market.common.entity;

import org.codehaus.jackson.annotate.JsonIgnoreProperties;

/**
 * 字典类型实体类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DictType {
    /**
     * 字典类型编码
     */

    private String typeCode;

    /**
     * 字典类型名称
     */

    private String typeName;

    /**
     * 获取字典类型编码
     */
    public String getTypeCode() {
        return typeCode;
    }

    /**
     * 设置typeCode
     * @param typeCode
     */
    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode == null ? null : typeCode.trim();
    }

    /**
     * 获取字典类型名称
     */
    public String getTypeName() {
        return typeName;
    }

    /**
     * 设置setTypeName
     * @param typeName
     */
    public void setTypeName(String typeName) {
        this.typeName = typeName == null ? null : typeName.trim();
    }
}
