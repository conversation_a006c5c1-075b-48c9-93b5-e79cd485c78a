package com.snbc.bbpf.bus.product.manager.config.payconfig;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 功能描述: <br>
 * 〈调用boss获取微信配置信息〉
 *
 * @param: null
 * @return:
 * @exception
 * @author: gs
 * @date: 2019-08-08 16:26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WechatConfig {
    /**
     * 支付类型
     */
    @JsonProperty("payConfigType")
    private String payConfigType;

    @JsonProperty("appSecret")
    private String appSecret;

    /**
     * 回调地址
     */
    @JsonProperty("callbackAddr")
    private String callbackAddr;

    /**
     * 商户号
     */
    @JsonProperty("mchId")
    private String mchId;

    /**
     * 私钥
     */
    @JsonProperty("appPrivateKey")
    private String appPrivateKey;

    /**
     * appid
     */
    @JsonProperty("appId")
    private String appId;

    /**
     * HTTP证书在服务器中的路径，用来加载证书用
     */
    @JsonProperty("certLocalPath")
    private String certLocalPath;

    @JsonProperty("certPassword")
    private String certPassword;

    @JsonProperty("subAppId")
    private String subAppId;

    @JsonProperty("subMchId")
    private String subMchId;

    /**
     * 是否分账标识
     * Y分账
     * N不分账
     */
    @JsonProperty("profitSharing")
    private String profitSharing;

}
