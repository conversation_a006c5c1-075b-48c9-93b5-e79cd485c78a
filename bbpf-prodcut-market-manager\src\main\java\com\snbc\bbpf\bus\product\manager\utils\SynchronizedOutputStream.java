package com.snbc.bbpf.bus.product.manager.utils;

import java.io.IOException;
import java.io.OutputStream;
/***********************************************************************
 * Packet
 * Project Name:vems
 *同步数据线程
 * <AUTHOR> @creator 王松
 * @create-time 2018-06-25 15:23
 ***********************************************************************/
public   class SynchronizedOutputStream extends OutputStream {
    private  OutputStream out ;
    private  Object lock ;
    /***
     * @Description:    初始化数据
     * @Author:         wangsong
     * @param :         OutputStream
     * @param :         df
     * @param :         date
     * @CreateDate:     2020/10/13 13:35
     * @UpdateUser:     wangsong
     * @UpdateDate:     2020/10/13 13:35
     */
    public  SynchronizedOutputStream(OutputStream out) {
        this(out, out);
    }
    /***
     * @Description:    初始化数据
     * @Author:         wangsong
     * @param :         OutputStream
     * @param :         df
     * @param :         date
     * @CreateDate:     2020/10/13 13:35
     * @UpdateUser:     wangsong
     * @UpdateDate:     2020/10/13 13:35
     */
    public  SynchronizedOutputStream(OutputStream out, Object lock) {
        this.out = out;
        this.lock = lock;
    }

    @Override
    public   void write(int datum) throws IOException {
        synchronized (lock) {
            out.write(datum);
        }
    }

    @Override
    public void write(byte[] data) throws IOException {
        synchronized (lock) {
            out.write(data);
        }
    }

    @Override
    public void write(byte[] data, int offset, int length) throws IOException {
        synchronized (lock) {
            out.write(data, offset, length);
        }
    }

    @Override
    public void flush() throws IOException {
        synchronized (lock) {
            out.flush();
        }
    }

    @Override
    public void close() throws IOException {
        synchronized (lock) {
            out.close();
        }
    }
}
