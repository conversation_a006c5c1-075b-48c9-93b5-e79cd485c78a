/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.enums;

import com.snbc.bbpf.bus.product.manager.config.Constant;

/**
 * @ClassName: AvailableUnitEnum
 * 服务使用单位
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/20 13:32
 */
public enum AvailableUnitEnum {
    /**
     * 天
     * */
    DAY_STR("Day", Constant.ONE_NUM),
    /**
     * 月
     * */
    MONTH_STR("Month", Constant.MONTH_NUM),
    /**
     * 季
     * */
    SEASON_STR("Season", Constant.SEASON_NUM),
    /**
     * 年
     * */
    YEAR_STR("Year", Constant.YEAR_NUM);

    private final String code;
    private final int name;

    AvailableUnitEnum(String code, int name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public int getName() {
        return name;
    }
    public static int getName(String code) {
        for (AvailableUnitEnum p : AvailableUnitEnum.values()) {
            if (p.code.equals(code)) {
                return p.name;
            }
        }
        return Constant.ZERO;
    }
}
