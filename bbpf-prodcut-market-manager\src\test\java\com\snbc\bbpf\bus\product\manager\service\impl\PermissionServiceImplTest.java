package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.utils.CurrentUser;
import com.snbc.bbpf.bus.product.market.common.dto.product.PermissionNode;
import com.snbc.bbpf.bus.product.market.common.dto.product.PermissionSortVo;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import com.snbc.bbpf.bus.product.market.common.entity.ProductServices;
import com.snbc.bbpf.bus.product.market.common.mapper.PermissionMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPermissionMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductServicesMapper;
import com.snbc.bbpf.bus.product.market.common.vo.PermissionVo;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.JavaType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.time.LocalDateTime;
import java.time.Month;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

/**
 * @ClassName: PermissionServiceImplTest
 * 权限测试类
 * @module: bbpf_project
 * @Author: wangsong
 * @date: 2021/11/19
 * copyright 2020 barm Inc. All rights reserver
 */
class PermissionServiceImplTest {
    @Mock
    PermissionMapper permissionMapper;
    @Mock
    ProductPermissionMapper productPermissionMapper;
    @Mock
    ProductServicesMapper productServicesMapper;
    @Mock
    Logger log;
    @InjectMocks
    PermissionServiceImpl permissionServiceImpl;

    List<Permission> permissions;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    @BeforeEach
    void setUp() throws  Exception  {
            String strPermissionList = "[\n" +
                    "            {\n" +
                    "                \"permissionId\": \"-1\",\n" +
                    "                \"permissionType\": \"0\",\n" +
                    "                \"permissionName\": \"权限\",\n" +
                    "                \"permissionCode\": \"SYQX\",\n" +
                    "                \"parentId\": \"\",\n" +
                    "                \"orderBy\": 0,\n" +
                    "                \"permissionImage\": null,\n" +
                    "                \"hasEnable\": 1,\n" +
                    "                \"routingUrl\": \" #\",\n" +
                    "                \"parentName\": \"\",\n" +
                    "                \"permissionDesc\": \"所有权限\",\n" +
                    "                \"permissionLevel\": 0,\n" +
                    "                \"sysType\": 0\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"permissionId\": \"24d421e2-fc70-4bc4-bd07-95c8553e886d\",\n" +
                    "                \"permissionType\": \"1\",\n" +
                    "                \"permissionName\": \"PC后台\",\n" +
                    "                \"permissionCode\": \"PCHT\",\n" +
                    "                \"parentId\": \"-1\",\n" +
                    "                \"orderBy\": 2,\n" +
                    "                \"permissionImage\": \"\",\n" +
                    "                \"hasEnable\": 1,\n" +
                    "                \"routingUrl\": \"#\",\n" +
                    "                \"parentName\": \"权限\",\n" +
                    "                \"permissionDesc\": \"PC后台系统\",\n" +
                    "                \"permissionLevel\": 1,\n" +
                    "                \"sysType\": 0\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"permissionId\": \"ce51e0d1-0e0f-4b5a-8ac9-f9b3a6a91418\",\n" +
                    "                \"permissionType\": \"2\",\n" +
                    "                \"permissionName\": \"系统管理\",\n" +
                    "                \"permissionCode\": \"systemManagement\",\n" +
                    "                \"parentId\": \"24d421e2-fc70-4bc4-bd07-95c8553e886d\",\n" +
                    "                \"orderBy\": 1,\n" +
                    "                \"permissionImage\": \"wechat\",\n" +
                    "                \"hasEnable\": 1,\n" +
                    "                \"routingUrl\": \"#\",\n" +
                    "                \"parentName\": \"PC后台\",\n" +
                    "                \"permissionDesc\": \"系统管理\",\n" +
                    "                \"permissionLevel\": 2,\n" +
                    "                \"sysType\": 0\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"permissionId\": \"ab5f233d-98b6-4e6b-ad8d-eb57d33a7554\",\n" +
                    "                \"permissionType\": \"2\",\n" +
                    "                \"permissionName\": \"字典管理\",\n" +
                    "                \"permissionCode\": \"dictionary\",\n" +
                    "                \"parentId\": \"ce51e0d1-0e0f-4b5a-8ac9-f9b3a6a91418\",\n" +
                    "                \"orderBy\": 0,\n" +
                    "                \"permissionImage\": \"el-icon-tickets\",\n" +
                    "                \"hasEnable\": 1,\n" +
                    "                \"routingUrl\": \"#\",\n" +
                    "                \"parentName\": \"系统管理\",\n" +
                    "                \"permissionDesc\": \"系统管理\",\n" +
                    "                \"permissionLevel\": 3,\n" +
                    "                \"sysType\": 0\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"permissionId\": \"913fadba-6534-4f4d-a0d7-86e8a421de2c\",\n" +
                    "                \"permissionType\": \"4\",\n" +
                    "                \"permissionName\": \"查询字典值\",\n" +
                    "                \"permissionCode\": \"dic_getDictValueList\",\n" +
                    "                \"parentId\": \"ab5f233d-98b6-4e6b-ad8d-eb57d33a7554\",\n" +
                    "                \"orderBy\": 1,\n" +
                    "                \"permissionImage\": \"\",\n" +
                    "                \"hasEnable\": 1,\n" +
                    "                \"routingUrl\": \"/console/dict/getDictValueList\",\n" +
                    "                \"parentName\": \"资源权限\",\n" +
                    "                \"permissionDesc\": \"\",\n" +
                    "                \"permissionLevel\": 4,\n" +
                    "                \"sysType\": 0\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"permissionId\": \"e518c6a1-e76e-4071-b108-08bf0c6a0450\",\n" +
                    "                \"permissionType\": \"4\",\n" +
                    "                \"permissionName\": \"删除字典值\",\n" +
                    "                \"permissionCode\": \"dic_delDictValue\",\n" +
                    "                \"parentId\": \"ab5f233d-98b6-4e6b-ad8d-eb57d33a7554\",\n" +
                    "                \"orderBy\": 2,\n" +
                    "                \"permissionImage\": \"\",\n" +
                    "                \"hasEnable\": 1,\n" +
                    "                \"routingUrl\": \"/console/dict/delDictValue\",\n" +
                    "                \"parentName\": \"资源权限\",\n" +
                    "                \"permissionDesc\": \"\",\n" +
                    "                \"permissionLevel\": 4,\n" +
                    "                \"sysType\": 0\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"permissionId\": \"b63adae0-631b-49a0-99e9-f1754d56b044\",\n" +
                    "                \"permissionType\": \"4\",\n" +
                    "                \"permissionName\": \"修改字典值\",\n" +
                    "                \"permissionCode\": \"dic_updateDictValue\",\n" +
                    "                \"parentId\": \"ab5f233d-98b6-4e6b-ad8d-eb57d33a7554\",\n" +
                    "                \"orderBy\": 3,\n" +
                    "                \"permissionImage\": \"\",\n" +
                    "                \"hasEnable\": 1,\n" +
                    "                \"routingUrl\": \"/console/dict/updateDictValue\",\n" +
                    "                \"parentName\": \"资源权限\",\n" +
                    "                \"permissionDesc\": \"\",\n" +
                    "                \"permissionLevel\": 4,\n" +
                    "                \"sysType\": 0\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"permissionId\": \"b59a58c2-332d-4308-9958-51da62835d50\",\n" +
                    "                \"permissionType\": \"4\",\n" +
                    "                \"permissionName\": \"添加字典值\",\n" +
                    "                \"permissionCode\": \"dic_addDictValue\",\n" +
                    "                \"parentId\": \"ab5f233d-98b6-4e6b-ad8d-eb57d33a7554\",\n" +
                    "                \"orderBy\": 4,\n" +
                    "                \"permissionImage\": \"\",\n" +
                    "                \"hasEnable\": 1,\n" +
                    "                \"routingUrl\": \"/console/dict/addDictValue\",\n" +
                    "                \"parentName\": \"资源权限\",\n" +
                    "                \"permissionDesc\": \"\",\n" +
                    "                \"permissionLevel\": 4,\n" +
                    "                \"sysType\": 0\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"permissionId\": \"3702ce39-8f8b-4443-b3ea-090b32235d2b\",\n" +
                    "                \"permissionType\": \"4\",\n" +
                    "                \"permissionName\": \"导出字典\",\n" +
                    "                \"permissionCode\": \"dic_exportAllDict\",\n" +
                    "                \"parentId\": \"ab5f233d-98b6-4e6b-ad8d-eb57d33a7554\",\n" +
                    "                \"orderBy\": 5,\n" +
                    "                \"permissionImage\": \"\",\n" +
                    "                \"hasEnable\": 1,\n" +
                    "                \"routingUrl\": \"/console/dict/exportAllDict\",\n" +
                    "                \"parentName\": \"资源权限\",\n" +
                    "                \"permissionDesc\": \"\",\n" +
                    "                \"permissionLevel\": 4,\n" +
                    "                \"sysType\": 0\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"permissionId\": \"cad0d12a-7bb0-4a42-9bda-697c356e25fc\",\n" +
                    "                \"permissionType\": \"4\",\n" +
                    "                \"permissionName\": \"查询字典类型\",\n" +
                    "                \"permissionCode\": \"dic_getDictTypeList\",\n" +
                    "                \"parentId\": \"ab5f233d-98b6-4e6b-ad8d-eb57d33a7554\",\n" +
                    "                \"orderBy\": 6,\n" +
                    "                \"permissionImage\": \"\",\n" +
                    "                \"hasEnable\": 1,\n" +
                    "                \"routingUrl\": \"/console/dict/getDictTypeList\",\n" +
                    "                \"parentName\": \"资源权限\",\n" +
                    "                \"permissionDesc\": \"\",\n" +
                    "                \"permissionLevel\": 4,\n" +
                    "                \"sysType\": 0\n" +
                    "            }\n" +
                    "        ]";
            JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, Permission.class);
            permissions = objectMapper.readValue(strPermissionList,javaType);
            permissionServiceImpl=new PermissionServiceImpl();
            MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetExpireTime() {
        Integer result = 0;
        Assertions.assertEquals(Integer.valueOf(0), result);
    }

    @Test
    void testGetAllPermission() {
        when(permissionMapper.getAllPermission()).thenReturn(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")));

        List<Permission> result = permissionServiceImpl.getAllPermission();
        Assertions.assertEquals(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")), result);
    }

    @Test
    void testGetAllPermissionContainRoot() {
        when(permissionMapper.getAllPermissionContainRoot()).thenReturn(permissions);

        PageInfo<PermissionVo> result = permissionServiceImpl.getAllPermissionContainRoot(Integer.valueOf(1), Integer.valueOf(10));
        Assertions.assertEquals(permissions.size()-1, result.getList().size());
    }

    @Test
    void testGetAllPermissionContainRootById() {
        when(permissionMapper.getAllPermissionById(anyString(), anyString())).thenReturn(permissions);

        PageInfo<PermissionVo> result = permissionServiceImpl.getAllPermissionContainRootById("permissionId", Integer.valueOf(1), Integer.valueOf(10), "");
        Assertions.assertEquals(permissions.size()-1, result.getList().size());
    }

   /* @Test
    void testGetEnableAllPermission() {
        when(permissionMapper.selectResourcePermissionListByRoleId(anyString(), anyString())).thenReturn(Arrays.<String>asList("String"));
        when(permissionMapper.getEnableAllPermission()).thenReturn(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")));

        List<PermissionsVo> result = permissionServiceImpl.getEnableAllPermission("productId", "sysType");
        Assertions.assertEquals(Arrays.<PermissionsVo>asList(new PermissionsVo(true, "permissionId", "permissionType", "permissionTypeName", "permissionName", "permissionCode", "parentId", Integer.valueOf(0), "permissionImage", Integer.valueOf(0), "routingUrl", Arrays.<PermissionsVo>asList(null))), result);
    }*/

    @Test
    void testGetAllPermissionByParentId() {
        when(permissionMapper.getPermissionByParentId(anyString())).thenReturn(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")));

        List<Permission> result = permissionServiceImpl.getAllPermissionByParentId("parentId");
        Assertions.assertEquals(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")), result);
    }

    @Test
    void testGetAllPermissionById() {
        when(permissionMapper.getAllPermissionById(anyString(), anyString())).thenReturn(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")));

        List<Permission> result = permissionServiceImpl.getAllPermissionById("permissionId");
        Assertions.assertEquals(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")), result);
    }

    @Test
    void testInsertPermission()  throws Exception {
        try (MockedStatic<CurrentUser> ms = Mockito.mockStatic(CurrentUser.class)){
            Permission permission=new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId");
            when(permissionMapper.insertSelective(any())).thenReturn(1);
            when(permissionMapper.selectByPrimaryKey(anyString())).thenReturn(permission);
            when(permissionMapper.queryMaxOrderByParentId(anyString())).thenReturn(0);

            int result = permissionServiceImpl.insertPermission(permission);
            Assertions.assertEquals(1, result);
        }
    }

    @Test
    void testUpdatePermission()  throws Exception {
        when(permissionMapper.selectRoleListByPermissionId(anyString())).thenReturn(Arrays.<String>asList());
        when(permissionMapper.selectByPrimaryKey(anyString())).thenReturn(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId"));
        when(permissionMapper.selectByPermissionCode(anyString())).thenReturn(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId"));
        when(permissionMapper.updateByPrimaryKeySelective(any())).thenReturn(0);
        when(productServicesMapper.selectByPrimaryKey(anyString())).thenReturn(new ProductServices());

        int result = permissionServiceImpl.updatePermission(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId"));
        Assertions.assertEquals(0, result);
    }

    @Test
    void testGetPermissionTree() throws Exception {
        when(permissionMapper.getAllPermissionWithRoot()).thenReturn(permissions);
        String strpermission = "{\n" +
                "    \"permissionId\":\"-1\",\n" +
                "    \"permissionName\":\"权限\",\n" +
                "    \"parentId\":\"\",\n" +
                "    \"parentName\":\"#\",\n" +
                "    \"level\":1,\n" +
                "    \"routingUrl\":null,\n" +
                "    \"permissionType\":\"0\",\n" +
                "    \"permissionIcon\":null,\n" +
                "    \"hasEnable\":1,\n" +
                "    \"remarks\":\"权限根节点\",\n" +
                "    \"orderBy\":0,\n" +
                "    \"permissionCode\":\"QBQX\",\n" +
                "    \"children\":[\n" +
                "        {\n" +
                "            \"permissionId\":\"24d421e2-fc70-4bc4-bd07-95c8553e886d\",\n" +
                "            \"permissionName\":\"PC后台\",\n" +
                "            \"parentId\":\"-1\",\n" +
                "            \"parentName\":\"权限\",\n" +
                "            \"level\":2,\n" +
                "            \"routingUrl\":\"#\",\n" +
                "            \"permissionType\":\"1\",\n" +
                "            \"permissionIcon\":\"\",\n" +
                "            \"hasEnable\":1,\n" +
                "            \"remarks\":\"PC后台系统\",\n" +
                "            \"orderBy\":7,\n" +
                "            \"permissionCode\":\"PCHT\",\n" +
                "            \"children\":[\n" +
                "                {\n" +
                "                    \"permissionId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                    \"permissionName\":\"系统管理\",\n" +
                "                    \"parentId\":\"24d421e2-fc70-4bc4-bd07-95c8553e886d\",\n" +
                "                    \"parentName\":\"PC后台\",\n" +
                "                    \"level\":3,\n" +
                "                    \"routingUrl\":\"#\",\n" +
                "                    \"permissionType\":\"2\",\n" +
                "                    \"permissionIcon\":\"el-icon-setting\",\n" +
                "                    \"hasEnable\":1,\n" +
                "                    \"remarks\":\"系统管理\",\n" +
                "                    \"orderBy\":1,\n" +
                "                    \"permissionCode\":\"systemManagement\",\n" +
                "                    \"children\":[\n" +
                "                        {\n" +
                "                            \"permissionId\":\"b4dc76e7-84f4-41dd-b3a4-eab25c15e1bd\",\n" +
                "                            \"permissionName\":\"组织机构\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-grape\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"组织机构\",\n" +
                "                            \"orderBy\":0,\n" +
                "                            \"permissionCode\":\"organization\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"ff4066f1-224a-43b9-8060-016e9332e37d\",\n" +
                "                            \"permissionName\":\"角色管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-user\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"角色管理\",\n" +
                "                            \"orderBy\":1,\n" +
                "                            \"permissionCode\":\"role\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"3ef01dea-99ab-44cd-b624-6b9b3f4e2b98\",\n" +
                "                            \"permissionName\":\"权限管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-ice-drink\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"权限管理\",\n" +
                "                            \"orderBy\":2,\n" +
                "                            \"permissionCode\":\"permission\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"fb7ce873-bdf0-4f61-83ce-0bb7e49da65b\",\n" +
                "                            \"permissionName\":\"地域管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-help\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"系统管理\",\n" +
                "                            \"orderBy\":3,\n" +
                "                            \"permissionCode\":\"area\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "                            \"permissionName\":\"字典管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":5,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-goods\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"系统管理\",\n" +
                "                            \"orderBy\":4,\n" +
                "                            \"permissionCode\":\"dictionary\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"202ac195-8d8f-4be5-af56-9b3400de04dd\",\n" +
                "                            \"permissionName\":\"配置管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-hot-water\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"配置管理\",\n" +
                "                            \"orderBy\":5,\n" +
                "                            \"permissionCode\":\"configuration\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"2825a748-031f-47ef-8156-5a4ae1ff0df8\",\n" +
                "                            \"permissionName\":\"日志管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-document\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"\",\n" +
                "                            \"orderBy\":6,\n" +
                "                            \"permissionCode\":\"logger\",\n" +
                "                            \"children\":null\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        PermissionNode permissionNode = objectMapper.readValue(strpermission, PermissionNode.class);
        PermissionNode result = permissionServiceImpl.getPermissionTree(0);
        Assertions.assertEquals(permissionNode.getPermissionId(), result.getPermissionId());
    }

    @Test
    void testPermissionSort() throws Exception{
        String strSort = "{\n" +
                "    \"currentNodeId\": \"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "    \"targetNodeId\": \"fb7ce873-bdf0-4f61-83ce-0bb7e49da65b\",\n" +
                "    \"parentId\": \"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "    \"type\": \"next\"\n" +
                "}";
        String strPermission="{\n" +
                "        \"permissionId\":\"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"字典管理\",\n" +
                "        \"permissionCode\":\"dictionary\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":4,\n" +
                "        \"permissionImage\":\"el-icon-goods\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"系统管理\",\n" +
                "        \"permissionLevel\":5,\n" +
                "        \"sysType\":0\n" +
                "    }";
        Permission permission=objectMapper.readValue(strPermission,Permission.class);
        PermissionSortVo permissionSortVo = objectMapper.readValue(strSort, PermissionSortVo.class);
        when(permissionMapper.selectByPrimaryKey(anyString())).thenReturn(permission);
        when(permissionMapper.getPermissionByParentId(anyString())).thenReturn(permissions);
        when(permissionMapper.updateByPrimaryKeySelective(any())).thenReturn(0);
        when(permissionMapper.queryMaxOrderByParentId(anyString())).thenReturn(0);
        Boolean result = permissionServiceImpl.permissionSort(permissionSortVo);
        Assertions.assertEquals(Boolean.TRUE, result);
    }

    @Test
    void testGetResourcePermissionsByRoleIds() throws Exception{
        when(permissionMapper.selectResourcePermissionListByRoleId(any(), anyString())).thenReturn(Arrays.<String>asList("String"));

        List<String> result = permissionServiceImpl.getResourcePermissionsByRoleIds("roleIds", "sysType");
        Assertions.assertEquals(Arrays.<String>asList("String"), result);
    }

    @Test
    void testDeletePermissions() throws Exception{
        when(permissionMapper.selectRoleListByPermissionId(anyString())).thenReturn(Arrays.<String>asList());
        when(permissionMapper.selectByPrimaryKey(anyString())).thenReturn(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId"));
        when(permissionMapper.getPermissionByParentId(anyString())).thenReturn(Arrays.<Permission>asList());
        when(productServicesMapper.selectByPrimaryKey(anyString())).thenReturn(new ProductServices());

        permissionServiceImpl.deletePermissions(new String[]{"permissionIds"});
    }

    @Test
    void testQueryPermission() throws Exception{
        when(permissionMapper.selectRoleListByPermissionId(anyString())).thenReturn(Arrays.<String>asList());
        when(permissionMapper.selectByPrimaryKey(anyString())).thenReturn(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId"));
        when(productServicesMapper.selectByPrimaryKey(anyString())).thenReturn(new ProductServices());

        PermissionVo result = permissionServiceImpl.queryPermission("permissionId");
        Assertions.assertEquals(result.getPermissionId(),"permissionId");
    }

    @Test
    void testSelectByPermissionCode() throws Exception{
        when(permissionMapper.selectByPermissionCode(anyString())).thenReturn(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId"));

        Permission result = permissionServiceImpl.selectByPermissionCode("permissionCode");
        Assertions.assertEquals(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), LocalDateTime.of(2021, Month.NOVEMBER, 19, 13, 21, 46), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId"), result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
