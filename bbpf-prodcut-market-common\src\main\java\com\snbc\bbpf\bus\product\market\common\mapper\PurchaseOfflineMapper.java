package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline;
import org.apache.ibatis.annotations.Mapper;
/**
 * @author: LiangJB
 * 功能描述: <br>
 * 线下支付表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface PurchaseOfflineMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 删除数据
     * @date: 2020/8/12 13:21
     */
    int deleteByPrimaryKey(String offlineId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(PurchaseOffline purchaseOffline);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(PurchaseOffline purchaseOffline);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    PurchaseOffline selectByPrimaryKey(String offlineId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    PurchaseOffline selectByPurchaseNo(String purchaseNo);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKeySelective(PurchaseOffline purchaseOffline);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKey(PurchaseOffline purchaseOffline);
}
