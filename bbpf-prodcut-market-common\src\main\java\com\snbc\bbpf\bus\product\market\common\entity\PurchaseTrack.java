package com.snbc.bbpf.bus.product.market.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseTrack {
    /**
     * 编号
     */
    private String purchaseTrackId;
    /**
     * 订单号
     */
    private String purchaseNo;
    /**
     * 下单时间
     */
    private LocalDateTime purchaseTime;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    /**
     * 成功时间
     */
    private LocalDateTime successTime;
    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * 取消原因
     */
    private String cancelReson;
    /**
     * 支付类型 /*支付类型 0：线下支付 1
            */
    private Integer payType;
    /**
     * 订单金额
     */
    private BigDecimal purchaseAmount;
    /**
     * 实付金额
     */
    protected BigDecimal paymentAmount;
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;
    /**
     * 订单状态 /*订单状态 0：待支付 1：已支付 2：购买成功 3 已取消 4 已退款*/
    private Integer purchaseStatus;
    /**
     * 租户Id
     */
    private String tenantId;
    /**
     * 用户Id
     */
    private String userId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
