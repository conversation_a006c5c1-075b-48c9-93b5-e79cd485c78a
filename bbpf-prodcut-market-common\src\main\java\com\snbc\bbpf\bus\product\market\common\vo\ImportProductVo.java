package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class ImportProductVo {
    //服务名称
    @NotNull(message = "服务名称不能为空")
    
    private String productName;
    //服务编码
    @NotNull(message = "服务编码不能为空")
    
    private String productCode;
    //服务类目
    @NotNull(message = "服务类目不能为空")
    
    private Integer  productCategory;
    //服务类型
    @NotNull(message = "服务类型不能为空")
    
    private  Integer productType;
    //收费方式
    @NotNull(message = "收费方式不能为空")
    
    private Integer chargeType;

    
    private List<String> permissionIds;

}
