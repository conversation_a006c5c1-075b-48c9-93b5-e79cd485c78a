package com.snbc.bbpf.bus.product.manager.redislock;

import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.product.manager.service.DictValueService;
import com.snbc.bbpf.bus.product.manager.service.OrderService;
import com.snbc.bbpf.bus.product.manager.service.ProductPayService;
import com.snbc.bbpf.bus.product.manager.service.PurchasePayTrackService;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.controller.pay
 * @ClassName: RedisKeyExpirationListener
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 过期处理
 * @Author: MR.LI
 * @CreateDate: 2020/10/13 13:03
 */
@Component
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(RedisKeyExpirationListener.class);
    @Autowired
    private OrderService orderService;
    @Autowired
    private ProductPayService productPayService;
    @Autowired
    private DictValueService dictValueService;
    @Autowired
    private PurchasePayTrackService purchasePayTrackService;
    @Autowired
    private ProductPurchaseMapper productPurchaseMapper;

    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    /**
     * 针对redis数据失效事件，进行数据处理</br>
     * wjc1 20230608 复杂度优化
     * @param message
     * @param pattern
     */
    @SneakyThrows
    @Override
    public void onMessage(@NotNull Message message, byte[] pattern) {
        LOGGER.debug("RedisKeyExpirationListener onMessage:{}", message);
        // 用户做自己的业务处理即可,注意message.toString()可以获取失效的key
        String expiredKey = message.toString();
        String aliPrefix = Constant.ALIPAY + "_";
        String wxPrefix = Constant.WECHATPAY + "_";
        // 支付宝
        if (expiredKey.contains(aliPrefix)) {
            payOrder(expiredKey, aliPrefix);
        } else if (expiredKey.contains(wxPrefix)) {
            //微信
            payOrder(expiredKey, wxPrefix);
        } else {
            LOGGER.debug("no match info，expiredKey={}",expiredKey);
        }
    }

    private void payOrder(String expiredKey, String payPrefix) throws Exception {
        String timeoutStr = dictValueService.getDictValueByTypeCodeAndValueCode(Constant.TIMEOUT_MIN_KEY, Constant.TIMEOUT_MIN_VALUE_KEY);
        //创建锁
        String outTradeNum = expiredKey.substring(payPrefix.length());
        //支付方式：alipay  wechat
        String payType=payPrefix.replace("_", "");
        String temp = getOutTradeNum(payType, outTradeNum);
        if (StringUtils.isNotBlank(temp)) {
            outTradeNum = temp;
        }
        //校验订单状态 为待支付时执行超时操作
        if (checkOrderStatus(outTradeNum)) {
            LOGGER.debug("RedisKeyExpirationListener {} outTradeNum:{}", payType, outTradeNum);
            boolean isLock = productPayService.lock(outTradeNum, 1, Long.parseLong(timeoutStr));
            // 判断是否获取锁
            if (isLock) {
                LOGGER.debug("{} order is not paid,order number:{}，Cancel the order",payType, outTradeNum);
                orderService.orderCancel(outTradeNum, Constant.PURCHASE_STATUS_TIMEOUT);
                //發送信息
                orderService.sendMsg(outTradeNum, BusTemplateCodeEnum.MSG_TYPE_TIMEOUT.getCode());
                productPayService.deleteLock(outTradeNum);
            } else {
                LOGGER.debug("{} order cancellation is in progress,The order number:{}",payType, outTradeNum);
            }
        }
    }

    /*
      校验订单状态是否为待支付
      @param purchaseNo
     * @return
     */
    private boolean checkOrderStatus(String purchaseNo) {
        ProductPurchase productPurchase = productPurchaseMapper.selectByPurchaseNo(purchaseNo);
        return null != productPurchase && Constant.PURCHASE_STATUS_WAITPAY == productPurchase.getPurchaseStatus();
    }

    private String getOutTradeNum(String payType, String payTrackNo) {
        PurchasePayTrack payTrack = new PurchasePayTrack();
        payTrack.setPayTrackNo(payTrackNo);
        payTrack.setPayType(payType);

        List<PurchasePayTrack> trackList = purchasePayTrackService.selectPayTracks(payTrack);
        if (!CollectionUtils.isEmpty(trackList)) {
            PurchasePayTrack track = trackList.get(0);
            return track.getPurchaseNo();
        }
        return null;
    }
}
