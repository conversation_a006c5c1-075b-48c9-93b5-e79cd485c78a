package com.snbc.bbpf.bus.product.manager.service.impl;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.config.payconfig.AlipayConfig;
import com.snbc.bbpf.bus.product.manager.config.payconfig.WechatConfig;
import com.snbc.bbpf.bus.product.manager.config.payconfig.request.AliQRCodeRequest;
import com.snbc.bbpf.bus.product.manager.config.payconfig.request.RefundRequest;
import com.snbc.bbpf.bus.product.manager.config.payconfig.request.WechatQRCodeRequest;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.ProductPayConfigService;
import com.snbc.bbpf.bus.product.manager.service.ProductPayService;
import com.snbc.pay.Pay;
import com.snbc.pay.entity.PreCreateData;
import com.snbc.pay.entity.request.TradeRefundRequest;
import com.snbc.pay.entity.response.OrderPayResponse;
import com.snbc.pay.entity.response.TradeCloseResponse;
import com.snbc.pay.entity.response.TradeRefundResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: ProductPayServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/10/13 13:03
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/13 13:03
 */
@Service
public class ProductPayServiceImpl implements ProductPayService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProductPayServiceImpl.class);

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ProductPayConfigService productPayConfigService;


    /***
     * @Description: 获取支付宝支付QRCode，支付二维码
     * @Author: wangsong
     * @param :         aliQRCodeRequest
     * @CreateDate: 2020/10/13 13:40
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/10/13 13:40
     * @return :        java.lang.String
     */
    @Override
    public String getAlipayQRCode(AliQRCodeRequest aliQRCodeRequest) throws BusinessException {
        AlipayConfig alipayConfig = JSON.parseObject(productPayConfigService.getAlipayPayConfig(Constant.ALIPAY_CONFIG_ID), AlipayConfig.class);
        LOGGER.debug("getAlipayQRCode alipayConfig:{}",JSON.toJSONString(alipayConfig));
        Pay pay = new com.snbc.pay.alipay.PayImpl(alipayConfig.getAppId(), alipayConfig.getAppPrivateKey(),
                alipayConfig.getCharset(), alipayConfig.getPublicKey(), alipayConfig.getPid(),
                alipayConfig.getSignType(), null, null);
        PreCreateData data = new PreCreateData();
        // 订单编号
        data.setOutTradeNo(aliQRCodeRequest.getOutTradeNo());
        // 商品名称
        data.setSubject(aliQRCodeRequest.getProductSubject());
        //总金额
        data.setTotalAmount(String.valueOf(aliQRCodeRequest.getTotalAmount()));
        //订单过期时间
        if (StringUtils.isNotBlank(aliQRCodeRequest.getTimeExpress())) {
            data.setTimeoutExpress(aliQRCodeRequest.getTimeExpress());
        } else {
            data.setTimeExpire("30");
        }
        OrderPayResponse res = null;
        try {
            long startTime = System.currentTimeMillis();
            res = pay.getQRcodeUrl(data, alipayConfig.getCallbackAddr());
            loggerRecord("调用支付宝获取二维码接口耗时：{}", "调用支付宝获取二维码接口返回信息为：{}"
                    , startTime, res);
        } catch (Exception e) {
            errorLogger(e);
        }
        isQRCodeError(res, "获取支付宝二维码失败 ProductPayServiceImpl.getAlipayQRCodeUrl");
        return res.getCodeUrl();
    }

    private void loggerRecord(String loggerInfo, String respInfo, long startTime, OrderPayResponse res) {
        LOGGER.info(loggerInfo, System.currentTimeMillis() - startTime);
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info(respInfo, JSON.toJSONString(res));
        }
    }

    /***
     * @Description: 微信支付二维码
     * @Author: wangsong
     * @param :         wechatQRCodeRequest
     * @CreateDate: 2020/10/13 13:40
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/10/13 13:40
     * @return :        java.lang.String
     */

    public String getWechatQRCode(WechatQRCodeRequest wechatQRCodeRequest) throws BusinessException {
        WechatConfig wechatConfig = JSON.parseObject(productPayConfigService.getWechatPayConfig(Constant.WECHAT_CONFIG_ID),WechatConfig.class);
        LOGGER.info("getWechatQRCode wechatConfig:{}",JSON.toJSONString(wechatConfig));
        Pay pay = new com.snbc.pay.weixin.PayImpl(wechatConfig.getAppId(), wechatConfig.getSubAppId(),
                wechatConfig.getAppPrivateKey(), wechatConfig.getMchId(),
                wechatConfig.getSubMchId(), wechatConfig.getCertLocalPath(), wechatConfig.getCertPassword(), null);
        PreCreateData data = new PreCreateData();
        // 订单id
        data.setOutTradeNo(wechatQRCodeRequest.getOutTradeNo());
        // 商品名称
        data.setSubject(wechatQRCodeRequest.getSubject());
        String totalFee = String.valueOf(wechatQRCodeRequest.getAmount());
        // 默认为分
        data.setTotalAmount(totalFee);
        data.setTradeType(Constant.NATIVE);
        //订单过期时间
        if (StringUtils.isNotBlank(wechatQRCodeRequest.getTimeoutExpress())) {
            data.setTimeExpire(wechatQRCodeRequest.getTimeoutExpress());
        } else {
            data.setTimeExpire("30");
        }
        String callbackAddr = wechatConfig.getCallbackAddr();
        OrderPayResponse res = null;
        try {
            long startTime = System.currentTimeMillis();
            LOGGER.info("getWechatQRCode pay:{}",JSON.toJSONString(pay));
            res = pay.getQRcodeUrl(data, callbackAddr);
            loggerRecord("The calling time of wechat QR code：{}",
                    "Call to obtain the return information of the TWO-DIMENSIONAL code interface：{}",
                    startTime, res);
        } catch (Exception e) {
            errorLogger(e);
        }
        isQRCodeError(res, "获取二维码失败 ProductPayServiceImpl.getWechatQRCodeUrl");
        return res.getCodeUrl();
    }


    private void isQRCodeError(OrderPayResponse res, String qRCodeErrorInfo) throws BusinessException {
        if (res == null || !res.isSuccess()) {
            LOGGER.info(qRCodeErrorInfo);
            throw new BusinessException(Errors.OUT_SNBCPAY_PARAM_ERROR.getMessage(), Errors.OUT_SNBCPAY_PARAM_ERROR.getCode(), null);
        }
    }

    private void errorLogger(Exception e) throws BusinessException {
        LOGGER.error("Error obtaining qr code by calling snbcpay. getQRcodeUrl", e);
        throw new BusinessException(Errors.QR_CODEERROR_ERROR.getMessage(), Errors.QR_CODEERROR_ERROR.getCode(), e);
    }

    /***
     * @Description: 支付宝回调
     * @Author: wangsong
     * @param :         refundRequest
     * @CreateDate: 2020/10/13 13:41
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/10/13 13:41
     * @return :        com.snbc.pay.entity.response.TradeRefundResponse
     */
    @Override
    public TradeRefundResponse refundAliOrder(RefundRequest refundRequest) throws Exception {
        AlipayConfig alipayConfig = JSON.parseObject(productPayConfigService.getAlipayPayConfig(Constant.ALIPAY_CONFIG_ID),AlipayConfig.class);
        Pay pay = new com.snbc.pay.alipay.PayImpl(alipayConfig.getAppId(), alipayConfig.getAppPrivateKey(),
                alipayConfig.getCharset(), alipayConfig.getPublicKey(), alipayConfig.getPid(),
                alipayConfig.getSignType(), null, null);
        TradeRefundRequest tradeRefundRequest = new TradeRefundRequest();
        tradeRefundRequest.setOutTradeNo(refundRequest.getOutTradeNo());
        tradeRefundRequest.setOutRequestNo(refundRequest.getOutRequestNo());
        tradeRefundRequest.setTotalAmount(refundRequest.getTotalAmount());
        tradeRefundRequest.setRefundAmount(refundRequest.getRefundAmount());
        return pay.refundOrder(tradeRefundRequest);
    }

    /***
     * @Description: 微信回调
     * @Author: wangsong
     * @param :         refundRequest
     * @CreateDate: 2020/10/13 13:41
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/10/13 13:41
     * @return :        com.snbc.pay.entity.response.TradeRefundResponse
     */
    @Override
    public TradeRefundResponse refundWechatOrder(RefundRequest refundRequest) throws Exception {
        WechatConfig wechatConfig = JSON.parseObject(productPayConfigService.getWechatPayConfig(Constant.WECHAT_CONFIG_ID),WechatConfig.class);
        Pay wechatPay = new com.snbc.pay.weixin.PayImpl(wechatConfig.getAppId(),
                wechatConfig.getSubAppId(), wechatConfig.getAppPrivateKey(), wechatConfig.getMchId(),
                wechatConfig.getSubMchId(), wechatConfig.getCertLocalPath(), wechatConfig.getCertPassword(), null);
        TradeRefundRequest tradeRefundRequest = new TradeRefundRequest();
        tradeRefundRequest.setOutTradeNo(refundRequest.getOutTradeNo());
        tradeRefundRequest.setOutRequestNo(refundRequest.getOutRequestNo());
        tradeRefundRequest.setTotalAmount(refundRequest.getTotalAmount());
        tradeRefundRequest.setRefundAmount(refundRequest.getRefundAmount());
        return wechatPay.refundOrder(tradeRefundRequest);
    }

    @Override
    public TradeCloseResponse closeWechatOrder(String orderNum) throws Exception {
        WechatConfig wechatConfig = JSON.parseObject(productPayConfigService.getWechatPayConfig(Constant.WECHAT_CONFIG_ID),WechatConfig.class);
        Pay wechatPay = new com.snbc.pay.weixin.PayImpl(wechatConfig.getAppId(), wechatConfig.getSubAppId(),
                wechatConfig.getAppPrivateKey(), wechatConfig.getMchId(), wechatConfig.getSubMchId(),
                wechatConfig.getCertLocalPath(), wechatConfig.getCertPassword(), null);
        TradeCloseResponse response;
        try {
            long startTime = System.currentTimeMillis();
            response = wechatPay.closeOrder(orderNum);
            LOGGER.info("The time required to invoke the third party of wechat is:{}ms", System.currentTimeMillis() - startTime);
            if (!response.getCode().equals(Constant.SNBC_PAY_SUCCESS_CODE)) {
                throw new BusinessException(response.getMsg(), response.getSubCode(), null);
            }
        } catch (Exception e) {
            LOGGER.error("Wechat closed order is abnormal,The order number：{}", orderNum, e);
            throw new BusinessException(Errors.CLOSE_ORDER_ERROR.getMessage(), Errors.CLOSE_ORDER_ERROR.getCode(), e);
        }
        return response;
    }

    @Override
    public TradeCloseResponse closeAliOrder(String orderNum) throws Exception {
        AlipayConfig alipayConfig = JSON.parseObject(productPayConfigService.getAlipayPayConfig(Constant.ALIPAY_CONFIG_ID),AlipayConfig.class);
        Pay pay = new com.snbc.pay.alipay.PayImpl(alipayConfig.getAppId(), alipayConfig.getAppPrivateKey(),
                alipayConfig.getCharset(), alipayConfig.getPublicKey(), alipayConfig.getPid(),
                alipayConfig.getSignType(), null, null);
        return pay.closeOrder(orderNum);
    }

    /**
     * 创建锁
     *
     * @param key         锁的Key
     * @param value       值(随便写毫无意义)
     * @param releaseTime 锁过期时间 防止死锁
     * @return
     */
    @Override
    public boolean lock(String key, int value, long releaseTime) {
        // 尝试获取锁
        Boolean boo = redisTemplate.opsForValue().setIfAbsent(key, value);
        redisTemplate.expire(key, releaseTime, TimeUnit.MINUTES);
        // 判断结果
        return boo != null && boo;
    }

    /**
     * 根据key'删除锁
     *
     * @param key
     */
    @Override
    public void deleteLock(String key) {
        // 删除key即可释放锁
        redisTemplate.delete(key);
    }

    @Override
    public void updateProductStatus(String key, String value) {
        redisTemplate.opsForValue().set(key, value,Constant.EXPIRE_TIME,TimeUnit.SECONDS);
        LOGGER.info("Cache update completed：{}",key);
    }

}
