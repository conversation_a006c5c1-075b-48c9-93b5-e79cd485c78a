/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snbc.bbpf.bus.product.manager.config.NumberConstant;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.utils.CurrentUser;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.snbc.bbpf.bus.product.manager.config.Constant.TENANTID;

/**
 * @ClassName: ParameterRequestWrapper
 * @Description: 去除request参数空格
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/8/12
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public class ParamRequestWrapper extends HttpServletRequestWrapper {
    private static final Logger LOGGER = LoggerFactory.getLogger(ParamRequestWrapper.class);

    private Map<String, String[]> params = new HashMap<>();

    public ParamRequestWrapper(HttpServletRequest request) {
        super(request);
        Map<String, String[]> requestMap = request.getParameterMap();
        this.params.putAll(requestMap);
        if (params.containsKey(TENANTID) && !params.get(TENANTID)[NumberConstant.NO_ZERO].equals(CurrentUser.getTenantId())) {
            throw new BusinessException(Errors.PARAM_ERROR.getMessage(), Errors.PARAM_ERROR.getCode());
        }
        this.modifyParameterValues();
    }

    /***
     * @Description: 获取输入流
     * @Author: ws
     * @CreateDate: 2021/6/8 10:25
     * @UpdateDate: 2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.bus.system.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public ServletInputStream getInputStream() throws IOException {
        String json = "";
        try {
            if (!super.getHeader(HttpHeaders.CONTENT_TYPE).equalsIgnoreCase(MediaType.APPLICATION_JSON_VALUE)
                    && !super.getHeader(HttpHeaders.CONTENT_TYPE).equalsIgnoreCase(MediaType.APPLICATION_JSON_UTF8_VALUE)) {
                return super.getInputStream();
            }
            json = IOUtils.toString(super.getInputStream(), StandardCharsets.UTF_8.name());
            if (StringUtils.isEmpty(json)) {
                return super.getInputStream();
            }
            Map<String, Object> map = jsonStringToMap(json);
            if (map.containsKey(TENANTID) && !map.get(TENANTID).equals(super.getHeader(TENANTID))) {
                throw new BusinessException(Errors.PARAM_ERROR.getMessage(), Errors.PARAM_ERROR.getCode());
            }
            ByteArrayInputStream bis = new ByteArrayInputStream(JSON.toJSONString(map).getBytes(StandardCharsets.UTF_8.name()));
            return new MyServletInputStream(bis);
        } catch (BusinessException e) {
            LOGGER.warn("PraamRequestWrapper BusinessException error ", e);
            throw new BusinessException(e.getMessage(), e.getCode());
        } catch (Exception e) {
            LOGGER.warn("exception error ", e);
            return new MyServletInputStream(new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8.name())));
        }
    }

    private void modifyParameterValues() {
        Set<String> set = params.keySet();
        Iterator<String> it = set.iterator();
        while (it.hasNext()) {
            String key = it.next();
            String[] values = params.get(key);
            values[0] = values[0].trim();
            params.put(key, values);
        }
    }

    /***
     * @Description: 获取getParameter
     * @Author: ws
     * @param :         name
     * @CreateDate: 2021/6/8 10:25
     * @UpdateDate: 2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.bus.system.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public String getParameter(String name) {
        String[] values = params.get(name);
        if (values == null || values.length == 0) {
            return null;
        }
        return values[0];
    }

    /***
     * @Description: 获取getParameterValues
     * @Author: ws
     * @param :         name
     * @CreateDate: 2021/6/8 10:25
     * @UpdateDate: 2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.bus.system.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public String[] getParameterValues(String name) {
        return params.get(name);
    }

    /***
     * @Description: 获取当前组织机构及子组织机构下人员列表
     * @Author: WS
     * @CreateDate: 2021/6/8 10:25
     * @UpdateDate: 2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.bus.system.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    class MyServletInputStream extends ServletInputStream {
        private ByteArrayInputStream bis;

        public MyServletInputStream(ByteArrayInputStream bis) {
            this.bis = bis;
        }

        @Override
        public boolean isFinished() {
            return true;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener listener) {
            LOGGER.debug("setReadListener");
        }

        @Override
        public int read() {
            return bis.read();
        }
    }

    /***
     * @Description: 获取jsonStringToMap
     * @Author: ws
     * @param :         jsonString
     * @CreateDate: 2021/6/8 10:25
     * @UpdateDate: 2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.bus.system.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static Map<String, Object> jsonStringToMap(String jsonString) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        for (Object k : jsonObject.keySet()) {
            Object o = jsonObject.get(k);
            if (o instanceof JSONArray) {
                Iterator<Object> it = ((JSONArray) o).iterator();
                List<Object> list = new ArrayList<>();
                while (it.hasNext()) {
                    Object obj = it.next();
                    if (obj instanceof JSONObject) {
                        list.add(jsonStringToMap(obj.toString()));
                    } else {
                        list.add(obj);
                    }
                }
                map.put(k.toString(), list);
            } else if (o instanceof JSONObject) {
                map.put(k.toString(), jsonStringToMap(o.toString()));
            } else if (o instanceof String) {
                map.put(k.toString(), o.toString().trim());
            } else {
                map.put(k.toString(), o);
            }
        }
        return map;
    }
}
