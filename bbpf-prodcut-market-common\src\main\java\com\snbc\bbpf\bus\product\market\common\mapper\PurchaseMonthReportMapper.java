package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.PurchaseMonthReport;
import org.apache.ibatis.annotations.Mapper;
/**
 * @author: LiangJB
 * 功能描述: <br>
 * 月报表表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface PurchaseMonthReportMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 删除数据
     * @date: 2020/8/12 13:21
     */
    int deleteByPrimaryKey(String monthReportId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(PurchaseMonthReport monthReport);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(PurchaseMonthReport monthReport);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 查询月报表数据
     * @date: 2020/8/12 13:21
     */
    PurchaseMonthReport selectByPrimaryKey(String monthReportId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 修改月报表数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKeySelective(PurchaseMonthReport monthReport);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 修改月报表数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKey(PurchaseMonthReport monthReport);
}
