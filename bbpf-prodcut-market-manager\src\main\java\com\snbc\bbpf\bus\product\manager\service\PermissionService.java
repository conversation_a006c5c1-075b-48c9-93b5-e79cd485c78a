/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.market.common.dto.product.MenuPermissionDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.PermissionNode;
import com.snbc.bbpf.bus.product.market.common.dto.product.PermissionSortVo;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import com.snbc.bbpf.bus.product.market.common.vo.PermissionVo;
import com.snbc.bbpf.bus.product.market.common.vo.PermissionsVo;

import java.io.IOException;
import java.util.List;

/**
 * @ClassName: PermissionService
 * 权限定义类
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
public interface PermissionService {
    /**
     * 权限树
     * @param level
     * @return
     */
    PermissionNode getPermissionTree(int level);
    /**
     * 拖拽排序
     * @param perSort
     * @return
     * @throws Exception
     */
    Boolean permissionSort(PermissionSortVo perSort) throws Exception;

    /**
     * 获取资源权限
     * @param productIds
     * @param sysType
     * @return
     * @throws IOException
     */
    List<String> getResourcePermissionsByRoleIds(String productIds, String sysType) throws IOException;
    /**
     * 批量删除权限定义
     * @param permissionIds
     * @throws Exception
     */
    void deletePermissions(String[] permissionIds) throws Exception;
    /**
     * 查询所有权限树节点
     * @return
     */
    List<Permission> getAllPermission();

    /**
     * 查询所有权限树节点 --包含根节点
     * @return
     */
    PageInfo<PermissionVo> getAllPermissionContainRoot(Integer pageNum, Integer pageSize);

    /**
     * 查询所有权限树节点
     * @return
     */
    List<PermissionsVo> getEnableAllPermission(String productId, String sysType );
    /**
     * 根据父节点获取所有子节点以及下属节点
     * @param parentId
     * @return
     */
    List<Permission> getAllPermissionByParentId(String parentId);
    /**
     * 获取所有子节点以及下属节点
     * @param permissionId
     * @return
     */
    List<Permission> getAllPermissionById(String permissionId);

    /**
     * 获取所有子节点 及父节点
     * @param permissionId
     * @return
     */
    PageInfo<PermissionVo> getAllPermissionContainRootById(String permissionId,Integer pageNum,Integer pageSize,String serachKey);

    /**
     * 添加系统资源
     * @param resource
     * @return
     * @throws Exception
     */
    int insertPermission(Permission resource) throws Exception;

    /**
     * 修改系统资源信息
     *
     * @param resource
     * @return
     * @throws Exception
     */
    int updatePermission(Permission resource) throws Exception;
    int changeStatus(Permission resource) throws Exception;

    /**
     * 查询系统资源信息
     *
     * @param permissionId
     * @return
     * @throws Exception
     */
    PermissionVo queryPermission(String permissionId) throws Exception;
    /**
     * 查询系统资源信息
     *
     * @param permissionCode
     * @return
     * @throws Exception
     */
    Permission selectByPermissionCode(String permissionCode) throws Exception;


    List<MenuPermissionDto> getAllPermissionList();
}
