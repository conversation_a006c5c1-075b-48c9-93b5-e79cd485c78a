package com.snbc.bbpf.bus.product.manager.exception;

import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.manager.utils.ResultVoUtil;
import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.text.MessageFormat;
import java.util.Objects;

/**
 * 异常拦截处理器
 *
 * <AUTHOR>
 */
@ControllerAdvice
@ResponseBody
/**
  * @Description:    拦截controller异常
  * @Author:         wangsong
  * @param:           * @param null :
  * @CreateDate:     2020/8/24 11:54
  * @UpdateUser:     wangsong
  * @UpdateDate:     2020/8/24 11:54
  * @return:          * @return : null
 */
public class GlobalExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    private CallResponse callResponse = new CallResponse();
    private CommonResp commonResp = new CommonResp();

    /***
      * @Description:    业务异常
      * @Author:         wangsong
      * @param :         businessException
      * @CreateDate:     2020/8/24 11:02
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/8/24 11:02
      * @return :        com.snbc.vems.product.exception.ProductCommonResp
     */
    @ExceptionHandler(BusinessException.class)
    public CommonResp bussessionException(BusinessException businessException){
        LOGGER.error(businessException.getMessage(), businessException);
        callResponse.setCode(businessException.getCode());
        callResponse.setMessage(businessException.getMessage());
        commonResp.setHead(callResponse);
        return commonResp;
    }

    //运行时异常
    @ExceptionHandler({RuntimeException.class})
    public CommonResp runtimeExceptionHandler(Exception ex) {
        if(ex.getMessage().contains("ClientException")){
            LOGGER.error(ex.getMessage(),ex);
            callResponse.setCode(Errors.OTHER_SYSTEM_ERROR.getCode());
            callResponse.setMessage(Errors.OTHER_SYSTEM_ERROR.getMessage());
            commonResp.setHead(callResponse);
        } else {
            LOGGER.error(ex.getMessage(), ex);
            callResponse.setCode(Errors.FAILED.getCode());
            callResponse.setMessage(Errors.FAILED.getMessage());
            commonResp.setHead(callResponse);
        }
        return commonResp;
    }

    //其他错误
    @ExceptionHandler({Exception.class})
    public CommonResp exception(Exception ex) {
        LOGGER.error(ex.getMessage(), ex);
        callResponse.setCode(Errors.FAILED.getCode());
        callResponse.setMessage(Errors.FAILED.getMessage());
        commonResp.setHead(callResponse);
        return commonResp;
    }


    //必填参数异常
    @ExceptionHandler({MissingServletRequestParameterException.class})
    public CommonResp exception(MissingServletRequestParameterException ex) {
        callResponse.setCode(Errors.PARAM_NOTNULL.getCode());
        callResponse.setMessage(MessageFormat.format(Errors.PARAM_NOTNULL.getMessage(),ex.getParameterName()));
        commonResp.setHead(callResponse);
        return commonResp;
    }

    /**
     * 拦截表单参数校验
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({BindException.class})
    public CommonResp bindException(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        callResponse = ResultVoUtil.error(Errors.PARAM_NOTNULL.getCode(),
                MessageFormat.format(Errors.PARAM_NOTNULL.getMessage(),
                Objects.requireNonNull(bindingResult.getFieldError()).getField()));
        commonResp.setHead(callResponse);
        return commonResp;
    }

    /**
     * 拦截JSON参数校验
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResp bindException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        callResponse = ResultVoUtil.error(Errors.PARAM_NOTNULL.getCode(),
                MessageFormat.format(Errors.PARAM_NOTNULL.getMessage(),
                Objects.requireNonNull(bindingResult.getFieldError()).getField()));
        commonResp.setHead(callResponse);
        return commonResp;
    }
    /**
     * 入参解析失败
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public CommonResp httpMessageNotReadableException(HttpMessageNotReadableException e) {
        callResponse = ResultVoUtil.error(Errors.PARAM_NOTNULL.getCode(),
                MessageFormat.format(Errors.INPUT_PARAM_ANALYSIS_ERROR.getMessage(),e.getMessage()));
        commonResp.setHead(callResponse);
        return commonResp;
    }

    /***
     * @Description:    方法405异常拦截
     * @Author:         wjc1
     * @param :         e
     * @CreateDate:     2021/5/17 16:11
     * @UpdateDate:     2021/5/17 16:11
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler({HttpRequestMethodNotSupportedException.class})
    public CommonResp httpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        callResponse.setCode(String.valueOf(HttpStatus.METHOD_NOT_ALLOWED));
        callResponse.setMessage(e.getLocalizedMessage());
        commonResp.setHead(callResponse);
        return commonResp;
    }
    /***
     * @Description:    方法415异常拦截
     * @Author:         wjc1
     * @param :         e
     * @CreateDate:     2021/5/17 16:11
     * @UpdateDate:     2021/5/17 16:11
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    @ExceptionHandler({HttpMediaTypeNotSupportedException.class})
    public CommonResp httpRequestMethodNotSupportedException(HttpMediaTypeNotSupportedException e) {
        callResponse.setCode(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE));
        callResponse.setMessage(e.getLocalizedMessage());
        commonResp.setHead(callResponse);
        return commonResp;
    }
}
