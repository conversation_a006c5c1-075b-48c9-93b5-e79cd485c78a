/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.mapper;


import com.snbc.bbpf.bus.product.market.common.dto.product.MenuPermissionDto;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: PermissionMapper
 * 权限定义数据访问类
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Mapper
public interface PermissionMapper {

    /**
     * 获取资源权限
     * @param productIds 产品服务id
     * @param sysType 系统类型
     * @return
     */
    List<String> selectResourcePermissionListByRoleId(@Param ( value = "productIds" ) String[] productIds, @Param ( value = "sysType" ) String sysType);

    /**
     * 根据权限id查找角色列表
     * @param permissionId
     * @return
     */
    List<String> selectRoleListByPermissionId(@Param ( value = "permissionId" ) String permissionId);
    /**
     * 更新权限排序
     * @param perSortList
     */
    void updatePermissionList(List<Permission> perSortList);

    /**
     * 更新path
     * @param oldPath
     * @param newPath
     */
    void updatePermissionPath(@Param ( "oldPath" ) String oldPath, @Param ( "newPath" ) String newPath);
    void updatePermissionLevel();
    /**
     *
     * @param permissionId
     * @return
     */
    int deleteByPrimaryKey(String permissionId);

    /**
     * 批量删除权限定义
     * @param permissionIds
     */
    void deleteByPermissionIds(String[] permissionIds);
    /**
     * 插入权限数据
     * @param permission
     * @return
     */
    int insert(Permission permission);

    /**
     * 根据条件插入权限数据
     * @param record
     * @return
     */
    int insertSelective(Permission permission);

    /**
     * 根据权限id获取权限信息
     * @param permissionId
     * @return
     */
    Permission selectByPrimaryKey(String permissionId);

    /**
     * 根据ids 查询
     * @param ids
     * @return
     */
    List<Permission> selectByIds(String[] ids);
    /**
     * 根据权限编码获取权限信息
     * @param permissionCode
     * @return
     */
    Permission selectByPermissionCode(String permissionCode);
    /**
     * 查询所有权限节点，不包含根节点
     * @return
     */
    List<Permission> getAllPermission();

    List<Permission> getAllPermissionContainRoot();
    /**
     * 查询所有可用权限节点，不包含根节点
     * @return
     */
    List<Permission> getEnableAllPermission();
    /**
     * 查询所有权限节点，包含根节点
     * @return
     */
    List<Permission> getAllPermissionWithRoot();
    /**
     * 根据父节点id获取子节点
     * @param parentId
     * @return
     */
    List<Permission> getPermissionByParentId(@Param ( "parentId" ) String parentId);
    /**
     * 根据节点id获取所有子孙节点
     * @param permissionId
     * @return
     */
    List<Permission> getAllPermissionById(String permissionId,String serachKey);

    /**
     * 根据主键更新权限信息
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(Permission permission);

    /**
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(Permission permission);

    /**
     * 获取权限最大排序号
     * @param parentId
     * @return
     */
    int queryMaxOrderByParentId(String parentId);
    /**
     * 根据订单号获取权限
     * @param purchaseNo
     * @return
     */
    List<Permission> getPermissionByPurchaseNo(String purchaseNo);

    List<MenuPermissionDto> getAllPermissionForProductDetil(@Param("funcationIds") List<String> funcationIds);

    List<Permission> getMenuPermission();
}
