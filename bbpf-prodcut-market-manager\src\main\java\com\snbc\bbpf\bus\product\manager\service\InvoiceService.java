package com.snbc.bbpf.bus.product.manager.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceListRRQDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoicePurchaseDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceTitleDto;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ApplyInvoiceParam;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.InvoiceParamQuery;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.OpenerInvoice;

import jakarta.servlet.http.HttpServletResponse;
import java.security.NoSuchAlgorithmException;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service
 * @ClassName: InvoiceService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票业务处理层
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:21
 */
public interface InvoiceService {

    InvoiceDetailDto invoiceDetail(String invoiceApplyId) ;

    PageInfo<InvoicePurchaseDto> invoicePurchaseListWithPage(String invoiceApplyId, Integer pageSize,
                                 Integer pageNum, String tenantId, String userId, String sortField, String sequence);

    void exportInvoice(HttpServletResponse response,InvoiceParamQuery invoiceParamQuery) throws BusinessException;

    PageInfo<InvoiceListRRQDto> invoiceListWithPageForRRQ(InvoiceParamQuery invoiceParamQuery);

    void applyInvoice(ApplyInvoiceParam applyInvoiceParam) throws BusinessException, NoSuchAlgorithmException;

    void updateInvoiceStatus(OpenerInvoice openerInvoice) throws BusinessException;

    InvoiceTitleDto getInvoiceTitle(String tenantId);

}
