package com.snbc.bbpf.bus.product.manager.service.impl;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.service.AuthorityService;
import com.snbc.bbpf.bus.product.manager.converter.PermissionToPermission2TenantConverter;
import java.util.stream.Collectors;
import com.snbc.bbpf.bus.product.market.common.dto.order.Authority2TenantConfigDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.Permission2Tenant;
import com.snbc.bbpf.bus.product.market.common.entity.ErrorLog;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import com.snbc.bbpf.bus.product.market.common.mapper.ErrorLogMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PermissionMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.controller
 * @ClassName: 验证服务
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 文件上传接口
 * @Author: ouyang
 * @CreateDate: 2020/8/24 14:55
 * @UpdateUser: ouyang
 * @UpdateDate: 2020/10/10 14:55
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AuthorityServiceImpl implements AuthorityService {

    @Autowired
    private ErrorLogMapper errorLogMapper;
    @Autowired
    private PermissionMapper permissionMapper;
    public static final int ZERO = 0;
    public static final int THREE = 3;
    public static final int INT_TWO = 2;
    private static final Logger LOGGER = LoggerFactory.getLogger(AuthorityServiceImpl.class);

    //这个Handler需要MQ
    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 写日志表
     *
     * @param tenantId
     * @param roleId
     */
    private void writeLog(String tenantId, String roleId, String purchaseNo) {
        ErrorLog errorLog = new ErrorLog();
        errorLog.setErrorLogId(UUID.randomUUID().toString());
        errorLog.setLogType(1);
        errorLog.setRequestParam(String.format("tenantId:%s,roleId:%s", tenantId, roleId));
        errorLog.setRequestTime(LocalDateTime.now());
        errorLog.setTenantId(tenantId);
        errorLog.setRequestUrl(purchaseNo);
        errorLogMapper.insert(errorLog);
    }

    /**
     * 给租户赋权
     *
     * @param purchaseNo
     * @param tenantId
     */
    @Override
    public boolean authority(String productCode,String purchaseNo, String tenantId) {
        //授权
        try {
            List<Permission> perList = permissionMapper.getPermissionByPurchaseNo(purchaseNo);
            if(!CollectionUtils.isEmpty(perList)){
                List<String> ids = new ArrayList<>();
                Set<String> perSet = new HashSet<>();
                for(Permission per : perList){
                    ids.add(per.getPermissionId());
                    String[] perIds = per.getPermissionPath().split("/");
                    for(String id : perIds){
                        if(StringUtils.isNotBlank(id)){
                            perSet.add(id);
                        }
                    }
                }
                List<Permission> permissionList = permissionMapper.selectByIds(perSet.toArray(new String[0]));
                // List<String>
                Authority2TenantConfigDto aut = new Authority2TenantConfigDto();
                aut.setTenantId(tenantId);
                aut.setProductCode(productCode);
                aut.setPurchaseNo(purchaseNo);
                aut.setPerList(permissionList.stream()
                    .map(PermissionToPermission2TenantConverter.INSTANCE::to)
                    .collect(Collectors.toList()));
                aut.setIds(ids);
                String json = JSON.toJSONString(aut);
                LOGGER.info("authority Authority2TenantConfigDto :{}",json);
                rabbitTemplate.convertAndSend(Constant.PRODUCT_AUTHORITY_EXCHANGE,Constant.PRODUCT_AUTHORITY_ROUTING_KEY, json);
            }
        } catch (Exception ex) {
            writeLog(tenantId, "", purchaseNo);
            LOGGER.error("The rights assigned to tenants are abnormal productCode:{},purchaseNo:{}, tenantId:{}",productCode,purchaseNo,tenantId, ex);
        }
        return false;
    }

}
