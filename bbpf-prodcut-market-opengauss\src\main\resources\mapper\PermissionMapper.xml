<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.PermissionMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.Permission">
        <id column="permission_id" property="permissionId" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="permission_type" property="permissionType" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="permission_name" property="permissionName" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="permission_code" property="permissionCode" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="parent_name" property="parentName" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="permission_desc" property="permissionDesc" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="order_by" property="orderBy" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result column="permission_image" property="permissionImage" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="permission_level" property="permissionLevel" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result column="sys_type" property="sysType" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result column="routing_url" property="routingUrl" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="has_enable" property="hasEnable" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result column="permission_path" property="permissionPath" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="create_org_id" property="createOrgId" jdbcType="VARCHAR" javaType="java.lang.String"/>
    </resultMap>
    <sql id="Base_Column_List">
    permission_id, permission_type, permission_name, permission_code, parent_name,
    parent_id, permission_desc, order_by, permission_image, create_time, update_time,
    permission_level, sys_type, has_enable, routing_url, create_user_id, create_org_id,permission_path
  </sql>
    <!--批量更新 -->
    <update id="updatePermissionList" parameterType="java.util.List">
        update t_permission
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_by =case" suffix="end,">
                <foreach collection="list" item="item">
                    <if test="item.orderBy!=null">
                        when permission_id=#{item.permissionId} then #{item.orderBy}
                    </if>
                </foreach>
            </trim>
            <trim prefix="parent_id =case" suffix="end,">
                <foreach collection="list" item="item">
                    when permission_id=#{item.permissionId} then #{item.parentId}
                </foreach>
            </trim>
            <trim prefix="permission_path =case" suffix="end,">
                <foreach collection="list" item="item">
                    when permission_id=#{item.permissionId} then #{item.permissionPath}
                </foreach>
            </trim>
            <trim prefix="update_time =case" suffix="end,">
                <foreach collection="list" item="item">
                    when permission_id=#{item.permissionId} then now()
                </foreach>
            </trim>
        </trim>
        <where>
            <foreach collection="list" separator="or" item="item" index="index">
                permission_id = #{item.permissionId}
            </foreach>
        </where>
    </update>
    <update id="updatePermissionPath" parameterType="java.lang.String">
    update t_permission set permission_path=replace(permission_path,#{oldPath},#{newPath}) where permission_path like concat(#{oldPath},'%')
  </update>
    <delete id="deleteByPermissionIds" parameterType="java.lang.String">
        delete from t_permission
        where permission_id in
        <foreach collection="array" item="arr" index="no" open="("
                 separator="," close=")">
            #{arr}
        </foreach>
    </delete>
    <select id="queryMaxOrderByParentId" resultType="java.lang.Integer" parameterType="java.lang.String">
  	SELECT
    IFNULL(max(order_by),0)
    FROM t_permission
    where 1=1 AND parent_id=#{parentId,jdbcType=VARCHAR}
  </select>
    <select id="getPermissionByParentId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_permission
        where 1=1
        <if test="parentId!=null and parentId!=''">
            and parent_id=#{parentId,jdbcType=VARCHAR}
        </if>
        order by order_by
    </select>
    <select id="getAllPermission" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_permission where permission_id !='-1' order by permission_level, order_by
    </select>
    <select id="getAllPermissionContainRoot" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_permission
    </select>
    <select id="getEnableAllPermission" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_permission where permission_id !='-1' and has_enable=1 order by permission_level, order_by
    </select>
    <select id="getAllPermissionWithRoot" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_permission where permission_type!=3 and permission_type!=4
    </select>
    <select id="getAllPermissionById" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_permission
        where permission_path like concat(
        (select permission_path from t_permission where permission_id =#{permissionId,jdbcType=VARCHAR}),'%')
        <if test="serachKey != null and serachKey != ''">
            AND (permission_name like CONCAT('%',#{serachKey,jdbcType=VARCHAR},'%') or
            permission_code like CONCAT('%',#{serachKey,jdbcType=VARCHAR},'%') or
            routing_url like CONCAT('%',#{serachKey,jdbcType=VARCHAR},'%'))
        </if>
        order by permission_level
    </select>
    <select id="selectResourcePermissionListByRoleId" parameterType="java.lang.String" resultType="java.lang.String">
        select DISTINCT tpp.permission_id perid,product_id from t_product_permission tpp
        LEFT JOIN t_permission tp on tpp.permission_id=tp.permission_id
        where has_enable=1
        <if test="sysType != null and sysType != ''">
            AND tp.sys_type = #{sysType,jdbcType=VARCHAR}
        </if>
        and product_id IN
        <foreach collection="productIds" item="pid" index="index"
                 open="(" close=")" separator=",">
            #{pid}
        </foreach>
    </select>
    <select id="selectRoleListByPermissionId" parameterType="java.lang.String" resultType="java.lang.String">
        select product_id from t_product_permission where 1=1
        <if test="permissionId != null and permissionId != ''">
            AND permission_id = #{permissionId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from t_permission
        where permission_id = #{permissionId,jdbcType=VARCHAR}
    </select>
    <select id="selectByIds" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from t_permission
        where permission_id in
        <foreach collection="ids" item="arr" index="no" open="("
                                         separator="," close=")">
        #{arr}
        </foreach>
    </select>
    <select id="selectByPermissionCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from t_permission
        where permission_code = #{permissionCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from t_permission
    where permission_id = #{permissionId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.Permission">
    insert into t_permission (permission_id, permission_type, permission_name,
      permission_code, parent_name, parent_id,
      permission_desc, order_by, permission_image,
      create_time, update_time, permission_level,
      sys_type, has_enable, routing_url,
      create_user_id, create_org_id,permission_path)
    values (#{permissionId,jdbcType=VARCHAR}, #{permissionType,jdbcType=VARCHAR}, #{permissionName,jdbcType=VARCHAR},
      #{permissionCode,jdbcType=VARCHAR}, #{parentName,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR},
      #{permissionDesc,jdbcType=VARCHAR}, #{orderBy,jdbcType=INTEGER}, #{permissionImage,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{permissionLevel,jdbcType=INTEGER},
      #{sysType,jdbcType=INTEGER}, #{hasEnable,jdbcType=INTEGER}, #{routingUrl,jdbcType=VARCHAR},
      #{createUserId,jdbcType=VARCHAR}, #{createOrgId,jdbcType=VARCHAR},#{permissionPath,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.Permission">
        insert into t_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="permissionId != null">
                permission_id,
            </if>
            <if test="permissionType != null">
                permission_type,
            </if>
            <if test="permissionName != null">
                permission_name,
            </if>
            <if test="permissionCode != null">
                permission_code,
            </if>
            <if test="parentName != null">
                parent_name,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="permissionDesc != null">
                permission_desc,
            </if>
            <if test="orderBy != null">
                order_by,
            </if>
            <if test="permissionImage != null">
                permission_image,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="permissionLevel != null">
                permission_level,
            </if>
            <if test="sysType != null">
                sys_type,
            </if>
            <if test="hasEnable != null">
                has_enable,
            </if>
            <if test="routingUrl != null">
                routing_url,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createOrgId != null">
                create_org_id,
            </if>
            <if test="permissionPath != null">
                permission_path,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="permissionId != null">
                #{permissionId,jdbcType=VARCHAR},
            </if>
            <if test="permissionType != null">
                #{permissionType,jdbcType=VARCHAR},
            </if>
            <if test="permissionName != null">
                #{permissionName,jdbcType=VARCHAR},
            </if>
            <if test="permissionCode != null">
                #{permissionCode,jdbcType=VARCHAR},
            </if>
            <if test="parentName != null">
                #{parentName,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="permissionDesc != null">
                #{permissionDesc,jdbcType=VARCHAR},
            </if>
            <if test="orderBy != null">
                #{orderBy,jdbcType=INTEGER},
            </if>
            <if test="permissionImage != null">
                #{permissionImage,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="permissionLevel != null">
                #{permissionLevel,jdbcType=INTEGER},
            </if>
            <if test="sysType != null">
                #{sysType,jdbcType=INTEGER},
            </if>
            <if test="hasEnable != null">
                #{hasEnable,jdbcType=INTEGER},
            </if>
            <if test="routingUrl != null">
                #{routingUrl,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createOrgId != null">
                #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="permissionPath != null">
                #{permissionPath,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.Permission">
        update t_permission
        <set>

            <if test="permissionName != null">
                permission_name = #{permissionName,jdbcType=VARCHAR},
            </if>
            <if test="permissionCode != null">
                permission_code = #{permissionCode,jdbcType=VARCHAR},
            </if>
            <if test="parentName != null">
                parent_name = #{parentName,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null" >
                parent_id = #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="permissionDesc != null">
                permission_desc = #{permissionDesc,jdbcType=VARCHAR},
            </if>
            <if test="orderBy != null">
                order_by = #{orderBy,jdbcType=INTEGER},
            </if>
            <if test="permissionImage != null">
                permission_image = #{permissionImage,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="permissionLevel != null">
                permission_level = #{permissionLevel,jdbcType=INTEGER},
            </if>
            <if test="sysType != null">
                sys_type = #{sysType,jdbcType=INTEGER},
            </if>
            <if test="hasEnable != null">
                has_enable = #{hasEnable,jdbcType=INTEGER},
            </if>
            <if test="routingUrl != null">
                routing_url = #{routingUrl,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createOrgId != null">
                create_org_id = #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="permissionPath != null">
                permission_path = #{permissionPath,jdbcType=VARCHAR},
            </if>
        </set>
        where permission_id = #{permissionId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.bus.product.market.common.entity.Permission">
    update t_permission
    set permission_type = #{permissionType,jdbcType=VARCHAR},
      permission_name = #{permissionName,jdbcType=VARCHAR},
      permission_code = #{permissionCode,jdbcType=VARCHAR},
      parent_name = #{parentName,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=VARCHAR},
      permission_desc = #{permissionDesc,jdbcType=VARCHAR},
      order_by = #{orderBy,jdbcType=INTEGER},
      permission_image = #{permissionImage,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      permission_level = #{permissionLevel,jdbcType=INTEGER},
      sys_type = #{sysType,jdbcType=INTEGER},
      has_enable = #{hasEnable,jdbcType=INTEGER},
      routing_url = #{routingUrl,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_org_id = #{createOrgId,jdbcType=VARCHAR},
      permission_path = #{permissionPath,jdbcType=VARCHAR}
    where permission_id = #{permissionId,jdbcType=VARCHAR}
  </update>

    <select id="getPermissionByPurchaseNo" resultMap="BaseResultMap" parameterType="java.lang.String">
    SELECT
    t.permission_id,t.permission_type, t.permission_name, t.permission_code, t.parent_name,
    t.parent_id, t.permission_desc, t.order_by, t.permission_image,
    t.permission_level,t.permission_path, t.sys_type, t.has_enable, t.routing_url
    FROM
    t_permission t
    left JOIN t_product_permission t1 ON t.permission_id = t1.permission_id

    left JOIN t_product_services t2 ON t1.product_id = t2.product_id

    left JOIN t_product_purchase t3 ON t3.product_id = t2.product_id
    WHERE
    t3.purchase_no = #{purchaseNo,jdbcType=VARCHAR}
  </select>



    <resultMap id="NodePermissionMap" type="com.snbc.bbpf.bus.product.market.common.dto.product.NodePermissionDto">
        <result column="permission_id" property="permissionId" jdbcType="VARCHAR"></result>
        <result column="permission_name" property="permissionName" jdbcType="VARCHAR"></result>
    </resultMap>

    <select id="getNodePermission" resultMap="NodePermissionMap">
      SELECT
      t1.permission_id,
      t1.permission_name
      FROM
      t_permission t1
    where t1.parent_id = #{permissionId,jdbcType=VARCHAR}
      ORDER BY
      t1.order_by
  </select>
    <resultMap id="AllUserPermissionMap" type="com.snbc.bbpf.bus.product.market.common.dto.product.MenuPermissionDto">
        <result column="permission_id" property="permissionId" jdbcType="VARCHAR"></result>
        <result column="permission_name" property="permissionName" jdbcType="VARCHAR"></result>
        <collection property="nodes" column="{permissionId = permission_id}" javaType="java.util.List" select="getNodePermission" ofType="com.snbc.bbpf.bus.product.market.common.dto.product.NodePermissionDto"/>
    </resultMap>
    <select id="getAllPermissionForProductDetil" parameterType="java.util.List" resultMap="AllUserPermissionMap">
         SELECT
            permission_id,
            permission_name
        FROM
            t_permission
        WHERE permission_id in
        <foreach collection="funcationIds" item="funcationIds" index="index" open="(" close=")" separator=",">
            #{funcationIds}
        </foreach>
        ORDER BY
            order_by
  </select>
    <update id="updatePermissionLevel">
    update t_permission set permission_level=length(permission_path)-length(replace(permission_path,'/',''))-1
  </update>

    <select id="getMenuPermission" resultType="com.snbc.bbpf.bus.product.market.common.entity.Permission">
         SELECT
            permission_id as permissionId,
            permission_name as permissionName,
            parent_id as parentId
        FROM
            t_permission
        WHERE
            permission_type = 2
    </select>

</mapper>
