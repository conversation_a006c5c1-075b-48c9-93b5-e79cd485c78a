<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper">
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase">
    <id column="product_purchase_id" jdbcType="VARCHAR" property="productPurchaseId" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo" />
    <result column="product_quantity" jdbcType="INTEGER" property="productQuantity" />
    <result column="product_grade" jdbcType="VARCHAR" property="productGrade" />
    <result column="purchase_time" jdbcType="TIMESTAMP" property="purchaseTime" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="success_time" jdbcType="TIMESTAMP" property="successTime" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="cancel_reson" jdbcType="VARCHAR" property="cancelReson" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="purchase_amount" jdbcType="DECIMAL" property="purchaseAmount" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="purchase_status" jdbcType="INTEGER" property="purchaseStatus" />
    <result column="invoice_status" jdbcType="INTEGER" property="invoiceStatus" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="is_renew" jdbcType="INTEGER" property="isRenew" />
    <result column="product_price" jdbcType="DECIMAL" property="productPrice" />
    <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    <result column="give_username" jdbcType="VARCHAR" property="giveUserName" />
    <result column="give_reason" jdbcType="VARCHAR" property="giveReason" />
    <result column="discountcard_amount" jdbcType="DECIMAL" property="discountcardAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="brankstart_time" jdbcType="TIMESTAMP" property="brankstartTime" />
  </resultMap>
  <sql id="Base_Column_List">
    product_purchase_id, product_id, purchase_no, product_quantity, product_grade, purchase_time,
    pay_time, success_time, cancel_time, cancel_reson, pay_type, purchase_amount, payment_amount,
    discount_amount, purchase_status, invoice_status, tenant_id, user_id, is_renew,product_price,
    tenant_name,give_username,give_reason,discountcard_amount,remark,brankstart_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_product_purchase
    where product_purchase_id = #{productPurchaseId,jdbcType=VARCHAR}
  </select>
  <select id="selectByPurchaseNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_product_purchase
    where purchase_no = #{purchaseNo,jdbcType=VARCHAR}
  </select>
  <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase">
    insert into t_product_purchase (product_purchase_id, product_id, purchase_no,
      product_quantity, product_grade, purchase_time,
      pay_time, success_time, cancel_time,
      cancel_reson, pay_type, purchase_amount,
      payment_amount, discount_amount, purchase_status,
      invoice_status, tenant_id, user_id,
      is_renew,product_price,tenant_name,give_username,give_reason,discountcard_amount,remark,brankstart_time)
    values (#{productPurchaseId,jdbcType=VARCHAR}, #{productId,jdbcType=VARCHAR}, #{purchaseNo,jdbcType=VARCHAR},
      #{productQuantity,jdbcType=INTEGER}, #{productGrade,jdbcType=VARCHAR}, #{purchaseTime,jdbcType=TIMESTAMP},
      #{payTime,jdbcType=TIMESTAMP}, #{successTime,jdbcType=TIMESTAMP}, #{cancelTime,jdbcType=TIMESTAMP},
      #{cancelReson,jdbcType=VARCHAR}, #{payType,jdbcType=INTEGER}, #{purchaseAmount,jdbcType=DECIMAL},
      #{paymentAmount,jdbcType=DECIMAL}, #{discountAmount,jdbcType=DECIMAL}, #{purchaseStatus,jdbcType=INTEGER},
      #{invoiceStatus,jdbcType=INTEGER}, #{tenantId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
      #{isRenew,jdbcType=INTEGER},#{productPrice,jdbcType=DECIMAL},#{tenantName,jdbcType=VARCHAR},
      #{giveUserName,jdbcType=VARCHAR},#{giveReason,jdbcType=VARCHAR},
      #{discountcardAmount,jdbcType=DECIMAL},#{remark,jdbcType=VARCHAR},#{brankstartTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase">
    insert into t_product_purchase
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productPurchaseId != null">
        product_purchase_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="purchaseNo != null">
        purchase_no,
      </if>
      <if test="productQuantity != null">
        product_quantity,
      </if>
      <if test="productGrade != null">
        product_grade,
      </if>
      <if test="purchaseTime != null">
        purchase_time,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="successTime != null">
        success_time,
      </if>
      <if test="cancelTime != null">
        cancel_time,
      </if>
      <if test="cancelReson != null">
        cancel_reson,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="purchaseAmount != null">
        purchase_amount,
      </if>
      <if test="paymentAmount != null">
        payment_amount,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="purchaseStatus != null">
        purchase_status,
      </if>
      <if test="invoiceStatus != null">
        invoice_status,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="isRenew != null">
        is_renew,
      </if>
      <if test="productPrice != null">
        product_price,
      </if>
      <if test="tenantName != null">
        tenant_name,
      </if>
      <if test="giveUserName != null">
        give_username,
      </if>
      <if test="giveReason != null">
        give_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productPurchaseId != null">
        #{productPurchaseId,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseNo != null">
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="productQuantity != null">
        #{productQuantity,jdbcType=INTEGER},
      </if>
      <if test="productGrade != null">
        #{productGrade,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        #{purchaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="successTime != null">
        #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelReson != null">
        #{cancelReson,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="purchaseAmount != null">
        #{purchaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="purchaseStatus != null">
        #{purchaseStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="isRenew != null">
        #{isRenew,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="tenantName != null">
        #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="giveUserName != null">
        #{give_username,jdbcType=VARCHAR},
      </if>
      <if test="giveReason != null">
        #{give_reason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPurchaseNoSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase">
    update t_product_purchase
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productQuantity != null">
        product_quantity = #{productQuantity,jdbcType=INTEGER},
      </if>
      <if test="productGrade != null">
        product_grade = #{productGrade,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        purchase_time = #{purchaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="successTime != null">
        success_time = #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelReson != null">
        cancel_reson = #{cancelReson,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="purchaseAmount != null">
        purchase_amount = #{purchaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="purchaseStatus != null">
        purchase_status = #{purchaseStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceStatus != null">
        invoice_status = #{invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="isRenew != null">
        is_renew = #{isRenew,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        product_price = #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="tenantName != null">
        tenant_name = #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="giveUserName != null">
        give_username=#{giveUserName,jdbcType=VARCHAR},
      </if>
      <if test="giveReason != null">
        give_reason=#{giveReason,jdbcType=VARCHAR}
      </if>
      <if test="brankstartTime != null">
        brankstart_time=#{brankstartTime,jdbcType=TIMESTAMP}
      </if>
    </set>
    where purchase_No = #{purchaseNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase">
    update t_product_purchase
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseNo != null">
        purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="productQuantity != null">
        product_quantity = #{productQuantity,jdbcType=INTEGER},
      </if>
      <if test="productGrade != null">
        product_grade = #{productGrade,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        purchase_time = #{purchaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="successTime != null">
        success_time = #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelReson != null">
        cancel_reson = #{cancelReson,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="purchaseAmount != null">
        purchase_amount = #{purchaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="purchaseStatus != null">
        purchase_status = #{purchaseStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceStatus != null">
        invoice_status = #{invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="isRenew != null">
        is_renew = #{isRenew,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        product_price = #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="tenantName != null">
        tenant_name = #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="giveUserName != null">
        give_username=#{giveUserName,jdbcType=VARCHAR},
      </if>
      <if test="giveReason != null">
        give_reason=#{giveReason,jdbcType=VARCHAR}
      </if>
    </set>
    where product_purchase_id = #{productPurchaseId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase">
    update t_product_purchase
    set product_id = #{productId,jdbcType=VARCHAR},
      purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      product_quantity = #{productQuantity,jdbcType=INTEGER},
      product_grade = #{productGrade,jdbcType=VARCHAR},
      purchase_time = #{purchaseTime,jdbcType=TIMESTAMP},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      success_time = #{successTime,jdbcType=TIMESTAMP},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      cancel_reson = #{cancelReson,jdbcType=VARCHAR},
      pay_type = #{payType,jdbcType=INTEGER},
      purchase_amount = #{purchaseAmount,jdbcType=DECIMAL},
      payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      discount_amount = #{discountAmount,jdbcType=DECIMAL},
      purchase_status = #{purchaseStatus,jdbcType=INTEGER},
      invoice_status = #{invoiceStatus,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      is_renew = #{isRenew,jdbcType=INTEGER},
      product_price = #{productPrice,jdbcType=DECIMAL},
      tenant_name = #{tenantName,jdbcType=VARCHAR},
      give_username=#{giveUserName,jdbcType=VARCHAR},
      give_reason=#{giveReason,jdbcType=VARCHAR}
    where product_purchase_id = #{productPurchaseId,jdbcType=VARCHAR}
  </update>

  <update id="updatePurchaseInvoiceStatus">
    <foreach collection="productPurchaseIdList" item="productPurchaseId" index="index" open="" close="" separator=";">
      update t_product_purchase
      <set>
        invoice_status = #{invoiceStatus}
      </set>
      where product_purchase_id = #{productPurchaseId}
    </foreach>
  </update>

  <!--查询订单金额信息-->
  <select id="selectNotIssued" resultType="java.math.BigDecimal">
    SELECT
      payment_amount as paymentAmount
      FROM
      t_product_purchase
    WHERE
    1 = 1
    and invoice_status = #{invoiceStatus}
    <!--已支付或已购买成功的订单-->
    AND (purchase_status = 1 OR purchase_status = 2)
    and product_purchase_id in
    <foreach collection="productPurchaseIdList" item="productPurchaseIdList" index="index" open="(" close=")" separator=",">
      #{productPurchaseIdList}
    </foreach>
  </select>
  <select id="selectByTenantAndProductIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM t_product_purchase WHERE  tenant_id = #{tenantId} AND product_id IN
    <foreach collection="productIds" item="item" index="index" open = "(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
  <update id="updatePurchaseByInvoiceId">
      UPDATE t_product_purchase t1 JOIN t_invoice_purchase t2
      SET invoice_status = 2
      WHERE
          t1.product_purchase_id = t2.product_purchase_id
      AND t2.invoice_apply_id = #{invoiceApplyId,jdbcType=VARCHAR}
  </update>
</mapper>