package com.snbc.bbpf.bus.product.market.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
/**
 *线下交易表类型实体类
 */
public class PurchaseOffline {
    /**
     * 编号
     */
    private String offlineId;
    /**
     * 购买支付id
     */
    private String purchaseNo;
    /**
     * 汇款编号
     */
    private String remitTradeNo;
    /**
     * 汇款银行名称
     */
    private String remitBankName;
    /**
     * 汇款账号
     */
    private String remitBankNo;
    /**
     * 汇款凭证
     */
    private String remitProof;
    /**
     * 汇款时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime remitTime;
    /**
     * 确认汇款时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmRemitTime;

    public String getOfflineId() {
        return offlineId;
    }

    public void setOfflineId(String offlineId) {
        this.offlineId = offlineId == null ? null : offlineId.trim();
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }
    /**
     * 设置 productPurchaseId
     * @param purchaseNo
     */
    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo == null ? null : purchaseNo.trim();
    }

    public String getRemitTradeNo() {
        return remitTradeNo;
    }
    /**
     * 设置 remitTradeNo
     * @param remitTradeNo
     */
    public void setRemitTradeNo(String remitTradeNo) {
        this.remitTradeNo = remitTradeNo == null ? null : remitTradeNo.trim();
    }

    public String getRemitBankName() {
        return remitBankName;
    }
    /**
     * 设置 remitBankName
     * @param remitBankName
     */
    public void setRemitBankName(String remitBankName) {
        this.remitBankName = remitBankName == null ? null : remitBankName.trim();
    }

    public String getRemitBankNo() {
        return remitBankNo;
    }
    /**
     * 设置 remitBankNo
     * @param remitBankNo
     */
    public void setRemitBankNo(String remitBankNo) {
        this.remitBankNo = remitBankNo == null ? null : remitBankNo.trim();
    }

    public String getRemitProof() {
        return remitProof;
    }
    /**
     * 设置 remitProof
     * @param remitProof
     */
    public void setRemitProof(String remitProof) {
        this.remitProof = remitProof == null ? null : remitProof.trim();
    }

    public LocalDateTime getRemitTime() {
        return remitTime;
    }
    /**
     * 设置 remitTime
     * @param remitTime
     */
    public void setRemitTime(LocalDateTime remitTime) {
        this.remitTime = remitTime;
    }

    public LocalDateTime getConfirmRemitTime() {
        return confirmRemitTime;
    }
    /**
     * 设置 confirmRemitTime
     * @param confirmRemitTime
     */
    public void setConfirmRemitTime(LocalDateTime confirmRemitTime) {
        this.confirmRemitTime = confirmRemitTime;
    }
}
