package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.market.common.entity.Message;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PutMapping;

/**
 * @ClassName: MessageService
 * @Description: 调用消息中心服务发送消息
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/6/9
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@FeignClient(value = "bbpf-message-center")
public interface MessageService {

    @PutMapping("/console/msg/sendTemplateMsg")
    CommonResp sendMsg(Message message);
}
