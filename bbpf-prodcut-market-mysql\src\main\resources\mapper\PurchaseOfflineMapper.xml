<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.PurchaseOfflineMapper">
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline">
    <id column="offline_id" jdbcType="VARCHAR" property="offlineId" />
    <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo" />
    <result column="remit_trade_no" jdbcType="VARCHAR" property="remitTradeNo" />
    <result column="remit_bank_name" jdbcType="VARCHAR" property="remitBankName" />
    <result column="remit_bank_no" jdbcType="VARCHAR" property="remitBankNo" />
    <result column="remit_proof" jdbcType="VARCHAR" property="remitProof" />
    <result column="remit_time" jdbcType="TIMESTAMP" property="remitTime" />
    <result column="confirm_remit_time" jdbcType="TIMESTAMP" property="confirmRemitTime" />
  </resultMap>
  <sql id="Base_Column_List">
    offline_id, purchase_no, remit_trade_no, remit_bank_name, remit_bank_no,
    remit_proof, remit_time, confirm_remit_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_purchase_offline
        where offline_id = #{offlineId,jdbcType=VARCHAR}
    </select>
    <select id="selectByPurchaseNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_purchase_offline
        where purchase_no = #{purchaseNo,jdbcType=VARCHAR}
    </select>
  <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline">
    insert into t_purchase_offline (offline_id, purchase_no, remit_trade_no,
      remit_bank_name, remit_bank_no, remit_proof,
      remit_time, confirm_remit_time)
    values (#{offlineId,jdbcType=VARCHAR}, #{purchaseNo,jdbcType=VARCHAR}, #{remitTradeNo,jdbcType=VARCHAR},
      #{remitBankName,jdbcType=VARCHAR}, #{remitBankNo,jdbcType=VARCHAR}, #{remitProof,jdbcType=VARCHAR},
      #{remitTime,jdbcType=TIMESTAMP}, #{confirmRemitTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline">
    insert into t_purchase_offline
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="offlineId != null">
        offline_id,
      </if>
      <if test="purchaseNo != null">
        purchase_no,
      </if>
      <if test="remitTradeNo != null">
        remit_trade_no,
      </if>
      <if test="remitBankName != null">
        remit_bank_name,
      </if>
      <if test="remitBankNo != null">
        remit_bank_no,
      </if>
      <if test="remitProof != null">
        remit_proof,
      </if>
      <if test="remitTime != null">
        remit_time,
      </if>
      <if test="confirmRemitTime != null">
        confirm_remit_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="offlineId != null">
        #{offlineId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseNo != null">
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="remitTradeNo != null">
        #{remitTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="remitBankName != null">
        #{remitBankName,jdbcType=VARCHAR},
      </if>
      <if test="remitBankNo != null">
        #{remitBankNo,jdbcType=VARCHAR},
      </if>
      <if test="remitProof != null">
        #{remitProof,jdbcType=VARCHAR},
      </if>
      <if test="remitTime != null">
        #{remitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmRemitTime != null">
        #{confirmRemitTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline">
    update t_purchase_offline
    <set>
      <if test="purchaseNo != null">
        purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="remitTradeNo != null">
        remit_trade_no = #{remitTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="remitBankName != null">
        remit_bank_name = #{remitBankName,jdbcType=VARCHAR},
      </if>
      <if test="remitBankNo != null">
        remit_bank_no = #{remitBankNo,jdbcType=VARCHAR},
      </if>
      <if test="remitProof != null">
        remit_proof = #{remitProof,jdbcType=VARCHAR},
      </if>
      <if test="remitTime != null">
        remit_time = #{remitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmRemitTime != null">
        confirm_remit_time = #{confirmRemitTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where offline_id = #{offlineId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline">
    update t_purchase_offline
    set purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      remit_trade_no = #{remitTradeNo,jdbcType=VARCHAR},
      remit_bank_name = #{remitBankName,jdbcType=VARCHAR},
      remit_bank_no = #{remitBankNo,jdbcType=VARCHAR},
      remit_proof = #{remitProof,jdbcType=VARCHAR},
      remit_time = #{remitTime,jdbcType=TIMESTAMP},
      confirm_remit_time = #{confirmRemitTime,jdbcType=TIMESTAMP}
    where offline_id = #{offlineId,jdbcType=VARCHAR}
  </update>
</mapper>