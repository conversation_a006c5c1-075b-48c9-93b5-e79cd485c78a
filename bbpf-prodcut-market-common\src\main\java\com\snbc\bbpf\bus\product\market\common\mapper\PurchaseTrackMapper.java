package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PurchaseTrackMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(PurchaseTrack purchaseTrack);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(PurchaseTrack purchaseTrack);
}
