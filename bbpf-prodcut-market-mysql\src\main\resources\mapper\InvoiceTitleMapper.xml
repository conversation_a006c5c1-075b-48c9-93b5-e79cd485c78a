<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.InvoiceTitleMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.InvoiceTitle">
        <id column="invoice_title_id" jdbcType="VARCHAR" property="invoiceTitleId"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="apply_type" jdbcType="INTEGER" property="applyType"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="tax_register_no" jdbcType="VARCHAR" property="taxRegisterNo"/>
        <result column="bank_name" jdbcType="VARCHAR" property="bankName"/>
        <result column="bank_no" jdbcType="VARCHAR" property="bankNo"/>
        <result column="register_address" jdbcType="VARCHAR" property="registerAddress"/>
        <result column="register_phonenum" jdbcType="VARCHAR" property="registerPhonenum"/>
        <result column="receiver_name" jdbcType="VARCHAR" property="receiverName"/>
        <result column="receiver_tel" jdbcType="VARCHAR" property="receiverTel"/>
        <result column="receiver_address" jdbcType="VARCHAR" property="receiverAddress"/>
        <result column="mail" jdbcType="VARCHAR" property="mail"/>
    </resultMap>
    <sql id="Base_Column_List">
    invoice_title_id, tenant_id, apply_type, company_name, tax_register_no, bank_name,
    bank_no, register_address, register_phonenum, mail, receiver_address, receiver_name, receiver_tel
  </sql>
    <insert id="insertInvoiceTitle" parameterType="com.snbc.bbpf.bus.product.market.common.entity.InvoiceTitle">
        insert into t_invoice_title
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceTitleId != null">
                invoice_title_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="applyType != null">
                apply_type,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="taxRegisterNo != null">
                tax_register_no,
            </if>
            <if test="bankName != null">
                bank_name,
            </if>
            <if test="bankNo != null">
                bank_no,
            </if>
            <if test="registerAddress != null">
                register_address,
            </if>
            <if test="registerPhonenum != null">
                register_phonenum,
            </if>
            <if test="mail != null">
                mail,
            </if>
            <if test="receiverAddress != null and receiverAddress !=''">
                receiver_address,
            </if>
            <if test="receiverName != null and receiverName !=''">
                receiver_name,
            </if>
            <if test="receiverTel != null and receiverTel !=''">
                receiver_tel,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceTitleId != null">
                #{invoiceTitleId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="applyType != null">
                #{applyType,jdbcType=INTEGER},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="taxRegisterNo != null">
                #{taxRegisterNo,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="bankNo != null">
                #{bankNo,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null">
                #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerPhonenum != null">
                #{registerPhonenum,jdbcType=VARCHAR},
            </if>
            <if test="mail != null">
                #{mail,jdbcType=VARCHAR},
            </if>
            <if test="receiverAddress != null and receiverAddress !=''">
                #{receiverAddress,jdbcType=VARCHAR},
            </if>
            <if test="receiverName != null and receiverName !=''">
                #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="receiverTel != null and receiverTel !=''">
                #{receiverTel,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <select id="selectInvoiceByTenantId" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(0) from t_invoice_title where tenant_id = #{tenantId,jdbcType=VARCHAR}
    </select>

    <select id="getInvoiceTitleByTenantId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from t_invoice_title
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
    </select>

    <update id="updateInvoiceTit" parameterType="com.snbc.bbpf.bus.product.market.common.entity.InvoiceTitle">
        update t_invoice_title
        <set>
            <trim suffixOverrides=",">
                <if test="applyType != null">
                    apply_type = #{applyType,jdbcType=INTEGER},
                </if>
                <if test="companyName != null">
                    company_name = #{companyName,jdbcType=VARCHAR},
                </if>
                <if test="taxRegisterNo != null">
                    tax_register_no = #{taxRegisterNo,jdbcType=VARCHAR},
                </if>
                <if test="bankName != null">
                    bank_name = #{bankName,jdbcType=VARCHAR},
                </if>
                <if test="bankNo != null">
                    bank_no = #{bankNo,jdbcType=VARCHAR},
                </if>
                <if test="registerAddress != null">
                    register_address = #{registerAddress,jdbcType=VARCHAR},
                </if>
                <if test="registerPhonenum != null">
                    register_phonenum = #{registerPhonenum,jdbcType=VARCHAR},
                </if>
                <if test="mail != null">
                    mail = #{mail,jdbcType=VARCHAR},
                </if>
                <if test="receiverAddress != null and receiverAddress !=''">
                    receiver_address = #{receiverAddress,jdbcType=VARCHAR},
                </if>
                <if test="receiverName != null and receiverName !=''">
                    receiver_name = #{receiverName,jdbcType=VARCHAR},
                </if>
                <if test="receiverTel != null and receiverTel !=''">
                    receiver_tel = #{receiverTel,jdbcType=VARCHAR}
                </if>
            </trim>
            where tenant_id = #{tenantId,jdbcType=VARCHAR}
        </set>
    </update>
</mapper>