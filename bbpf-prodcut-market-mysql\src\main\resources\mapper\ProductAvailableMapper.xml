<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductAvailableMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.ProductAvailable">
        <id column="available_id" jdbcType="VARCHAR" property="availableId" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="vems_id" jdbcType="VARCHAR" property="vemsId" />
        <result column="product_id" jdbcType="VARCHAR" property="productId" />
        <result column="vems_name" jdbcType="VARCHAR" property="vemsName" />
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
        <result column="due_time" jdbcType="TIMESTAMP" property="dueTime" />
        <result column="available_value" jdbcType="DECIMAL" property="availableValue" />
        <result column="available_unit" jdbcType="VARCHAR" property="availableUnit" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="activation_time" jdbcType="TIMESTAMP" property="activationTime" />
        <result column="is_renew" jdbcType="INTEGER" property="isRenew" />
    </resultMap>

    <sql id="Base_Column_List">
    available_id, tenant_id, vems_id, product_id, vems_name, begin_time,
    due_time, available_value, available_unit, create_time, update_time,activation_time,is_renew
  </sql>
    <sql id="Base_Column_List1">
    available_id, tenant_id, vems_id, tpa.product_id, vems_name, begin_time,
    due_time, available_value, available_unit, tps.create_time, tps.update_time,tps.activation_time,tps.is_renew
  </sql>
     <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_product_available
        where available_id = #{availableId,jdbcType=VARCHAR}
    </select>
    <select id="selectByVemsIds"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_product_available
        where vems_id in
        <foreach  item="item" collection="vemsIds" index="index"  open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="tenantId != null">
            and tenant_id = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="productId != null">
            and product_id = #{productId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectByTenant" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_product_available
        <where>
            <if test="tenantId != null">
                and tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="productId != null">
                and product_id = #{productId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectByTenantAndProductId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_product_available
        where  1=1
        <if test="tenantId != null">
            and tenant_id = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="vemsId != null">
            and vems_id = #{vemsId,jdbcType=VARCHAR}
        </if>
        <if test="productId != null">
            and product_id = #{productId,jdbcType=VARCHAR}
        </if>
    </select>
    <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductAvailable">
    insert into t_product_available (available_id, tenant_id,
      vems_id, product_id, vems_name,
      begin_time, due_time, available_value,
      available_unit, create_time, update_time,activation_time,is_renew
      )
    values (#{availableId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR},
      #{vemsId,jdbcType=VARCHAR}, #{productId,jdbcType=VARCHAR}, #{vemsName,jdbcType=VARCHAR},
      #{beginTime,jdbcType=TIMESTAMP}, #{dueTime,jdbcType=TIMESTAMP}, #{availableValue,jdbcType=DECIMAL},
      #{availableUnit,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      , #{activationTime,jdbcType=TIMESTAMP},#{isRenew,jdbcType=INTEGER}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductAvailable">
        insert into t_product_available
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="availableId != null">
                available_id,
            </if>
            <if test="productPurchaseId != null">
                product_purchase_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="vemsId != null">
                vems_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="vemsName != null">
                vems_name,
            </if>
            <if test="beginTime != null">
                begin_time,
            </if>
            <if test="dueTime != null">
                due_time,
            </if>
            <if test="availableValue != null">
                available_value,
            </if>
            <if test="availableUnit != null">
                available_unit,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="activationTime!=null">
                activation_time,
            </if>
            <if test="isRenew!=null">
                is_renew,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="availableId != null">
                #{availableId,jdbcType=VARCHAR},
            </if>
            <if test="productPurchaseId != null">
                #{productPurchaseId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="vemsId != null">
                #{vemsId,jdbcType=VARCHAR},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=VARCHAR},
            </if>
            <if test="vemsName != null">
                #{vemsName,jdbcType=VARCHAR},
            </if>
            <if test="beginTime != null">
                #{beginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dueTime != null">
                #{dueTime,jdbcType=TIMESTAMP},
            </if>
            <if test="availableValue != null">
                #{availableValue,jdbcType=DECIMAL},
            </if>
            <if test="availableUnit != null">
                #{availableUnit,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="activationTime != null">
                #{acitvationTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isRenew!=null">
                #{isRenew,jdbcType=INTEGER}
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductAvailable">
        update t_product_available
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="vemsId != null">
                vems_id = #{vemsId,jdbcType=VARCHAR},
            </if>
            <if test="productId != null">
                product_id = #{productId,jdbcType=VARCHAR},
            </if>
            <if test="vemsName != null">
                vems_name = #{vemsName,jdbcType=VARCHAR},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dueTime != null">
                due_time = #{dueTime,jdbcType=TIMESTAMP},
            </if>
            <if test="availableValue != null">
                available_value = #{availableValue,jdbcType=DECIMAL},
            </if>
            <if test="availableUnit != null">
                available_unit = #{availableUnit,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="activationTime != null">
                activation_time = #{activationTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isRenew!=null">
                is_renew = #{isRenew,jdbcType=INTEGER}
            </if>
        </set>
        where available_id = #{availableId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductAvailable">
    update t_product_available
    set  tenant_id = #{tenantId,jdbcType=VARCHAR},
      vems_id = #{vemsId,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=VARCHAR},
      vems_name = #{vemsName,jdbcType=VARCHAR},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      due_time = #{dueTime,jdbcType=TIMESTAMP},
      available_value = #{availableValue,jdbcType=DECIMAL},
      available_unit = #{availableUnit,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      activation_time = #{activationTime,jdbcType=TIMESTAMP},
      is_renew = #{isRenew,jdbcType=INTEGER}
    where available_id = #{availableId,jdbcType=VARCHAR}
  </update>

    <select id="vemsWithPage" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.VemsQuery" resultType="com.snbc.bbpf.bus.product.market.common.dto.product.PVemsDto">
        SELECT
            vems_id AS vemsId,
            vems_name AS vemsName,
            due_time AS serviceExpTime
        FROM
            t_product_available
        where 1=1
        <if test="serviceExpStartTime != null and serviceExpStartTime != ''">
            and due_time <![CDATA[ >= ]]> #{serviceExpStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="serviceExpEndTime != null and serviceExpEndTime != ''">
            and due_time <![CDATA[ <= ]]> #{serviceExpEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="vemsName != null and vemsName !=''">
            and vems_name like CONCAT('%',#{vemName,jdbcType=VARCHAR},'%')
        </if>
        <if test="tenantId != null and tenantId !=''">
            and tenant_id = #{tenantId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectDueTimeByVemIds" parameterType="java.lang.String" resultType="com.snbc.bbpf.bus.product.market.common.dto.product.PVemsDto">
         SELECT DISTINCT
            vems_id as vemsId,
            due_time AS serviceExpTime
         FROM
            t_product_available
         where 1=1
        and product_id = #{productId,jdbcType=VARCHAR}
        and vems_id in
        <foreach collection="vemIds" item = "vemIds" index="index" open="(" close=")" separator=",">
            #{vemIds}
        </foreach>
    </select>
    <select id="getProductNameById" parameterType="java.lang.String" resultType="java.lang.String">
      SELECT product_name from t_product_services where product_id=#{productId,jdbcType=VARCHAR}
    </select>
    <select id="queryOutTimeProductByGrade" resultMap="BaseResultMap">
        SELECT DISTINCT <include refid="Base_Column_List" />
        FROM t_product_available where available_unit=#{grade,jdbcType=VARCHAR}
        <choose>
            <when test='grade=="天/台"'>
                and TIMESTAMPDIFF(DAY, now(),due_time) <![CDATA[ <= ]]> #{threshold,jdbcType=INTEGER}
            </when>
            <when test='grade=="天"'>
                and TIMESTAMPDIFF(DAY, now(),due_time) <![CDATA[ <= ]]> #{threshold,jdbcType=INTEGER}
            </when>
             <otherwise>
                and available_value<![CDATA[ <= ]]> #{threshold,jdbcType=INTEGER}
             </otherwise>
        </choose>
    </select>
    <select id="queryOutTimeProduct" resultMap="BaseResultMap">
        SELECT DISTINCT <include refid="Base_Column_List1" />
        FROM t_product_available tpa LEFT JOIN t_product_services tps
        on tpa.product_id=tps.product_id
        WHERE TIMESTAMPDIFF(DAY, now(),tpa.due_time) <![CDATA[ < ]]> #{days,jdbcType=INTEGER}
        <if test="code != null and code.size() > 0">
            and tps.product_code in
            <foreach collection="code" item = "item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>






    <select id="selectseviceAvailableList"
            parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductAvailablePageQueryBase"
            resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ProductAvailablePageDto">
        select
        tpa.available_id availableId,
        tpa.tenant_id tenantId,
        tpa.product_id productId,
        tps.product_code productCode,
        tps.product_name productName,
        tpt.value_name productTypeName,
        tct.value_name chargeTypeName,
        tpa.begin_time beginTime,
        tpa.due_time dueTime,
        tpa.available_value availableValue,
        tpa.available_unit availableUnit,
        tpa.is_renew isRenew,
        tps.product_image productImage
        from    t_product_available tpa
        left join t_product_services tps  on tpa.product_id=tps.product_id
        left join (select value_code,value_name from t_dict_value  where type_code='product_type') tpt on tps.product_type=tpt.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='charge_type') tct on tps.charge_type=tct.value_code

        where 1=1
        <if test="availableId != null and availableId != ''">
            and tpa.available_id = #{availableId,jdbcType = VARCHAR}
        </if>
        <if test="tenantId != null and tenantId != ''">
            and tpa.tenant_id = #{tenantId,jdbcType = VARCHAR}
        </if>
        <if test = "productType != null">
            and tps.product_type = #{productType,jdbcType=INTEGER}
        </if>
        <if test = "chargeType != null">
            and tps.charge_type = #{chargeType,jdbcType=INTEGER}
        </if>
            order by tpa.create_time desc
    </select>
    <select id="selectseviceAvailable" parameterType="java.lang.String" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ProductAvailablePageDto">
        select
        tpa.available_id availableId,
        tpa.tenant_id tenantId,
        tpa.product_id productId,
        tps.product_code productCode,
        tps.product_name productName,
        tpt.value_name productTypeName,
        tct.value_name chargeTypeName,
        tpa.begin_time beginTime,
        tpa.due_time dueTime,
        tpa.available_value availableValue,
        tpa.available_unit availableUnit,
        tpa.is_renew isRenew,
        tps.product_entrance productEntrance
        from    t_product_available tpa
        left join t_product_services tps  on tpa.product_id=tps.product_id
        left join (select value_code,value_name from t_dict_value  where type_code='product_type') tpt on tps.product_type=tpt.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='charge_type') tct on tps.charge_type=tct.value_code
        where 1=1
        <if test="availableId != null and availableId != ''">
            and tpa.available_id = #{availableId,jdbcType = VARCHAR}
        </if>
        <if test="tenantId != null and tenantId != ''">
            and tpa.tenant_id = #{tenantId,jdbcType = VARCHAR}
        </if>
        order by tpa.create_time desc
    </select>
</mapper>