<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<classPathEntry
			location="C:\java\maven_repository\mysql\mysql-connector-java\8.0.23\mysql-connector-java-8.0.23.jar"/>

	<context id="mysqlTables" targetRuntime="MyBatis3">
		<commentGenerator>
			<property name="suppressDate" value="true" />
			<!-- 是否去除自动生成的注释 true：是 ： false:否 -->
			<property name="suppressAllComments" value="true" />
		</commentGenerator>
		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
			connectionURL="***************************************************************"
						userId="root"
						password="">
			<property name="remarkable" value="true"></property>
		</jdbcConnection>

		<!-- 指定生成的类型为java类型，避免数据库中number等类型字段 -->
		<javaTypeResolver>
			<property name="forceBigDecimals" value="false" />
		</javaTypeResolver>

		<!-- 生成model模型，对应的包，存放位置可以指定具体的路径,如/ProjectName/src，也可以使用MAVEN来自动生成 -->
		<javaModelGenerator
			targetPackage="com.snbc.bbpf.bus.product.market.common.entity"
			targetProject="src/main/java">
			<property name="enableSubPackages" value="true" />
			<property name="trimStrings" value="true" />
		</javaModelGenerator>

		<!--对应的xml mapper文件 -->
		<sqlMapGenerator targetPackage="META-INF/sqlmap/"
			targetProject="src/main/resources">
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>

		<!-- 对应的dao接口 -->
		<javaClientGenerator type="XMLMAPPER"
			targetPackage="com.snbc.bbpf.bus.product.market.common.mapper"
			targetProject="src/main/java">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>

		<!-- 数据表 -->
		<!--<table schema="mybatis" tableName="t_product_services"
			domainObjectName="ProductServices" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_product_usage"
			domainObjectName="ProductUsage" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_purchase_coupon"
			domainObjectName="PurchaseCoupon" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_purchase_day_report"
			domainObjectName="PurchaseDayReport" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_purchase_month_report"
			domainObjectName="PurchaseMonthReport" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_purchase_flow"
			domainObjectName="PurchaseFlow" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_purchase_offline"
			domainObjectName="PurchaseOffline" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_purchase_online"
			domainObjectName="PurchaseOnline" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_product_refund"
			domainObjectName="ProductRefund" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_product_purchase"
			domainObjectName="ProductPurchase" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_product_grade"
			domainObjectName="ProductGrade" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_product_available"
			domainObjectName="ProductAvailable" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_notice_record"
			domainObjectName="NoticeRecord" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_invoice_title"
			domainObjectName="InvoiceTitle" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_invoice_purchase"
			domainObjectName="InvoicePurchase" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_invoice_delivery"
			domainObjectName="InvoiceDeliveryService" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_invoice_apply"
			domainObjectName="InvoiceApply" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_due_notice_rate"
			domainObjectName="DueNoticeRate" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>
		<table schema="mybatis" tableName="t_error_log"
			domainObjectName="ErrorLog" enableCountByExample="true"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true"></table>-->
		<table schema="mybatis" tableName="t_product_detail"
			   domainObjectName="ProductDetail" enableCountByExample="false"
			   enableUpdateByExample="false" enableDeleteByExample="false"
			   enableSelectByExample="false" selectByExampleQueryId="false"
			   enableSelectByPrimaryKey="true"
			   enableUpdateByPrimaryKey="true"
			   enableDeleteByPrimaryKey="true"
		></table>
	</context>
</generatorConfiguration>  
