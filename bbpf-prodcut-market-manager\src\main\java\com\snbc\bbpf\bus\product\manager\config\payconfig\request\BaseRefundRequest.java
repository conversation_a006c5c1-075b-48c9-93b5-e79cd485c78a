package com.snbc.bbpf.bus.product.manager.config.payconfig.request;

import com.alipay.api.domain.OpenApiRoyaltyDetailInfoPojo;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020-10-13 14:10
 */
@Data
public class BaseRefundRequest {
    /**
     * 订单退款币种信息
     */
    
    private String refundfeetype;
    /**
     * 退款的原因说明
     */
    
    private String refundReason;
    /**
     * 退分账明细信息
     */
    
    
    private List<OpenApiRoyaltyDetailInfoPojo> refundRoyaltyParameters;
    /**
     * 商户的门店编号
     */
    
    private String storeId;
    /**
     * 商户的终端编号
     */
    
    private String terminalId;
    /**
     * 微信分配的子商户公众账号ID
     * 否	String(32)	wx8888888888888888
     */
    
    private String subAppid;
    /**
     * 微信支付分配的子商户号
     * 是	String(32)	**********
     */
    
    private String subMchId;
    /**
     * 退款资金来源
     * 否	String(30)
     */
    
    private String refundAccount;
    /**
     * 退款结果通知url
     * notify_url	否	String(256)	https://weixin.qq.com/notify/
     异步接收微信支付退款结果通知的回调地址，通知URL必须为外网可访问的url，不允许带参数
     */
    
    private String notifyUrl;

    private String extendData;
    /**
     * 	福建-银联参数
     */
    // 授权
    private String authorization;
    // 商户号 字符串 是
    private String merchantCode;
    // 终端号 字符串 是
    private String terminalCode;
    // 商户订单号
    private String merchantOrderId;
    // 银商订单号
    private String originalOrderId;
    // 退款请求标识
    private String refundRequestId;
    // 退货金额
    private String transactionAmount;
}

