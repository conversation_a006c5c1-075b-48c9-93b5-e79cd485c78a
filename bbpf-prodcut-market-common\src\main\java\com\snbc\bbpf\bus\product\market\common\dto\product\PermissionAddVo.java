/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * @ClassName: PermissionVo
 * PermissionVo对象
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionAddVo {
    /**
     * 权限id
     */
    private String permissionId;
    @NotBlank(message = "功能类型不能为空")
    private String permissionType;
    @NotBlank(message = "功能名称不能为空")
    private String permissionName;
    @NotBlank(message = "功能编码不能为空")
    private String permissionCode;
    @NotBlank(message = "父功能ID不能为空")
    private String parentId;

    private Integer orderBy;
    @NotBlank(message = "功能图标不能为空")
    private String permissionIcon;
    @NotBlank(message = "启用状态不能为空")
    private Integer hasEnable;
    /**
     * 路由地址
     */
    private String routingUrl;

    private String parentName;

    private String remarks;

    private Integer permissionLevel;

    private Integer sysType;

    @Override
    public String toString() {
        return "PermissionAddVo{" +
                "permissionId='" + permissionId + '\'' +
                ", permissionType='" + permissionType + '\'' +
                ", permissionName='" + permissionName + '\'' +
                ", permissionCode='" + permissionCode + '\'' +
                ", parentId='" + parentId + '\'' +
                ", orderBy=" + orderBy +
                ", permissionIcon='" + permissionIcon + '\'' +
                ", hasEnable=" + hasEnable +
                ", routingUrl='" + routingUrl + '\'' +
                ", parentName='" + parentName + '\'' +
                ", remarks='" + remarks + '\'' +
                ", permissionLevel=" + permissionLevel +
                ", sysType=" + sysType +
                '}';
    }
}
