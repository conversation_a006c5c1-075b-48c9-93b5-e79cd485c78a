package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoceExpressDeliDto;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: InvoiceDeliveryService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票快递处理
 * @Author: wangsong
 * @CreateDate: 2020/10/27 18:20
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/27 18:20
 */
public interface InvoiceDeliveryService {
    InvoceExpressDeliDto logisticsDetail(String invoiceApplyId);
}
