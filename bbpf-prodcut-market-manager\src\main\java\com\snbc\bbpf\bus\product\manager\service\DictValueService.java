package com.snbc.bbpf.bus.product.manager.service;


import com.snbc.bbpf.bus.product.market.common.dto.dict.DictProductDto;
import com.snbc.bbpf.bus.product.market.common.dto.dict.DictValueDto;
import com.snbc.bbpf.bus.product.market.common.entity.DictValue;

import java.util.List;
import java.util.Map;


public interface DictValueService {

    /**
     * 添加系统字典数值
     *
     * @param dictValue
     * @return
     * @throws Exception
     */
    int insertDictValue(DictValue dictValue) throws Exception;

    /**
     * 修改系统字典数值信息
     *
     * @param dictValue
     * @return
     * @throws Exception
     */
    int updateDictValue(DictValue dictValue) throws Exception;

    /**
     * 删除系统字典数值信息
     *
     * @param dictValueId
     * @return
     * @throws Exception
     */
    int deleteDictValue(int dictValueId) throws Exception;


    /**
     * 根据字典数值ID查询基本信息
     *
     * @param valueId
     * @return
     * @throws Exception
     */
    DictValue selectByPrimary(Integer valueId) throws Exception;

    /**
     * 获取所有数据字典值
     *
     * @return
     * @throws Exception
     */
    List<DictValue> getAll() throws Exception;

    /**
     * 根据parentId数据字典值
     *
     * @return
     * @throws Exception
     */
    List<DictValue> getDictValueByParentId(Integer parentId) throws Exception;

    /**
     * 根据字典类型和字典值查询字典数值表记录
     *
     * @param map map字典类型typeCode和字典值valueCode
     * @return
     * @throws Exception
     */
    DictValue selectDictValueByCondition(Map<String, String> map) throws Exception;


    /**
     * 根据typeCode查找字典值列表
     *
     * @param typeCode
     * @return
     * @throws Exception
     */
    List<DictValue> queryDictValueByTypeCode(String typeCode) throws Exception;

    /**
     * 根据map获取dictvalueList
     *
     * @param map
     * @return
     */
    List<DictValue> getDictValueByMap(Map<String, Object> map);

    /**
     * 根据typeCode 和 valuecode获取valueName
     *
     * @param dictTypeCode
     * @param dictValueCode
     * @return
     */
    String getDictValueByTypeCodeAndValueCode(String dictTypeCode, String dictValueCode);

    /**
     * 根据typeCode查找字典值列表
     *
     * @param typeCode
     * @return
     * @throws Exception
     */
    List<DictValue> selectDictValueByTypeCode(String typeCode) throws Exception;

    DictProductDto dictInfoForProductList();

    Map<String, List<DictValueDto>> getMultipleDictValues(List<String> dictTypeCodes);
}
