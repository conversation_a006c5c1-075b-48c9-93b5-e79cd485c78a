package com.snbc.bbpf.bus.product.manager.config.payconfig;


import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;

/**
 * 功能描述: <br>
 * 〈详情列表〉
 *
 * @param: null
 * @return:
 * @exception
 * @author: gs
 * @date: 2019-08-09 10:33
 */

@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-04-26T10:08:21.632Z")

public class GoodsDetailList extends ArrayList<GoodsDetailDto> {

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

