/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: UserSimpleOrg
 * @Description: 用户组织参数机构
 * @module: si-bbpf-system
 * @Author: liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserSimpleOrg {
    private String orgId;
    private String orgName;
}
