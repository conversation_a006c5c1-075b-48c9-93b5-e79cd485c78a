<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductGradeMapper">
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.ProductGrade">
    <id column="product_grade_id" jdbcType="VARCHAR" property="productGradeId" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="grade" jdbcType="VARCHAR" property="grade" />
    <result column="grade_unit" jdbcType="VARCHAR" property="gradeUnit" />
    <result column="grade_discount" jdbcType="DECIMAL" property="gradeDiscount" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="is_default" jdbcType="INTEGER" property="isDefault" />
  </resultMap>
  <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductGrade">
    insert into t_product_grade (product_grade_id, product_id, grade,
      grade_unit, grade_discount, price,
      is_default)
    values (#{productGradeId,jdbcType=VARCHAR}, #{productId,jdbcType=VARCHAR}, #{grade,jdbcType=VARCHAR},
      #{gradeUnit,jdbcType=VARCHAR}, #{gradeDiscount,jdbcType=DECIMAL}, #{price,jdbcType=DECIMAL},
      #{isDefault,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductGrade">
    insert into t_product_grade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productGradeId != null">
        product_grade_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="grade != null">
        grade,
      </if>
      <if test="gradeUnit != null">
        grade_unit,
      </if>
      <if test="gradeDiscount != null">
        grade_discount,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productGradeId != null">
        #{productGradeId,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=VARCHAR},
      </if>
      <if test="gradeUnit != null">
        #{gradeUnit,jdbcType=VARCHAR},
      </if>
      <if test="gradeDiscount != null">
        #{gradeDiscount,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <delete id="deleteByProductId" parameterType="java.lang.String">
		DELETE FROM t_product_grade
		WHERE product_id = #{productId,jdbcType=VARCHAR}
  </delete>
</mapper>