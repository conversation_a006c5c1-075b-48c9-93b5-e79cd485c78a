/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.manager.utils;

import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.market.common.entity.ProductAvailable;
import com.snbc.bbpf.bus.product.market.common.entity.ProductDetail;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductAvailableMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductDetailMapper;
import com.snbc.bbpf.bus.product.market.common.vo.BaseOrderVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: OrderServiceUtils
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 服务订单通用类
 * @CreateDate: 2020/8/24 15:21
 * @UpdateDate: 2020/8/24 15:21
 */
@Component
public class OrderServiceUtils {

    public static final int SUCCESS = 1;
    public static final int ZERO = 0;
    public static final int ONE_NUM = 1;
    @Autowired
    private ProductDetailMapper productDetailMapper;
    @Autowired
    private ProductAvailableMapper productAvailableMapper;
    @Autowired
    private OrderNoUtil orderNoUtil;

    /**
     * 构建订单实体对象
     *
     * @param orderVo
     * @param productPurchaseId
     * @param purchaseStatus
     * @return
     */
    public ProductPurchase getProductPurchase(BaseOrderVo orderVo, String productPurchaseId, Integer purchaseStatus) {
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setProductPurchaseId(productPurchaseId);
        productPurchase.setPurchaseNo(orderNoUtil.getOrderNo());
        productPurchase.setTenantId(orderVo.getTenantId());
        productPurchase.setTenantName(orderVo.getTenantName());
        productPurchase.setUserId(orderVo.getUserId());
        productPurchase.setProductId(orderVo.getProductId());
        productPurchase.setPurchaseTime(LocalDateTime.now());
        productPurchase.setBrankstartTime(LocalDateTime.now());
        productPurchase.setProductQuantity(orderVo.getProductQuantity());
        productPurchase.setProductGrade(orderVo.getProductGrade());
        productPurchase.setDiscountAmount(orderVo.getDiscountAmount());
		productPurchase.setPayType(orderVo.getPayType());
        //试用付款金额不计算
        //付款金额再下单时不写入 jiafei 2022-02-08
        if(Constant.IS_RENEW_TRYORDER == orderVo.getIsRenew()){
            productPurchase.setPaymentAmount(new BigDecimal(ZERO));
			productPurchase.setPayType(Constant.PAY_TYPE_TRY);
        }
        productPurchase.setPurchaseAmount(orderVo.getPurchaseAmount());
        productPurchase.setProductPrice(orderVo.getProductPrice());
        //未开票
        productPurchase.setInvoiceStatus(ZERO);
        //暂存单
        productPurchase.setPurchaseStatus(purchaseStatus);
        productPurchase.setIsRenew(orderVo.getIsRenew());
        productPurchase.setRemark(orderVo.getRemark());
        return productPurchase;
    }

    /**
     * 构建跟踪实体对象
     *
     * @return
     */
    public PurchaseTrack getPurchaseTrackEntity() {
        PurchaseTrack purchaseTrack = new PurchaseTrack();
        purchaseTrack.setPurchaseTrackId(UUID.randomUUID().toString());
        purchaseTrack.setCreateTime(LocalDateTime.now());
        return purchaseTrack;
    }

    /**
     * 更新产品服务能力
     *
     * @param purchaseNo
     */
    public boolean updateProductAvailable(String purchaseNo) {
        List<ProductDetail> detailList = productDetailMapper.selectByPurchaseNo(purchaseNo);
        //如果订单没有详情，直接返回
        if (CollectionUtils.isEmpty(detailList)) {
            return false;
        }
        boolean needAuthority = false;
        //循环订单详情，更新可用表
        for (ProductDetail productDetail : detailList) {
            ProductAvailable productAvailable = productAvailableMapper.selectByTenantAndProductId(productDetail.getTenantId(),
                    productDetail.getVemsId(), productDetail.getProductId());
            if (productAvailable != null) {//更新已有
                Double availableVaule = productAvailable.getAvailableValue() + productDetail.getAvailableValue();
                productAvailable.setAvailableValue(availableVaule);
                productAvailable.setUpdateTime(LocalDateTime.now());
                //转正时 更新服务续费状态
                productAvailable.setIsRenew(productDetail.getIsRenew());
                if (productAvailable.getDueTime().isAfter(LocalDateTime.now())) {
                    productAvailable.setDueTime(productAvailable.getDueTime().plusDays(productDetail.getAvailableValue()));
                } else {
                    productAvailable.setDueTime(LocalDate.now().plusDays(productDetail.getAvailableValue() + ONE_NUM).atStartOfDay());
                }
                productAvailableMapper.updateByPrimaryKeySelective(productAvailable);
            } else {
                productAvailable = getProductAvailable(productDetail);
                productAvailableMapper.insert(productAvailable);
                needAuthority = true;
            }
        }
        return needAuthority;
    }

    /**
     * 新增服务可用
     *
     * @param productDetail
     * @return
     */
    private static ProductAvailable getProductAvailable(ProductDetail productDetail) {
        ProductAvailable productAvailable = new ProductAvailable();
        productAvailable.setAvailableValue((double) productDetail.getAvailableValue());
        productAvailable.setAvailableId(UUID.randomUUID().toString());
        productAvailable.setAvailableUnit(productDetail.getAvailableUnit());
        productAvailable.setCreateTime(LocalDateTime.now());
        productAvailable.setActivationTime(productDetail.getActivationTime());
        productAvailable.setBeginTime(productDetail.getBeginTime());
        productAvailable.setDueTime(productDetail.getDueTime());
        productAvailable.setVemsId(productDetail.getVemsId());
        productAvailable.setVemsName(productDetail.getVemsName());
        productAvailable.setTenantId(productDetail.getTenantId());
        productAvailable.setProductId(productDetail.getProductId());
        productAvailable.setIsRenew(productDetail.getIsRenew());
        return productAvailable;
    }

    /**
     * 设置有可用记录的服务到期时间
     * 目前废弃该方法
     * @param productDetail
     * @param productAvailable
     */
    private void setDueTime(ProductDetail productDetail, ProductAvailable productAvailable) {
        //取消设置productAvailable.setBeginTime 服务开始时间 只变更截止时间
        productAvailable.setDueTime(productDetail.getDueTime());
        productAvailable.setActivationTime(productDetail.getActivationTime());
    }

    public static String payTypeConverter(String payType) {
        if (StringUtils.isNotBlank(payType)) {
            switch (payType) {
                case "96":
                    payType = "支付宝支付";
                    break;
                case "97":
                    payType = "微信支付";
                    break;
                case "0":
                    payType = "线下支付";
                    break;
                default:
                    payType = "";
                    break;
            }
        }
        return payType;
    }
}
