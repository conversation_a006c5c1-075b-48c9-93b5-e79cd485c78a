package com.snbc.bbpf.bus.product.manager.converter;

import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderListDto;
import com.snbc.bbpf.commons.objs.IConvert;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * ProductOrderListDto 到 ProductOrderDto 的转换器
 */
@Mapper
public interface ProductOrderListDtoToProductOrderDtoConverter extends IConvert<ProductOrderListDto, ProductOrderDto> {
    ProductOrderListDtoToProductOrderDtoConverter INSTANCE = Mappers.getMapper(ProductOrderListDtoToProductOrderDtoConverter.class);
}