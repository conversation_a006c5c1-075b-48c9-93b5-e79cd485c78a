/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.utils;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.manager.config.NumberConstant;
import com.snbc.bbpf.bus.product.manager.enums.MessageChannelEnum;
import com.snbc.bbpf.bus.product.manager.enums.MessageTypeEnum;
import com.snbc.bbpf.bus.product.manager.enums.SysCodeEnum;
import com.snbc.bbpf.bus.product.manager.service.MessageService;
import com.snbc.bbpf.bus.product.market.common.entity.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.text.SimpleDateFormat;

/**
 * @ClassName: SendMsgUtil
 * 发送消息
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2022/1/11 11:04
 */
@Component
@Slf4j
public class SendMsgUtil {
    @Autowired
    private MessageService messageService;

    public static final SendMsgUtil sendMsgUtil = new SendMsgUtil();

    @PostConstruct
    public void init(){
        sendMsgUtil.messageService = this.messageService;
    }

    /**
     * 发送短信
     * @param message 消息
     */
    public static CommonResp sendShortMessage(Message message){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
        log.info("Send a text message:{},Start time",JSON.toJSONString(message), sdf.format(System.currentTimeMillis()));
        CommonResp commonResp = sendMsgUtil.messageService.sendMsg(convertShortMessage(message));
        log.info("End time of sending SMS messages:{},return value:{}",
                sdf.format(System.currentTimeMillis()), JSON.toJSONString(commonResp));
        return commonResp;
    }

    /**
     * 发送系统消息
     * @param message 消息
     */
    public static void sendSysMessage(Message message){
        sendMsgUtil.messageService.sendMsg(converSysMessage(message));
    }

    /**
     * wjc1 构造短信内容
     @param message 消息
      * @return Message
     */
    private static Message convertShortMessage(Message message){
        message.setMsgChannelCode(MessageChannelEnum.SMS.getStatus());//短信
        message.setSysCode(SysCodeEnum.TENANT.getStatusName());
        message.setMsgModel(NumberConstant.NO_ONE);
        //系统消息
        message.setMessageType(MessageTypeEnum.NO2.getStatus());
        return message;
    }
    /**
     * wjc1 构造系统消息内容
     * @param message 消息
     * @return Message
     */
    private static Message converSysMessage(Message message){
        message.setMsgChannelCode(MessageChannelEnum.SYS.getStatus());
        message.setSysCode(SysCodeEnum.TENANT.getStatusName());
        message.setMsgModel(NumberConstant.NO_ONE);
        //系统消息
        message.setMessageType(MessageTypeEnum.NO2.getStatus());
        return message;
    }
}
