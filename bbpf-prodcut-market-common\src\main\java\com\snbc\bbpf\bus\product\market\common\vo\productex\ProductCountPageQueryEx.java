/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.market.common.vo.productex;

import com.snbc.bbpf.bus.product.market.common.Constant;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR> LiangJb
 * @ClassName : ProductCountPageQuery
 * @Description : 服务统计列表查询入参

参数名	参数位置	类型	描述	是否必填	示例
productName	查询条件	String	服务名称	否	物联网卡
productCategory	查询条件	int	服务类目	是	全部 传-1
productType	查询条件	int	服务类型	是	全部传-1
chargeType	查询条件	int	服务支付方式	是	全部传-1
 * pageNum	查询条件	string	页数	是	1
 * pageSize	查询条件	string	每页行数	是	10
 * @Date: 2020-08-12 15:51
 */



@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T06:49:02.983Z")
@Data
public class ProductCountPageQueryEx {
    
    private Integer pageSize= Constant.TEN;
    
    private Integer pageNum=Constant.ONE;

    
    private String productName;
    
    private String productType;
    
    private String productCategory;
    
    private String chargeType;
}
