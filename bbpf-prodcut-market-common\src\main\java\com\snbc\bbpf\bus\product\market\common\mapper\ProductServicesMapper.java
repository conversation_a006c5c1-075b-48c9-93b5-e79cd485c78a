package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.ProductServices;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProductServicesMapper {

    /**
     * @author: liangJB
     * 根据产品id查询
     * @param productId
     * @return
     */
    ProductServices selectByPrimaryKey(@Param("productId") String productId);

    void delProductService(String productId);
}
