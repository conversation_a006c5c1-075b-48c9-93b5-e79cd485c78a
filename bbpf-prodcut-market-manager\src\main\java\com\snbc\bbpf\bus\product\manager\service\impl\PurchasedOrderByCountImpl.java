package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.service.PurchasedProductOrderService;
import com.snbc.bbpf.bus.product.manager.utils.OrderServiceUtils;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProducedOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductByConutStatistics;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ProjectName: product-manager
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: PurchasedProductOrderByFrequency
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 按次数服务购买订单信息
 * @Author: wangsong
 * @CreateDate: 2021/2/25 18:29
 * @UpdateUser: wangsong
 * @UpdateDate: 2021/2/25 18:29
 */
@Component(value = "purchasedProductOrderByCount")
public class PurchasedOrderByCountImpl implements PurchasedProductOrderService {
    @Autowired
    private ProductOrderMapper productOrderMapper;

    /***     
      * @Description:    查询按次收费的服务的订单列表
      * @Author:         wangsong
      * @param :         tenantId
      * @param :         productId
      * @param :         pageNum
      * @param :         pageSize
      * @CreateDate:     2021/3/2 10:39
      * @UpdateUser:     wangsong
      * @UpdateDate:     2021/3/2 10:39
      * @return :        com.github.pagehelper.PageInfo<java.lang.Object>
     */
    @Override
    public PageInfo<Object> purchasedOrderList(String tenantId, String productId, Integer pageNum, Integer pageSize) {
        //分页
        PageHelper.startPage(pageNum, pageSize);
        //查询已购买服务（按次收费）订单记录
        List<ProducedOrderDto> productByCountOrderDtos = productOrderMapper.productByCountOrder(tenantId, productId);
        //支付类型转换
        productByCountOrderDtos.forEach(productByCountOrderDto ->
            productByCountOrderDto.setPayType(OrderServiceUtils.payTypeConverter(productByCountOrderDto.getPayType()))
        );
        return new PageInfo(productByCountOrderDtos);
    }


    /***
      * @Author:         wangsong
      * @param :         tenantId
      * @param :         productId
      * @CreateDate:     2021/3/2 10:39
      * @UpdateUser:     wangsong
      * @UpdateDate:     2021/3/2 10:39
      * @return :        ProductByConutStatistics
     */
    @Override
    public ProductByConutStatistics productRemainingStatis(String tenantId, String productId) {
        //查询按次计费的服务最近要到期的次数及日期
        ProductByConutStatistics productExpireInfo = productOrderMapper.getProductExpireInfo(tenantId, productId);
        //查询剩余次数、购买总次数
        ProductByConutStatistics productCount = productOrderMapper.getProductCount(tenantId, productId);
        productExpireInfo.setTotalPurchases(productCount.getTotalPurchases());
        productExpireInfo.setRemainingCount(productCount.getRemainingCount());
        return productExpireInfo;
    }
}
