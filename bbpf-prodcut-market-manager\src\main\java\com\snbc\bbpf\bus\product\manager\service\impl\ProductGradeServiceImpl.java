package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.service.ProductGradeService;
import com.snbc.bbpf.bus.product.market.common.entity.ProductGrade;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductGradeMapper;
import com.snbc.bbpf.bus.product.market.common.vo.ProductGradeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: ProductGradeServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 产品阶梯收费业务处理
 * @Author: wangsong
 * @CreateDate: 2020/8/26 10:57
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/26 10:57
 */
@Service
@Transactional
public class ProductGradeServiceImpl implements ProductGradeService {
    @Autowired
    private ProductGradeMapper productGradeMapper;
    @Override
    public boolean addProductGrade(List<ProductGradeVo> productGradeVoList) {
        for (ProductGradeVo productGradeVo:productGradeVoList){
            ProductGrade productGrade=new ProductGrade();
            productGrade.setGrade(productGradeVo.getGrade());
            productGrade.setGradeDiscount(productGradeVo.getGradeDiscount());
            productGrade.setGradeUnit(productGradeVo.getGradeUnit());
            productGrade.setIsDefault(productGradeVo.getIsDefault());
            productGrade.setPrice(Double.parseDouble(productGradeVo.getPrice()));
            productGrade.setProductId(productGradeVo.getProductId());
            String strId=UUID.randomUUID().toString();
            productGrade.setProductGradeId(strId);
            productGradeMapper.insertSelective(productGrade);
        }
        return false;
    }
}
