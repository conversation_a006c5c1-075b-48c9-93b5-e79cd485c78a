<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.InvoiceApplyMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.InvoiceApply">
        <id column="invoice_apply_id" jdbcType="VARCHAR" property="invoiceApplyId"/>
        <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="invoice_delivery_id" jdbcType="VARCHAR" property="invoiceDeliveryId"/>
        <result column="apply_type" jdbcType="INTEGER" property="applyType"/>
        <result column="invoice_type" jdbcType="INTEGER" property="invoiceType"/>
        <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle"/>
        <result column="invoice_content" jdbcType="VARCHAR" property="invoiceContent"/>
        <result column="apply_user" jdbcType="VARCHAR" property="applyUser"/>
        <result column="invoice_carrier" jdbcType="INTEGER" property="invoiceCarrier"/>
        <result column="invoice_status" jdbcType="INTEGER" property="invoiceStatus"/>
        <result column="invoice_amount" jdbcType="DECIMAL" property="invoiceAmount"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="invoice_time" jdbcType="TIMESTAMP" property="invoiceTime"/>
        <result column="express_company" jdbcType="VARCHAR" property="expressCompany"/>
        <result column="express_no" jdbcType="VARCHAR" property="expressNo"/>
        <result column="tax_register_no" jdbcType="VARCHAR" property="taxRegisterNo"/>
        <result column="bank_name" jdbcType="VARCHAR" property="bankName"/>
        <result column="bank_no" jdbcType="VARCHAR" property="bankNo"/>
        <result column="register_address" jdbcType="VARCHAR" property="registerAddress"/>
        <result column="register_phonenum" jdbcType="VARCHAR" property="registerPhonenum"/>
        <result column="mail" jdbcType="VARCHAR" property="mail"/>
        <result column="tenant_name" jdbcType="VARCHAR" property="tenantName"/>
    </resultMap>
    <sql id="Base_Column_List">
    invoice_apply_id, invoice_code, tenant_id, invoice_delivery_id, apply_type, invoice_type, invoice_title,
    invoice_content, apply_user, invoice_carrier, invoice_status, invoice_amount, apply_time,
    invoice_time, express_company, express_no, tax_register_no, bank_name, bank_no, register_address,
    register_phonenum, mail,tenant_name
  </sql>
    <select id="selectInvoiceList" parameterType="com.snbc.bbpf.bus.product.market.common.vo.invoice.InvoiceParamQuery"
            resultType="com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceListRRQDto">
        select
            t1.invoice_delivery_id as invoiceDeliveryId,
            t1.invoice_apply_id as invoiceApplyId,
            t1.invoice_title AS invoiceTitle,
            t1.tax_register_no AS taxRegisterNo,
            t1.register_address AS registerAddress,
            t1.register_phonenum AS registerPhonenum,
            t1.bank_name AS bankName,
            t1.bank_no AS bankNo,
            t1.invoice_code as invoiceCode,
            t1.apply_time as applyTime,
            t1.invoice_time as invoiceTime,
            t1.invoice_amount as invoiceAmount,
            t1.invoice_content as invoiceContent,
            t1.tenant_name as tenantName,
            t1.mail as mail,
            t2.express_company as expressCompany,
            t2.express_no as expressNo,
            t2.address as receiverAddress,
            t3.value_name AS invoiceStatus,
            t4.value_name AS invoiceCarrier,
            t5.value_name AS invoiceType,
            t6.value_name AS applyType
            from t_invoice_apply t1
            left join t_invoice_delivery t2 on t1.invoice_delivery_id=t2.delivery_id
            left join (select value_code,value_name from t_dict_value  where type_code='invoice_status') t3 on t1.invoice_status=t3.value_code
            left join (select value_code,value_name from t_dict_value  where type_code='invoice_carrier') t4 on t1.invoice_carrier=t4.value_code
            left join (select value_code,value_name from t_dict_value  where type_code='invoice_type') t5 on t1.invoice_type=t5.value_code
            left join (select value_code,value_name from t_dict_value  where type_code='apply_type') t6 on t1.apply_type=t6.value_code
            where 1=1
        <if test="invoiceType != null and invoiceType !=''">
            and t1.invoice_type = #{invoiceType,jdbcType=INTEGER}
        </if>
        <if test="invoiceCarrier != null and invoiceCarrier != ''">
            and t1.invoice_carrier = #{invoiceCarrier,jdbcType=INTEGER}
        </if>
        <if test="invoiceStatus != null and invoiceStatus != ''">
            and t1.invoice_status = #{invoiceStatus,jdbcType=INTEGER}
        </if>
        <if test="tenantName != null and tenantName != ''">
            and t1.tenant_name like CONCAT ('%',#{tenantName,jdbcType = VARCHAR},'%')
        </if>
        <if test="invoiceApplyType != null and invoiceApplyType != ''">
            and t1.apply_type = #{invoiceApplyType,jdbcType=INTEGER}
        </if>
        <if test="tenantId != null">
            and t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null and startTime !=''">
            <![CDATA[ and t1.apply_time >= (#{startTime})]]>
        </if>
        <if test="endTime != null and endTime !=''">
            <![CDATA[ and t1.apply_time <= (#{endTime})]]>
        </if>
        order by
        <choose>
            <when test="sortField == 'invoiceAmount' or sortField == 'applyType' or sortField == 'invoiceCarrier' or sortField == 'invoiceStatus'">
                ${sortField}
            </when>
            <otherwise>
                t1.apply_time
            </otherwise>
        </choose>
        <choose>
            <when test="sequence == 'asc' or sequence == 'desc'">
                ${sequence}
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultType="com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceDetailDto">
    SELECT
        t1.invoice_code as invoiceCode,
        t1.invoice_title AS invoiceTitle,
        t1.invoice_content AS invoiceContent,
        t1.apply_user AS applyUser,
        t1.invoice_amount AS invoiceAmount,
        t1.apply_time AS applyTime,
        t1.invoice_time as invoiceTime,
        t2.express_company AS expressCompany,
        t2.express_no AS expressNo,
        t1.tax_register_no AS taxRegisterNo,
        t1.bank_name AS bankName,
        t1.bank_no AS bankNo,
        t1.register_address AS registerAddress,
        t1.register_phonenum AS registerPhonenum,
        t1.mail,
        t1.tenant_name AS tenantName,
        t2.address AS receiverAddress,
        t2.tel AS receiverTel,
        t2.receiver_name AS receiverName,
        t3.value_name AS invoiceStatus,
        t4.value_name AS invoiceCarrier,
        t5.value_name AS invoiceType,
        t6.value_name AS applyType
    FROM
        t_invoice_apply t1
    LEFT JOIN t_invoice_delivery t2 ON t1.invoice_delivery_id = t2.delivery_id
    left join (select value_code,value_name from t_dict_value  where type_code='invoice_status') t3 on t1.invoice_status=t3.value_code
    left join (select value_code,value_name from t_dict_value  where type_code='invoice_carrier') t4 on t1.invoice_carrier=t4.value_code
    left join (select value_code,value_name from t_dict_value  where type_code='invoice_type') t5 on t1.invoice_type=t5.value_code
    left join (select value_code,value_name from t_dict_value  where type_code='apply_type') t6 on t1.apply_type=t6.value_code
    where t1.invoice_apply_id = #{invoiceApplyId,jdbcType=VARCHAR}
  </select>

    <select id="invoiceDetailPurchaseList" resultType="com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoicePurchaseDto">
        SELECT
            t1.product_purchase_id AS productPurchaseId,
            t1.purchase_no AS purchaseNo,
            t3.product_name AS productName,
            t1.purchase_amount AS purchaseAmount,
            t1.discount_amount AS discountAmount,
            t1.payment_amount AS paymentAmount,
            t1.purchase_time AS purchaseTime,
            t1.pay_time AS payTime
        FROM
        t_product_purchase t1
        LEFT JOIN t_invoice_purchase t2 ON t1.product_purchase_id = t2.product_purchase_id
        LEFT JOIN t_product_services t3 ON t3.product_id = t1.product_id
        where 1=1
        <!--根据发票编号查询-->
        <if test="invoiceApplyId != null and invoiceApplyId != ''">
            and t2.invoice_apply_id = #{invoiceApplyId,jdbcType=VARCHAR}
        </if>
        <if test="tenantId != null">
            and t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''">
            and t1.user_id = #{userId,jdbcType=VARCHAR}
        </if>
        order by
        <choose>
            <when test="sortField == 'purchaseNo' or sortField == 'productName'
            or sortField == 'payTime' or sortField == 'paymentAmount'">
              ${sortField}
            </when>
            <otherwise>
                t1.pay_time
            </otherwise>
        </choose>
        <choose>
            <when test="sequence == 'desc' or sequence == 'asc'">
                ${sequence}
            </when>
            <otherwise>
              desc
            </otherwise>
        </choose>
  </select>

    <select id="invoicePurchaseList" resultType="com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoicePurchaseDto">
        SELECT
        t1.product_purchase_id AS productPurchaseId,
        t1.purchase_no AS purchaseNo,
        t2.product_name AS productName,
        t1.purchase_amount AS purchaseAmount,
        t1.discount_amount AS discountAmount,
        t1.payment_amount AS paymentAmount,
        t1.purchase_time AS purchaseTime,
        t1.pay_time AS payTime
        FROM
        t_product_purchase t1
        LEFT JOIN t_product_services t2 ON t2.product_id = t1.product_id
        where 1=1
        <if test="tenantId != null">
            and t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''">
            and t1.user_id = #{userId,jdbcType=VARCHAR}
        </if>
        <!--已购买成功的订单-->
            AND t1.purchase_status = 2
        <!--发票编号查询30天内的订单数据-->
            and TO_DAYS(NOW()) - TO_DAYS(t1.pay_time)  <![CDATA[ <= ]]> 30
        <!--未开票的订单-->
            and invoice_status = 0
            and payment_amount <![CDATA[ <> ]]> 0
        order by
        <choose>
            <when test="sortField == 'purchaseNo' or sortField == 'productName'
            or sortField == 'payTime' or sortField == 'paymentAmount'">
                ${sortField}
            </when>
            <otherwise>
                t1.pay_time
            </otherwise>
        </choose>
        <choose>
            <when test="sequence == 'desc' or sequence == 'asc'">
                ${sequence}
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <insert id="insertInvoiceRecord" parameterType="com.snbc.bbpf.bus.product.market.common.entity.InvoiceApply">
        insert into t_invoice_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceApplyId != null">
                invoice_apply_id,
            </if>
            <if test="invoiceCode != null">
                invoice_code,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="invoiceDeliveryId != null and invoiceDeliveryId != ''">
                invoice_delivery_id,
            </if>
            <if test="applyType != null">
                apply_type,
            </if>
            <if test="invoiceType != null">
                invoice_type,
            </if>
            <if test="invoiceTitle != null">
                invoice_title,
            </if>
            <if test="invoiceContent != null">
                invoice_content,
            </if>
            <if test="applyUser != null">
                apply_user,
            </if>
            <if test="invoiceCarrier != null">
                invoice_carrier,
            </if>
            <if test="invoiceStatus != null">
                invoice_status,
            </if>
            <if test="invoiceAmount != null">
                invoice_amount,
            </if>
            <if test="applyTime != null">
                apply_time,
            </if>
            <if test="invoiceTime != null">
                invoice_time,
            </if>
            <if test="expressCompany != null">
                express_company,
            </if>
            <if test="expressNo != null">
                express_no,
            </if>
            <if test="taxRegisterNo != null">
                tax_register_no,
            </if>
            <if test="bankName != null">
                bank_name,
            </if>
            <if test="bankNo != null">
                bank_no,
            </if>
            <if test="registerAddress != null">
                register_address,
            </if>
            <if test="registerPhonenum != null">
                register_phonenum,
            </if>
            <if test="mail">
                mail,
            </if>
            <if test="tenantName">
                tenant_name
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceApplyId != null">
                #{invoiceApplyId,jdbcType=VARCHAR},
            </if>
            <if test="invoiceCode != null">
                #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="invoiceDeliveryId != null and invoiceDeliveryId != ''">
                #{invoiceDeliveryId,jdbcType=VARCHAR},
            </if>
            <if test="applyType != null">
                #{applyType,jdbcType=INTEGER},
            </if>
            <if test="invoiceType != null">
                #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="invoiceTitle != null">
                #{invoiceTitle,jdbcType=VARCHAR},
            </if>
            <if test="invoiceContent != null">
                #{invoiceContent,jdbcType=VARCHAR},
            </if>
            <if test="applyUser != null">
                #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="invoiceCarrier != null">
                #{invoiceCarrier,jdbcType=INTEGER},
            </if>
            <if test="invoiceStatus != null">
                #{invoiceStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceAmount != null">
                #{invoiceAmount,jdbcType=DECIMAL},
            </if>
            <if test="applyTime != null">
                #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="invoiceTime != null">
                #{invoiceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expressCompany != null">
                #{expressCompany,jdbcType=VARCHAR},
            </if>
            <if test="expressNo != null">
                #{expressNo,jdbcType=VARCHAR},
            </if>
            <if test="taxRegisterNo != null">
                #{taxRegisterNo,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="bankNo != null">
                #{bankNo,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null">
                #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerPhonenum != null">
                #{registerPhonenum,jdbcType=VARCHAR},
            </if>
            <if test="mail">
                #{mail,jdbcType=VARCHAR},
            </if>
            <if test="tenantName">
                #{tenantName,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <update id="updateinvoiceStatus" parameterType="java.lang.String">
        update t_invoice_apply set invoice_status = 2,invoice_time = NOW()
        where invoice_apply_id = #{invoiceApplyId,jdbcType=VARCHAR}
    </update>

    <insert id="insertInvoicePurchase">
        <foreach collection="invoicePurchaseList" item="invoicePurchase" index="index" open="" close="" separator=";">
            insert into t_invoice_purchase(invoice_purchase_id,invoice_apply_id,product_purchase_id)
            values(#{invoicePurchase.invoicePurchaseId},#{invoicePurchase.invoiceApplyId},#{invoicePurchase.productPurchaseId})
        </foreach>
    </insert>

    <select id="getInvoiceTitleByTenantId" parameterType="java.lang.String" resultType="com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceTitleDto">
            SELECT
                t1.apply_type as applyType,
                t1.invoice_type as invoiceType,
                t1.invoice_carrier as invoiceCarrier,
                t1.invoice_title as companyName,
                t1.tax_register_no as taxRegisterNo,
                t1.bank_name as bankName,
                t1.bank_no as bankNo,
                t1.mail,
                t1.register_address as registerAddress,
                t1.register_phonenum as registerPhonenum,
                t2.receiver_name as receiverName,
                t2.tel as receiverTel,
                t2.address as receiverAddress
            FROM
                t_invoice_apply t1
            LEFT JOIN t_invoice_delivery t2 ON t1.invoice_delivery_id = t2.delivery_id
            WHERE 1=1
            and	t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
            ORDER BY t1.apply_time desc
            LIMIT 0,1
    </select>

    <select id="invoiceStatusById" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT
            invoice_status
        FROM
            t_invoice_apply
        WHERE
            invoice_apply_id = #{invoiceApplyId,jdbcType=VARCHAR};
    </select>

    <select id="selectExportInvoice" parameterType="com.snbc.bbpf.bus.product.market.common.vo.invoice.InvoiceParamQuery"
            resultType="com.snbc.bbpf.bus.product.market.common.entity.InvoiceApply">
        select
        t1.invoice_delivery_id as invoiceDeliveryId,
        t1.invoice_apply_id as invoiceApplyId,
        t1.invoice_title AS invoiceTitle,
        t1.tax_register_no AS taxRegisterNo,
        t1.register_address AS registerAddress,
        t1.register_phonenum AS registerPhonenum,
        t1.bank_name AS bankName,
        t1.bank_no AS bankNo,
        t1.invoice_code as invoiceCode,
        t1.apply_time as applyTime,
        t1.invoice_time as invoiceTime,
        t1.invoice_amount as invoiceAmount,
        t1.invoice_content as invoiceContent,
        t1.tenant_name as tenantName,
        t1.mail as mail,
        t2.express_company as expressCompany,
        t2.express_no as expressNo,
        t2.address as receiverAddress,
        t3.value_name AS invoiceStatus,
        t4.value_name AS invoiceCarrier,
        t5.value_name AS invoiceType,
        t6.value_name AS applyType
        from t_invoice_apply t1
        left join t_invoice_delivery t2 on t1.invoice_delivery_id=t2.delivery_id
        left join (select value_code,value_name from t_dict_value  where type_code='invoice_status') t3 on t1.invoice_status=t3.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='invoice_carrier') t4 on t1.invoice_carrier=t4.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='invoice_type') t5 on t1.invoice_type=t5.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='apply_type') t6 on t1.apply_type=t6.value_code
        where 1=1
        <if test="invoiceType != null and invoiceType !=''">
            and t1.invoice_type = #{invoiceType,jdbcType=INTEGER}
        </if>
        <if test="invoiceCarrier != null and invoiceCarrier != ''">
            and t1.invoice_carrier = #{invoiceCarrier,jdbcType=INTEGER}
        </if>
        <if test="invoiceStatus != null and invoiceStatus != ''">
            and t1.invoice_status = #{invoiceStatus,jdbcType=INTEGER}
        </if>
        <if test="tenantName != null and tenantName != ''">
            and t1.tenant_name like CONCAT ('%',#{tenantName,jdbcType = VARCHAR},'%')
        </if>
        <if test="invoiceApplyType != null and invoiceApplyType != ''">
            and t1.apply_type = #{invoiceApplyType,jdbcType=INTEGER}
        </if>
        <if test="tenantId != null">
            and t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null and startTime !=''">
            <![CDATA[ and t1.apply_time >= (#{startTime})]]>
        </if>
        <if test="endTime != null and endTime !=''">
            <![CDATA[ and t1.apply_time <= (#{endTime})]]>
        </if>
        order by
        <choose>
            <when test="sortField == 'invoiceAmount' or sortField == 'applyType' or sortField == 'invoiceCarrier' or sortField == 'invoiceStatus'">
                ${sortField}
            </when>
            <otherwise>
                t1.apply_time
            </otherwise>
        </choose>
        <choose>
            <when test="sequence == 'asc' or sequence == 'desc'">
                ${sequence}
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>
</mapper>
