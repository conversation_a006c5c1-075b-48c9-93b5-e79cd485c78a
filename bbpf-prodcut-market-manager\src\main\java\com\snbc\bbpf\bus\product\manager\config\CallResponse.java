/*
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.config;


import java.util.regex.Pattern;

/**
 * 统一返回实体
 *
 * <AUTHOR>
 */
public class CallResponse<T> {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    /**
     *正则表达式提取
     */
    private static final Pattern myRegex = Pattern.compile("\\{");
    private String code; // required
    private String message; // required
    private T result;

    /**
     * 获取返回码
     *
     * @return
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置返回码
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取返回提示
     *
     * @return
     */
    public String getMessage() {
        return message;
    }

    /**
     * 设置提示信息
     *
     * @param message
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 返回结果数据
     *
     * @return
     */
    public T getResult() {
        return result;
    }

    /**
     * 设置结果数据
     *
     * @param result
     */
    public void setResult(T result) {
        this.result = result;
    }

}

