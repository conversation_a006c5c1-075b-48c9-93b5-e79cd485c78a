package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.service.InvoiceDeliveryService;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoceExpressDeliDto;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceApplyMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceDeliveryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: InvoiceDeliveryServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/10/27 18:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/27 18:21
 */
@Service
public class InvoiceDeliveryServiceImpl implements InvoiceDeliveryService {

    @Autowired
    private InvoiceDeliveryMapper invoiceDeliveryMapper;
    @Autowired
    private InvoiceApplyMapper invoiceApplyMapper;

    /***
     * @Description: 查询快递信息
     * @Author: wangsong
     * @param :         invoiceApplyId
     * @CreateDate: 2020/8/27 10:16
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 10:16
     * @return :        com.snbc.vems.product.entity.InvoiceDeliveryService
     */
    @Override
    public InvoceExpressDeliDto logisticsDetail(String invoiceApplyId) {
        return invoiceDeliveryMapper.selectExpressInfo(invoiceApplyId);
    }
}
