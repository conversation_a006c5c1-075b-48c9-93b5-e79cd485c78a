/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.manager.exception;

/**
 * @ClassName:      BusinessException.java
 * @Description:    业务异常类
 * @module:
 * @Author:         wangsong
 * @date:           2021/5/17 15:20
 * copyright 2020 SNBC. All rights reserver
 */

@SuppressWarnings("serial")
public class BusinessExceptionEx extends RuntimeException {
    // 异常编码
    private final String code;
    // 异常内容
    private final Exception data;

    public BusinessExceptionEx(String msg, String code, Exception data) {
        super(msg);
        this.code = code;
        this.data = data;
    }
    public BusinessExceptionEx(IResponseEnum responseEnum, String message) {
        super(message);
        throw new BusinessExceptionEx(message,responseEnum.getCode());
    }
    public BusinessExceptionEx(String msg, String code) {
        super(msg);
        this.code = code;
        data = null;
    }

    public String getCode() {
        return code;
    }

    public Object getData() {
        return data;
    }
}
