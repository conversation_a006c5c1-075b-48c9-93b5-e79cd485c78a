/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: PermissionVo
 * 权限信息,用于权限展示
 * @module: bbpf-bus-system
 * @Author:Liangjb
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PermissionsVo {
    
    private boolean hasChecked;
    
    private String permissionId;
    
    private String permissionType;
    
    private String permissionTypeName;
    
    private String permissionName;
    
    private String permissionCode;
    
    private String parentId;
    
    private Integer orderBy;
    
    private String permissionImage;
    
    private Integer sysType;
    
    private String routingUrl;
    /**
     * 孩子节点列表
     */
    private List<PermissionsVo> children;
}
