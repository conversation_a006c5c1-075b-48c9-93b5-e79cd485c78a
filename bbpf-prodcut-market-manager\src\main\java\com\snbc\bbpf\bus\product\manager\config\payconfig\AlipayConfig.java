package com.snbc.bbpf.bus.product.manager.config.payconfig;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 功能描述: <br>
 * 〈AlipayConfig〉
 *
 * @param: null
 * @return:
 * @exception
 * @author: gs
 * @date: 2019-08-08 18:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlipayConfig {
	/**
	 *	二级商户信息，进件时分配给平台商的二级商户 id
	 *	 填写通过ant.merchant.expand.indirect.zft.passed 返回的二级商户id
	 */
	@JsonProperty("merchantId")
    private String merchantId;
	/**
	 *	 结算账户类型
	 * 	结算到进件填写的银行卡填写"cardAliasNo"
	   	结算到进件填写的支付宝账号填写"loginName"
	   	结算到进件填写的支付宝账号对应的userId填写"userId"
	 */
	@JsonProperty("transInType")
    private String transInType;
	/**
	 * 结算账户编号 trans_in
	--结算到银行卡填写 ant.merchant.expand.indirect.zft.passed 返回的卡编号"card_alias_no"；
	--结算到支付宝账号填写进件时填写的支付宝账号（登陆号）
	--如果存在一个手机号绑定多个支付宝账号的情况，结算时会报"交易结算异常",可使用传值userId解决；
	 */
	@JsonProperty("transIn")
    private String transIn;

    @JsonProperty("payConfigType")
    private String payConfigType;

    @JsonProperty("charset")
    private String charset;

    @JsonProperty("pid")
    private String pid;

    @JsonProperty("callbackAddr")
    private String callbackAddr;

    @JsonProperty("appPrivateKey")
    private String appPrivateKey;

    @JsonProperty("appId")
    private String appId;

    @JsonProperty("signType")
    private String signType;

    @JsonProperty("publicKey")
    private String publicKey;

    @JsonProperty("alipayVersion")
    private String alipayVersion;

    @JsonProperty("applyTip")
    private String applyTip;

    @JsonProperty("appAuthToken")
    private String appAuthToken;

}
