server.port = 9921
server.undertow.threads.io = 16
server.undertow.threads.worker = 256

spring.application.name = bbpf-bus-product-market
#日志
logging.level.root = debug
logging.level.com.snbc.bbpf = debug
#logging.config = /app/data/log4jconfig/bbpf-bus-product-market-log4j2.xml

spring.main.allow-bean-definition-overriding = true
#mybatis.mapper-locations = classpath*:META-INF/sqlmap/*Mapper.xml

pagehelper.helper-dialect = mysql
pagehelper.params = count=countSql
pagehelper.reasonable = true
pagehelper.support-methods-arguments = true
jasypt.encryptor.password = snbcpwde
jasypt.encryptor.algorithm = PBEWithMD5AndDES

#spring.datasource.url = *************************************************
#spring.datasource.username = gaussdb
#spring.datasource.password = ^*YTXgAfKK4B3N
#spring.datasource.driver-class-name = org.opengauss.Driver
mybatis.mapper-locations = classpath*:mapper/*Mapper.xml

spring.datasource.url = ******************************************************************************************************************************************************************
spring.datasource.username = ENC(Lcqm2iLDeczttkCPt9+9wcI+z6caw/No)
spring.datasource.password = ENC(swC7aTC9cP5qg5J2sQyiEYJpY1U3XoerbnRIQz5ni0M=)
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver


#eureka配置
eureka.instance.lease-renewal-interval-in-second = 5
eureka.instance.leaseRenewalIntervalInSeconds = 20
eureka.instance.leaseExpirationDurationInSeconds = 30
eureka.client.registerWithEureka = false
eureka.client.fetchRegistry = false
eureka.instance.prefer-ip-address = false
eureka.client.serviceUrl.defaultZone = http://*************:15110/eureka

#JWT过期时间，由以前单位为604800 秒 改为 24小时
bbpf.system.security.catch.expiration: 24
#redis库
spring.redis.database = 1
# Redis服务器地址
spring.redis.host = 127.0.0.1
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码
spring.redis.password=
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.max-active=200
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.max-idle=10
# 连接池中的最小空闲连接
spring.redis.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=

# swagger的开关，开发测试环境上 true, 生产环境上 false
product.swagger.enable = true

product.notice.pool= 10
product.notice.workers = 5
product.notice.delay = 10
product.notice.exchange = ProductNoticeExchange
product.notice.queue = ProductNoticeQueue
# on表示变更通知开启 off表示变更通知关闭
product.notice.onoff = on

bbpf.message.smsplatform=""




spring.rabbitmq.host = ************
spring.rabbitmq.port = 15012
spring.rabbitmq.username = admin
spring.rabbitmq.password = snbc0430
spring.rabbitmq.virtual-host = bbpf
#spring.rabbitmq.publisher-confirms=true
#spring.rabbitmq.listener.simple.acknowledge-mode=manual

product.notice.timeout = 30
product.timeout.delay = 5

# 到期提醒
product.timeout.cron = 0 0 0 * * ?

# 日报统计 秒，分，小时、天
product.daily.cron = 0 0 8 * * ?
# 月报统计
product.monthly.cron = 0 0 8 1 * ?

product.present.price = 1.0
product.release.url = http://IP:port/release-url
product.timeout.url = http://IP:port/time-out-url
product.default.duedays = 540
product.timeout.threshold = 30
#天/台:1,天:2,次:3,M/月:4,M:5
product.code.limit = 1:2,2:3,3:4,4:5

platform.mail.protocol = smtp
platform.mail.host = smtp.126.com
platform.mail.from = <EMAIL>
platform.mail.password = ENC(i8ZJxp+aQ6spQgjhBSDrMTfgPNyLVWSwgDMlvL1XSws=)
platform.mail.auth = true
platform.mail.port = 465
platform.mail.sendName = 产品服务统计报表
platform.mail.jndi-name = 666
platform.mail.copy = <EMAIL>
platform.mail.to = <EMAIL>
#系统获取菜单的默认父级节点（展示其节点以下的菜单，但不包含该菜单）
bbpf.system.menu.parentid = 24d421e2-fc70-4bc4-bd07-95c8553e886d

##文件上传配置ocal,配置文件上传下载方式，
#之后在相应的配置上修改配置文件，例如配置为local，本地文件存储目录必须配置，
#如果配置为youPaiYun，又拍云的相关参数一定要配置，用哪个配置哪
#上传路径
#bbpf.oss.config.uploadPath = system/dev/user/
##又拍云配置项定义:
#bbpf.oss.upyun.config.bucketName = bbpf2
#bbpf.oss.upyun.config.userName = bbpf2
#bbpf.oss.upyun.config.password = wG41haP16Y8dKKR0mhybPIrPMyVizhyt
##bbpf.oss.upyun.config.uploadPath=user/avatar/
##服务文件域名
#bbpf.oss.upyun.config.fileUrl = https://test-pic.newbeiyang.cn/
#上传路径
bbpf.oss.config.uploadPath = productmanager/dev/
#又拍云配置项定义:
bbpf.oss.upyun.config.bucketName = bbpf2
bbpf.oss.upyun.config.userName = bbpf2
bbpf.oss.upyun.config.password = wG41haP16Y8dKKR0mhybPIrPMyVizhyt
#bbpf.oss.upyun.config.uploadPath=user/avatar/
#服务文件域名
bbpf.oss.upyun.config.fileUrl = https://test-pic.newbeiyang.cn/
#文件大小配置
spring.servlet.multipart.max-file-size = 10MB
spring.servlet.multipart.max-request-size = 10MB
#阿里云oss配置定义:
# oss连接区域地址（如：华北地区对应的地址）
bbpf.oss.aliyun.oss.endpoint = http://oss-cn-hangzhou.aliyuncs.com
# 访问身份验证中用到用户标识
bbpf.oss.aliyun.oss.accessKeyId = ***
# 用户用于加密签名字符串和oss用来验证签名字符串的密钥
bbpf.oss.aliyun.oss.accessKeySecret = **
#在控制台创建的bucketName
bbpf.oss.aliyun.oss.bucketName = **
#最大连接数
bbpf.oss.aliyun.oss.maxConnections = 512
#超时时间
bbpf.oss.aliyun.oss.socketTimeout = 60
#最大失败重试次数
bbpf.oss.aliyun.oss.maxErrorRetry = 3

#本地文件存储Minio配置：
bbpf.oss.minio.config.endpoint = http://**************:9000
# minio文件夹（桶）名称
bbpf.oss.minio.config.bucketName = image-file
#minio用户
bbpf.oss.minio.config.accessKey = ENC(b/Wml1K8JC4C3XbcueuGD86P7k4WzH2H)
#minio秘钥
bbpf.oss.minio.config.secretKey = ENC(b/Wml1K8JC4C3XbcueuGD86P7k4WzH2H)

#本地文件存储配置：
bbpf.oss.file.local.path = d:/log

#通用配置：
#允许上传的文件格式
bbpf.oss.file.suffixName = .doc,.docx,.pdf,.png,.jpg,.jpeg
#文件上传频率，间隔多少秒可以再上传
bbpf.oss.file.upload.frequency = 0
#文件下载频率，间隔多少秒可以再下载
bbpf.oss.file.download.frequency = 30
#文件上传大小（bytes）限定10MB
bbpf.oss.file.upload.size = 10485760
#文件上传，下载需要用到的环境，本地:local，又拍云:youPaiYun，阿里云:aliYun，MinIo:minIo
bbpf.oss.file.util.class = youPaiYun

bbpf.product.request.type = ribbon
product.error.report.log.path = /data/product
bbpf.product.request.url = http://localhost:9988/
bbpf.product.request.token = 818383232
#spring.cloud.loadbalancer.ribbon.enabled = false


bbpf.gray.common.nameSpace=bbpf.gray