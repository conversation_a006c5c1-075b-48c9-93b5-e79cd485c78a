package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/*
* @author: liangJB
     * 功能描述: <br>
     * 服务基础订单详情
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
purchaseNo	String	是	订单编号
productName	String	是	服务名称
shopName	String	是	商户名称
gradeDiscount	int	是	规格（天/台）
price	Decimal	是	单价
productQuantity	int	是	购买量/台
purchaseAmount	Decimal	是	总金额(元)
discountAmount	Decimal	是	优惠金额(元)
paymentAmount	Decimal	是	实付金额(元)
payTypeName	String	是	支付方式

purchaseStatus	int	是	具体直参考订单状态0：待支付 1：已支付 2：购买成功 3:已取消
purchaseTime	Date	是	下单时间
payTime	Date	是	支付时间

purchaseChannelNo	String	是	支付流水
successTime	Date	是	购买成功时间

productTypeName String	是	产品类型
 chargeTypeName String	是	支付流水

* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductOrderDetailDto extends ProductShopOrderDto {

    
    private String purchaseChannelNo;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime successTime;
    
    private String productTypeName;
    
    private String chargeTypeName;
}
