package com.snbc.bbpf.bus.product.manager.resp.product;

import com.snbc.bbpf.bus.product.market.common.dto.product.ProductSimpleDto;
import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import lombok.Data;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.resp
 * @ClassName: SeviceInfoResp
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/9/8 10:20
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/9/8 10:20
 */
@Data
public class SeviceInfoResp {


    private CallResponse head;


    private ProductSimpleDto body;

}
