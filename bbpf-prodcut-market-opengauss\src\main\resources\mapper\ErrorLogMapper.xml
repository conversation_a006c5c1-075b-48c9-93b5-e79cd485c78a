<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ErrorLogMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.ErrorLog" >
    <result column="error_log_id" property="errorLogId" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="log_type" property="logType" jdbcType="INTEGER" />
    <result column="request_param" property="requestParam" jdbcType="VARCHAR" />
    <result column="request_time" property="requestTime" jdbcType="TIMESTAMP" />
    <result column="request_url" property="requestUrl" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ErrorLog" >
    insert into t_error_log (error_log_id, tenant_id, log_type, 
      request_param, request_time, request_url
      )
    values (#{errorLogId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{logType,jdbcType=INTEGER}, 
      #{requestParam,jdbcType=VARCHAR}, #{requestTime,jdbcType=TIMESTAMP}, #{requestUrl,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ErrorLog" >
    insert into t_error_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="errorLogId != null" >
        error_log_id,
      </if>
      <if test="tenantId != null" >
        tenant_id,
      </if>
      <if test="logType != null" >
        log_type,
      </if>
      <if test="requestParam != null" >
        request_param,
      </if>
      <if test="requestTime != null" >
        request_time,
      </if>
      <if test="requestUrl != null" >
        request_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="errorLogId != null" >
        #{errorLogId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="logType != null" >
        #{logType,jdbcType=INTEGER},
      </if>
      <if test="requestParam != null" >
        #{requestParam,jdbcType=VARCHAR},
      </if>
      <if test="requestTime != null" >
        #{requestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestUrl != null" >
        #{requestUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>