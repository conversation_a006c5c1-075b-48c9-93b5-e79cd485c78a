package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.ProductRefund;
import org.apache.ibatis.annotations.Mapper;
/**
 * @author: LiangJB
 * 功能描述: <br>
 * 服务退款表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface ProductRefundMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 删除数据
     * @date: 2020/8/12 13:21
     */
    int deleteByPrimaryKey(String refundId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(ProductRefund productRefund);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(ProductRefund productRefund);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 获取数据
     * @date: 2020/8/12 13:21
     */
    ProductRefund selectByPrimaryKey(String refundId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKeySelective(ProductRefund productRefund);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKey(ProductRefund productRefund);
}
