package com.snbc.bbpf.bus.product.manager.config.payconfig;


import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import java.util.Objects;

/**
 * 功能描述: <br>
 * 〈微信详情〉
 *
 * @param: null
 * @return:
 * @exception
 * @author: gs
 * @date: 2019-08-08 16:28
 */

@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-04-26T10:08:21.632Z")

public class WechatDetailDto {
    @JsonProperty("costPrice")
    private String costPrice;

    @JsonProperty("goodsDetailList")
    private GoodsDetailList goodsDetailList;

    @JsonProperty("receiptId")
    private String receiptId;

    public WechatDetailDto costPrice(String costPrice) {
        this.costPrice = costPrice;
        return this;
    }

    /**
     * 订单原价
     *
     * @return costPrice
     **/



    public String getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(String costPrice) {
        this.costPrice = costPrice;
    }

    public WechatDetailDto goodsDetailList(GoodsDetailList goodsDetailList) {
        this.goodsDetailList = goodsDetailList;
        return this;
    }

    /**
     * 商品详情列表
     *
     * @return goodsDetailList
     **/


    @Valid

    public GoodsDetailList getGoodsDetailList() {
        return goodsDetailList;
    }

    public void setGoodsDetailList(GoodsDetailList goodsDetailList) {
        this.goodsDetailList = goodsDetailList;
    }

    public WechatDetailDto receiptId(String receiptId) {
        this.receiptId = receiptId;
        return this;
    }

    /**
     * 商家小票ID
     *
     * @return receiptId
     **/



    public String getReceiptId() {
        return receiptId;
    }

    public void setReceiptId(String receiptId) {
        this.receiptId = receiptId;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WechatDetailDto wechatDetail = (WechatDetailDto) o;
        return Objects.equals(this.costPrice, wechatDetail.costPrice) &&
                Objects.equals(this.goodsDetailList, wechatDetail.goodsDetailList) &&
                Objects.equals(this.receiptId, wechatDetail.receiptId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(costPrice, goodsDetailList, receiptId);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class WechatDetail {\n");

        sb.append("    costPrice: ").append(toIndentedString(costPrice)).append("\n");
        sb.append("    goodsDetailList: ").append(toIndentedString(goodsDetailList)).append("\n");
        sb.append("    receiptId: ").append(toIndentedString(receiptId)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

