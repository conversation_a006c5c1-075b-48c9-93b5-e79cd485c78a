package com.snbc.bbpf.bus.product.manager.converter;

import com.snbc.bbpf.bus.product.market.common.dto.product.PermissionNode;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import com.snbc.bbpf.commons.objs.IConvert;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * Permission 到 PermissionNode 的转换器
 */
@Mapper
public interface PermissionToPermissionNodeConverter extends IConvert<Permission, PermissionNode> {
    PermissionToPermissionNodeConverter INSTANCE = Mappers.getMapper(PermissionToPermissionNodeConverter.class);
    
    @Mappings({
        @Mapping(source = "permissionDesc", target = "remarks"),
        @Mapping(source = "permissionImage", target = "permissionIcon"),
        @Mapping(source = "permissionId", target = "permissionId"),
        @Mapping(source = "permissionName", target = "permissionName"),
        @Mapping(source = "parentId", target = "parentId"),
        @Mapping(source = "parentName", target = "parentName"),
        @Mapping(source = "permissionLevel", target = "level"),
        @Mapping(source = "routingUrl", target = "routingUrl"),
        @Mapping(source = "permissionType", target = "permissionType"),
        @Mapping(source = "hasEnable", target = "hasEnable"),
        @Mapping(source = "orderBy", target = "orderBy"),
        @Mapping(source = "permissionCode", target = "permissionCode")
    })
    @Override
    PermissionNode to(Permission source);
    
    @Mappings({
        @Mapping(source = "remarks", target = "permissionDesc"),
        @Mapping(source = "permissionIcon", target = "permissionImage"),
        @Mapping(source = "permissionId", target = "permissionId"),
        @Mapping(source = "permissionName", target = "permissionName"),
        @Mapping(source = "parentId", target = "parentId"),
        @Mapping(source = "parentName", target = "parentName"),
        @Mapping(source = "level", target = "permissionLevel"),
        @Mapping(source = "routingUrl", target = "routingUrl"),
        @Mapping(source = "permissionType", target = "permissionType"),
        @Mapping(source = "hasEnable", target = "hasEnable"),
        @Mapping(source = "orderBy", target = "orderBy"),
        @Mapping(source = "permissionCode", target = "permissionCode")
    })
    @Override
    Permission from(PermissionNode source);
}