/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.controller.rrq
 * @ClassName: ProductOrderController
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 人人取运营系统产品服务订单接口
 * @Author: ouyang，liangjunbin
 * @CreateDate: 2020/8/24 14:55
 * @UpdateUser: ouyang
 * @UpdateDate: 2020/10/10 14:55
 */
package com.snbc.bbpf.bus.product.manager.controller.tenant;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.resp.product.AvailableDetailResp;
import com.snbc.bbpf.bus.product.manager.resp.product.OrderDetailResp;
import com.snbc.bbpf.bus.product.manager.resp.product.PeoductOrderDetailResp;
import com.snbc.bbpf.bus.product.manager.resp.product.ProductAvailablePageResp;
import com.snbc.bbpf.bus.product.manager.resp.product.PurchVemsPageResp;
import com.snbc.bbpf.bus.product.manager.resp.product.ServiceOrderPageResp;
import com.snbc.bbpf.bus.product.manager.service.ProductOrderService;
import com.snbc.bbpf.bus.product.manager.utils.ProductQueryUtil;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductAvailablePageDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductLOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceBuyPDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceShopOrderDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductAvailablePageQueryBase;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderVemsPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductShopVemsPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductOrderPageQueryEx;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductShopVemsPageQueryEx;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;


@RestController
@RequestMapping("/console/v1/tenant/productorder")
public class ProductOrderController {
    @Autowired
    private ProductOrderService productOrderService;

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 2.5.2.1. 设备标准列表
     * @date: 2020/8/12 13:21
     * @param: ProductShopVemsPageQuery
     * @return: ServiceBuyPDto
     */
    
    @GetMapping(value = "/sevicesShopListWithPage", produces = {"application/json"})
    public PurchVemsPageResp sevicesShopListWithPage(ProductShopVemsPageQueryEx recordEx) {
        CallResponse result = new CallResponse();
        PurchVemsPageResp purchVemsPageResp = new PurchVemsPageResp();
        ProductShopVemsPageQuery record = ProductQueryUtil.convertProductShopVemsPageQuery(recordEx);
        if (null == record.getTenantId() || "".equals(record.getTenantId())) {
            result.setCode(Errors.FAILED.getCode());
            result.setMessage("租户ID不能为空。");
        } else {
            PageInfo<ServiceBuyPDto> serviceBuyPDtoLst = productOrderService.sevicesShopListWithPage(record);
            result.setCode(Errors.SUCCESS.getCode());
            result.setMessage(Errors.SUCCESS.getMessage());
            purchVemsPageResp.setBody(serviceBuyPDtoLst);
        }
        purchVemsPageResp.setHead(result);
        return purchVemsPageResp;
    }

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 2.3.1. 人人取服务订单列表
     * @date: 2020/8/12 13:21
     * @param: ProductOrderPageQuery
     * @return: ServiceShopOrderDto
     */
    
    @PostMapping(value = "/seviceOrderListWithPage", produces = {"application/json"})
    public ServiceOrderPageResp tenantSeviceOrderListWithPage(@Valid @RequestBody ProductOrderPageQueryEx rrqrecordEx) {
        CallResponse result = new CallResponse();
        ServiceOrderPageResp serviceOrderPageResp = new ServiceOrderPageResp();
        ProductOrderPageQuery rrqrecord = ProductQueryUtil.convertProductOrderPageQuery(rrqrecordEx);
        if (null == rrqrecord.getTenantId() || "".equals(rrqrecord.getTenantId())) {
            result.setCode(Errors.FAILED.getCode());
            result.setMessage(Errors.FAILED.getMessage());
        } else {
            PageInfo<ServiceShopOrderDto> serviceShopOrderDtoLst = productOrderService.rrqserviceOrderListWithPage(rrqrecord);
            result.setCode(Errors.SUCCESS.getCode());
            result.setMessage(Errors.SUCCESS.getMessage());
            serviceOrderPageResp.setBody(serviceShopOrderDtoLst);
        }
        serviceOrderPageResp.setHead(result);
        return serviceOrderPageResp;
    }

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 1.6.2.1. 订单详情（修改） 租户订单详情跟BOSS订单增加了两个字段
     * 服务类型：标准版
     * 收费方式：按设备/天收费
     * @date: 2020/8/12 13:21
     * @param: purchaseNo
     * @return: ProductOrderDetailDto
     */
    
    @GetMapping(value = "/orderDetail", produces = {"application/json"})
    public OrderDetailResp orderDetail( String purchaseNo) {

        CallResponse result = new CallResponse();
        OrderDetailResp orderDetailResp = new OrderDetailResp();
        //填充获取数据列表
        ProductOrderDetailDto productOrderDetailDto = productOrderService.orderDetail(purchaseNo);
        result.setCode(Errors.SUCCESS.getCode());
        result.setMessage(Errors.SUCCESS.getMessage());
        orderDetailResp.setBody(productOrderDetailDto);
        orderDetailResp.setHead(result);
        return orderDetailResp;
    }

    
    @PostMapping(value = "/seviceAvailableListWithPage", produces = {"application/json"})
    public ProductAvailablePageResp seviceAvailableListWithPage(
            @Valid @RequestBody ProductAvailablePageQueryBase productAvailablePageQueryBase) {
        CallResponse result = new CallResponse();
        ProductAvailablePageResp productAvailablePageResp = new ProductAvailablePageResp();
        PageInfo<ProductAvailablePageDto> productAvailablePageDtoList = productOrderService.seviceAvailableListWithPage(productAvailablePageQueryBase);
        result.setCode(Errors.SUCCESS.getCode());
        result.setMessage(Errors.SUCCESS.getMessage());
        productAvailablePageResp.setBody(productAvailablePageDtoList);
        productAvailablePageResp.setHead(result);
        return productAvailablePageResp;
    }
    
    @GetMapping(value = "/availableDetail", produces = {"application/json"})
    public AvailableDetailResp availableDetail(
            @RequestParam("availableId") String availableId, @RequestParam("tenantId") String tenantId) {

        CallResponse result = new CallResponse();
        AvailableDetailResp availableDetailResp = new AvailableDetailResp();
        //填充获取数据列表
        ProductAvailablePageQueryBase productAvailablePageQueryBase = new ProductAvailablePageQueryBase();
        productAvailablePageQueryBase.setAvailableId(availableId);
        productAvailablePageQueryBase.setTenantId(tenantId);
        ProductAvailablePageDto productOrderDetailDto = productOrderService.availableDetail(availableId,tenantId);
        result.setCode(Errors.SUCCESS.getCode());
        result.setMessage(Errors.SUCCESS.getMessage());
        availableDetailResp.setBody(productOrderDetailDto);
        availableDetailResp.setHead(result);
        return availableDetailResp;
    }

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 服务订单详细记录
     * @date: 2020/8/12 13:21
     * @param: ProductOrderVemsPageQuery
     * @return: ProductLOrderDto
     */
    
    @GetMapping(value = "/seviceVemsOrderListWithPage", produces = {"application/json"})
    public PeoductOrderDetailResp seviceVemsOrderListWithPage(ProductOrderVemsPageQuery record) {
        CallResponse result = new CallResponse();
        PeoductOrderDetailResp peoductOrderDetailResp = new PeoductOrderDetailResp();
        PageInfo<ProductLOrderDto> productOrderDtoLst = productOrderService.seviceVemsOrderListWithPage(record);
        result.setCode(Errors.SUCCESS.getCode());
        result.setMessage(Errors.SUCCESS.getMessage());
        peoductOrderDetailResp.setBody(productOrderDtoLst);
        peoductOrderDetailResp.setHead(result);
        return peoductOrderDetailResp;
    }
}
