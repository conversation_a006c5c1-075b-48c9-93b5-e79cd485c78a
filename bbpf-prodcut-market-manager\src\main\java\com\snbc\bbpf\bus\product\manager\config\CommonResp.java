package com.snbc.bbpf.bus.product.manager.config;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020-09-10 11:05
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonResp<T> {
    
    private CallResponse head;
    
    private T body;
    //获取响应head内容
    public CallResponse getHead() {
        return head;
    }
    //设置响应head内容
    public void setHead(CallResponse head) {
        this.head = head;
    }
    //获取响应body内容
    public T getBody() {
        return body;
    }
    //设置响应body内容
    public void setBody(T body) {
        this.body = body;
    }
}
