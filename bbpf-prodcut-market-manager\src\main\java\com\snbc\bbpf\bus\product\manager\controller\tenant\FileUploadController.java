package com.snbc.bbpf.bus.product.manager.controller.tenant;

import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.resp.product.ProductCommonResp;
import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import com.snbc.component.filestorage.args.UploadArg;
import com.snbc.component.filestorage.args.UploadResult;
import com.snbc.component.filestorage.fileutil.FileUpAndDownload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.controller
 * @ClassName: FileUploadController
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 文件上传接口
 * @Author: ouyang
 * @CreateDate: 2020/8/24 14:55
 * @UpdateUser: ouyang
 * @UpdateDate: 2020/10/10 14:55
 */
@RestController

@RequestMapping("/console/v1/tenant/productservice")
public class FileUploadController {
    private static final Object lockObj = new Object();
    private static final Logger LOGGER = LoggerFactory.getLogger(FileUploadController.class);
    //文件上传下载
    @Autowired
    private FileUpAndDownload fileUpAndDownload;
    @Value("${bbpf.oss.config.uploadPath}")
    private String userAvatarPath;

    /***
      * @Description:    服务发布上传文件
      * @Author:         wangsong
      * @param :         file
      * @CreateDate:     2021/11/24 15:34
      * @UpdateDate:     2021/11/24 15:34
      * @return :        com.snbc.bbpf.bus.product.manager.resp.product.ProductCommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @RequestMapping(value = "/fileUpload", method = RequestMethod.POST)
    //@Buslog(opration = "上传文件", zh = "头像修改为=${file}",en="",target = "文件上传管理")
    public ProductCommonResp fileUpload(@RequestParam("file") MultipartFile file) throws Exception {
        ProductCommonResp response = new ProductCommonResp();
        CallResponse header = new CallResponse();
        response.setHead(header);
        synchronized (lockObj) {
            DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
            String uuid = UUID.randomUUID().toString();
            UploadArg fileArg = (new UploadArg.UploadArgBuilder(userAvatarPath + uuid +
                    File.separator + dtf2.format(LocalDateTime.now())
                    + file.getOriginalFilename(),
                    file.getInputStream(), uuid,file.getSize())).override(true).build();
            UploadResult result = fileUpAndDownload.upload(fileArg);
            if (result.getCode().equals(Errors.SUCCESS.getCode())){
                header.setCode(Errors.SUCCESS.getCode());
                header.setMessage(Errors.SUCCESS.getMessage());
                response.setBody(result.getFileUrl());
            } else {
                header.setCode(result.getCode());
                header.setMessage(result.getMessage());
            }
        }
        return response;
    }
}
