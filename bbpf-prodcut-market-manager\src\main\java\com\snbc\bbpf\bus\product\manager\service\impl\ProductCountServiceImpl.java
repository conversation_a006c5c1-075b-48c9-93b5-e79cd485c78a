/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: ProductCountServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 服务统计相关接口实现
 * @Author: LiangJb
 * @CreateDate: 2020/9/28 15:59
 */
package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.service.ProductCountService;
import com.snbc.bbpf.bus.product.manager.utils.ProductQueryUtil;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountIdDto;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductCountMapper;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCount;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCountPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.RPOrderQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class ProductCountServiceImpl implements ProductCountService {


    @Autowired
    private ProductCountMapper productCountMapper;

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 查询产品服务统计
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: com.snbc.vems.product.dto.product.ProductAllCount
     */
    @Override
    public ProductAllCount seviceCountWithPage(ProductCountQuery productCountQuery) {
        return productCountMapper.getSeviceCountByType(productCountQuery);
    }        //按类型进行服务统计列表展示

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 查询产品服务统计列表
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: ProductCountDto
     */
    @Override
    public PageInfo<ProductCountDto> seviceCountListWithPage(ProductCountPageQuery productCountPageQuery) {
        PageHelper.startPage(productCountPageQuery.getPageNum(), productCountPageQuery.getPageSize());
        List<ProductCountDto> productList = productCountMapper.getSeviceCountList(productCountPageQuery);
        productList.forEach(pitem ->
                pitem.setProductGradeUnit(ProductQueryUtil.getProductGradeUnit(pitem.getChargeTypeName()))
        );
        return new PageInfo<>(productList);
    }



    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 查询产品服务月报统计 带分页效果
     * 适用于商户订单月报查询
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: com.snbc.vems.product.dto.product.ShopMRPCountDto
     */
    @Override
    public PageInfo<ShopMRPCountDto> seviceMRPCountListWithPage(ProductMRPCountPageQuery productCountPageQuery) {
        PageHelper.startPage(productCountPageQuery.getPageNum(), productCountPageQuery.getPageSize());
        List<ShopMRPCountDto> productList = productCountMapper.getSeviceMRPCountReport(productCountPageQuery);
        return new PageInfo<>(productList);
    }
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 查询产品服务月报统计  不带分页效果
     * 适用于商户订单月报查询页面 下载 适用于邮件定时发送订单月报
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: com.snbc.vems.product.dto.product.ShopMRPCountDto
     */
    @Override
    public List<ShopMRPCountDto> getSeviceMRPCountList(ProductMRPCount productCount) {
        return productCountMapper.getSeviceMRPCountReport(productCount);
    }
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 查询产品服务月报统计标头
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: com.snbc.vems.product.dto.product.ShopMRPCountDto
     */
    @Override
    public ShopMRPAllCount getSeviceMRPCountSum(ProductMRPCount productCount) {
        return productCountMapper.getSeviceMRPCountSum(productCount);
    }
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 查询产品服务日报统计
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: com.snbc.vems.product.dto.product.ShopMRPCountDto
     */
    @Override
    public List<ShopMRPCountDto> getSeviceDRPCountList(ProductMRPCount productCount) {
        return productCountMapper.getSeviceDRPCountReport(productCount);
    }
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 查询产品服务日报统计标头
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: com.snbc.vems.product.dto.product.ShopMRPCountDto
     */
    @Override
    public ShopMRPAllCount getSeviceDRPCountSum(ProductMRPCount productCount) {
        return productCountMapper.getSeviceDRPCountSum(productCount);
    }
    /**
     * @author: LiangJB
     * 日期统计报表
     * @param recoud
     * @return
     */
    @Override
    public List<ShopMRPCountIdDto> getSeviceRPList(RPOrderQuery recoud) {
        return productCountMapper.getSeviceRPByTenant(recoud);
    }
    /**
     * @author: LiangJB
     * 插入日报表
     * @param rpOrderQuery
     * @return
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public int insertSeviceRPByTenant(RPOrderQuery rpOrderQuery){
        productCountMapper.deleteSeviceRPByTenant(rpOrderQuery.getReportTime());
        return productCountMapper.insertSeviceRPByTenant(rpOrderQuery);
    }
    /**
     * @author: LiangJB
     * 插入月报表
     * @param rpOrderQuery
     * @return
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public int insertSeviceRPByTenantM(RPOrderQuery rpOrderQuery){
        productCountMapper.deleteSeviceRPByTenantM(rpOrderQuery.getReportTime());
        return productCountMapper.insertSeviceRPByTenantM(rpOrderQuery);
    }
    /**
     * @author: LiangJB
     * 删除日报表
     * @param reportDay
     * @return
     */
    @Override
    public int deleteSeviceRPByTenant(String reportDay){
        return productCountMapper.deleteSeviceRPByTenant(reportDay);
    }
    /**
     * @author: LiangJB
     * 删除月报表
     * @param reportDay
     * @return
     */
    @Override
    public int deleteSeviceRPByTenantM(String reportDay){
        return productCountMapper.deleteSeviceRPByTenantM(reportDay);
    }
}
