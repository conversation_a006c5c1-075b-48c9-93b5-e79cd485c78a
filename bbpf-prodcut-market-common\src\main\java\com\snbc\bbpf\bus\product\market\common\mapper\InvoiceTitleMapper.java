package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.InvoiceTitle;
import org.apache.ibatis.annotations.Mapper;
@Mapper
public interface InvoiceTitleMapper {
    /***     
      * @Description:    新增发票抬头信息
      * @Author:         wangsong
      * @param :         record
      * @CreateDate:     2020/8/26 20:44
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/8/26 20:44
      * @return :        int
     */
    int insertInvoiceTitle(InvoiceTitle invoiceTitle);

    /***     
      * @Description:    根据税务证号查看是否存在此信息
      * @Author:         wangsong
      * @param :         taxRegisterNo
      * @CreateDate:     2020/8/26 20:44
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/8/26 20:44
      * @return :        int
     */
    int selectInvoiceByTenantId(String taxRegisterNo);

    //查询商户发票抬头信息
    InvoiceTitle getInvoiceTitleByTenantId(String tenantId);

    void updateInvoiceTit(InvoiceTitle invoiceTitle);
}
