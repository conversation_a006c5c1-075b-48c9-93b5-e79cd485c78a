package com.snbc.bbpf.bus.product.market.common.dto.invoice;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.vo
 * @ClassName: InvoiceDetailVo
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票详情vo
 * @Author: wangsong
 * @CreateDate: 2020/8/25 11:28
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/25 11:28
 */
@Data
@NoArgsConstructor
public class InvoiceDetailBaseDto {
    
    private String expressCompany;

    
    private String expressNo;

    
    private String taxRegisterNo;

    
    private String bankName;

    
    private String bankNo;

    
    private String registerAddress;

    
    private String registerPhonenum;

    
    private String receiverAddress;


}
