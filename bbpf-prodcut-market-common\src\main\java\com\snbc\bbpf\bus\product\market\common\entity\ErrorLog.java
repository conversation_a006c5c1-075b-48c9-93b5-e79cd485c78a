package com.snbc.bbpf.bus.product.market.common.entity;

import java.time.LocalDateTime;
/**
 * 到期通知频率类型实体类
 */
public class ErrorLog {
    /**
     * 主键ID
     */
    private String errorLogId;
    /**
     * 关联编号
     */
    private String tenantId;
    /**
     * 日志类型
     */
    private Integer logType;
    /**
     * 请求参数
     */
    private String requestParam;
    /**
     * 请求时间
     */
    private LocalDateTime requestTime;
    /**
     * 请求地址
     */
    private String requestUrl;

    public String getErrorLogId() {
        return errorLogId;
    }
    /**
     * 设置errorLogId
     * @param errorLogId
     */
    public void setErrorLogId(String errorLogId) {
        this.errorLogId = errorLogId == null ? null : errorLogId.trim();
    }

    public String getTenantId() {
        return tenantId;
    }
    /**
     * 设置tenantId
     * @param tenantId
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    public Integer getLogType() {
        return logType;
    }

    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    public String getRequestParam() {
        return requestParam;
    }
    /**
     * 设置requestParam
     * @param requestParam
     */
    public void setRequestParam(String requestParam) {
        this.requestParam = requestParam == null ? null : requestParam.trim();
    }

    public LocalDateTime getRequestTime() {
        return requestTime;
    }
    /**
     * 设置requestTime
     * @param requestTime
     */
    public void setRequestTime(LocalDateTime requestTime) {
        this.requestTime = requestTime;
    }

    public String getRequestUrl() {
        return requestUrl;
    }
    /**
     * 设置requestUrl
     * @param requestUrl
     */
    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl == null ? null : requestUrl.trim();
    }
}
