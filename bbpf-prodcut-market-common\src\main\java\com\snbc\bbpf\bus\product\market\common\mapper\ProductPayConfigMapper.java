package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.ProductPayConfig;
import org.apache.ibatis.annotations.Mapper;
/**
 * @author: Lirui
 * 功能描述: <br>
 * 支付描述接口
 * @date: 2020/8/12 13:26
 * @param: record
 * @return: com.snbc.vems.product.dto.product.ProductOrderListDto
 */
@Mapper
public interface ProductPayConfigMapper {
    //删除
    int deleteByPrimaryKey(String productPayconfigId);
//新增
    int insert(ProductPayConfig productPayConfig);
//查询
    int insertSelective(ProductPayConfig productPayConfig);
//查询
    ProductPayConfig selectByPrimaryKey(String productPayconfigId);
//修改
    int updateByPrimaryKeySelective(ProductPayConfig productPayConfig);
//修改
    int updateByPrimaryKey(ProductPayConfig productPayConfig);
}
