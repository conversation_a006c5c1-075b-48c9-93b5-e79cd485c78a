package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RemittanceDto {
    //汇款单id
    
    private String id;
    //订单编号
    
    private String purchaseNo;
    //汇款编号
    
    private String remitTradeNo;
    //汇款银行名称
    
    private String remitBankName;
    //汇款账号
    
    private String remitBankNo;
    //汇款凭证地址
    
    private String remitProof;
    //remitTime
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime remitTime;

}
