/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.event;

import org.springframework.context.ApplicationEvent;

/**
 * @ClassName: SendMsgEvent
 * 异步发送消息
 * @module: si-bbpf-product-market
 * @Author: wjc1
 * @date: 2023/6/8 16:37
 */
public class SendMsgEvent extends ApplicationEvent {
    private final String purchaseNo;
    private final String msgType;
    public SendMsgEvent(Object source, String purchaseNo, String msgType) {
        super(source);
        this.purchaseNo = purchaseNo;
        this.msgType = msgType;
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }

    public String getMsgType() {
        return msgType;
    }
}
