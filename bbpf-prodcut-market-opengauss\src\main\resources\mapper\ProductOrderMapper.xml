<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductOrderMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderListDto">
        <result column="product_id" jdbcType="VARCHAR" property="productId" />
        <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="tenant_name" jdbcType="VARCHAR" property="shopName" />

        <result column="product_category" jdbcType="VARCHAR" property="productCategoryName" />
        <result column="product_type" jdbcType="VARCHAR" property="productTypeName" />
        <result column="charge_type" jdbcType="VARCHAR" property="chargeTypeName" />
        <result column="pay_type" jdbcType="VARCHAR" property="payTypeName" />

        <result column="product_quantity" jdbcType="INTEGER" property="productQuantity" />
        <result column="purchase_amount" jdbcType="DECIMAL" property="purchaseAmount" />
        <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
        <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />

        <result column="purchase_status" jdbcType="INTEGER" property="purchaseStatus" />
        <result column="purchase_time" jdbcType="TIMESTAMP" property="purchaseTime" />
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />

        <result column="product_grade" jdbcType="VARCHAR" property="gradeDiscount" />
        <result column="product_price" jdbcType="DECIMAL" property="price" />
    </resultMap>


    <sql id="Base_Column_List">
        tpp.product_id,purchase_no, product_name, tenant_id, tpp.purchase_status, tpp.pay_type,
          tps.product_category,tenant_name, product_grade, product_price, product_quantity, purchase_amount,
          payment_amount, discount_amount, purchase_status, purchase_time, pay_time, cancel_time,
          tps.product_type, tps.product_image productImage, tpp.product_grade productGrade, tps.charge_type
  </sql>
    <select id="selectProductOrderList" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderPageQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from    t_product_purchase tpp
        left join t_product_services tps  on tpp.product_id=tps.product_id
        where purchase_status != -1
        <if test = "productName != null and productName != ''">
            and tps.product_name like CONCAT ('%',#{productName,jdbcType = VARCHAR},'%')
        </if>
        <if test="productId != null and productId !=''">
            and tpp.product_id = #{productId,jdbcType = VARCHAR}
        </if>
        <if test = "shopName != null and shopName != ''">
            and tpp.tenant_name like CONCAT ('%',#{shopName,jdbcType = VARCHAR},'%')
        </if>
        <if test="tenantId != null and tenantId != ''">
            and tpp.tenant_id = #{tenantId,jdbcType = VARCHAR}
        </if>
        <if test = "purchaseNo != null and purchaseNo != ''">
            and tpp.purchase_no like CONCAT ('%',#{purchaseNo,jdbcType = VARCHAR},'%')
        </if>
        <if test = "productType != 0">
            and tps.product_type = #{productType,jdbcType=INTEGER}
        </if>
        <if test = "chargeType != 0">
            and tps.charge_type = #{chargeType,jdbcType=INTEGER}
        </if>
        <if test = "payType != -1">
            and tpp.pay_type = #{payType,jdbcType=INTEGER}
        </if>
        <if test = "purchaseStatus != -1">
            and tpp.purchase_status = #{purchaseStatus,jdbcType=INTEGER}
        </if>
        <if test="startTime != null and startTime !=''">
            <![CDATA[ and tpp.pay_time >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime != null and endTime !=''">
            <![CDATA[ and tpp.pay_time <= #{endTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="startOrderTime != null and startOrderTime !=''">
            <![CDATA[ and tpp.purchase_time >= #{startOrderTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endOrderTime != null and endOrderTime !=''">
            <![CDATA[ and tpp.purchase_time <= #{endOrderTime,jdbcType=TIMESTAMP} ]]>
        </if>
        order by
        <choose>
            <when test="sortField == 'purchaseNo'">
                tpp.purchase_no
            </when>
            <when test="sortField == 'purchaseTime'">
                tpp.purchase_time
            </when>
            <when test="sortField == 'payTime'">
                tpp.pay_time
            </when>
            <when test="sortField == 'productName'">
                tps.product_name
            </when>
            <otherwise>
                tpp.purchase_time
            </otherwise>
        </choose>
        <choose>
            <when test="sortRule == 'asc' or sortRule == 'desc'">
                ${sortRule}
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="getProductRecordLstByPurchaseNo" parameterType="java.lang.String" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ProductLOrderDto">
        select
        vems_id vemsId,vems_name vemsName, product_grade gradeDiscount,product_price price,due_time dueTime,
        tpa.product_quantity  quantity,
        tpa.purchase_amount purchaseAmount,tpa.payment_amount paymentAmount,
        tpa.discount_amount discountAmount
        from t_product_detail tpa
        LEFT JOIN t_product_purchase tpp on tpa.product_purchase_id=tpp.product_purchase_id
        where 0=1
        <if test="purchaseNo != null and purchaseNo != ''">
            or purchase_no = #{purchaseNo}
        </if>
    </select>

    <select id="getServiceBuyLstByTenantId" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductShopVemsPageQuery" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ServiceBuyPDto">
        select
        tps.product_code productCode, vems_id vemsId,vems_name vemsName,tpa.product_id productId,
        activation_time beginTime,due_time dueTime, begin_time activationTime,
        TIMESTAMPDIFF(MONTH, NOW(), due_time) serviceStatus,'' vemsPic, '未启用' vemsStatus,'' vemsAddress,
        100 servicePercent
        from t_product_available tpa
        LEFT JOIN t_product_services tps on tpa.product_id=tps.product_id
        where 1=1
        <if test="tenantId != null and tenantId != ''">
            and tpa.tenant_id = #{tenantId}
        </if>
        <if test="productCode != null and productCode != ''">
            and tps.product_code = #{productCode}
        </if>
        <if test = "vemsName != null and vemsName != ''">
            and vems_name like CONCAT ('%',#{vemsName,jdbcType = VARCHAR},'%')
        </if>

        <if test = "dateType != 2">
            <if test="startTime != null and startTime !=''">
                <![CDATA[ and due_time >= #{startTime,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="endTime != null and endTime !=''">
                <![CDATA[ and due_time < #{endTime,jdbcType=TIMESTAMP} ]]>
            </if>
        </if>
        <if test = "dateType == 2">
            <if test="endTime != null and endTime !=''">
                <![CDATA[ and due_time < #{endTime,jdbcType=TIMESTAMP} ]]>
            </if>
        </if>
        order by due_time
    </select>


    <select id="getProductOrderDetailByPurchaseNo" parameterType="java.lang.String" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDetailDto">
        select tpu.value_name productgradeUnit,
        purchase_no purchaseNo,product_name productName, tenant_name  shopName,tpp.product_id productId,
        product_grade gradeDiscount,product_price price,
        product_quantity productQuantity,purchase_amount purchaseAmount,payment_amount paymentAmount,discount_amount discountAmount,
        tpc.value_name      payTypeName,tpp.pay_type      payType,
        tpp.purchase_status purchaseStatus,tpp.purchase_time purchaseTime,pay_time payTime,
        success_time successTime,purchase_channel_no  purchaseChannelNo,
        tpt.value_name     productTypeName,tct.value_name chargeTypeName,
        cancel_time cancelTime, tpss.value_name purchaseStatusName,tps.product_image productImage,tpp.brankstart_time brankstartTime,tps.probation probation
        from t_product_purchase tpp
        left join t_product_services tps  on tpp.product_id=tps.product_id
        left join t_product_grade tpg on tpg.product_id=tpp.product_id and tpg.grade =tpp.product_grade
        left join t_purchase_online tpo on tpp.purchase_no=tpo.product_purchase_id
        left join (select value_code,value_name from t_dict_value  where type_code='purchase_status') tpss on tpp.purchase_status=tpss.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='product_type') tpt on tps.product_type=tpt.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='charge_type') tct on tps.charge_type=tct.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='pay_type') tpc on tpp.pay_type=tpc.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='charge_type_unit') tpu on tpg.grade_unit=tpu.value_code
        where 0=1
        <if test="purchaseNo != null and purchaseNo != ''">
            or purchase_no = #{purchaseNo}
        </if>
    </select>
    <select id="getOrderLstByVemsId" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.VemsOrderPageQueryBase" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductOrderDto">
        SELECT
        purchase_No purchaseNo,	tenant_Name shopName	,tct.value_name payTypeName	,tps.product_Name productName	,
        tpd.begin_Time beginTime,purchase_time	 dueTime,product_grade availableValue,
        give_username giveUserName,give_reason giveReason
        from t_product_detail tpd
        LEFT JOIN t_product_services tps on tpd.product_id = tps.product_id
        left join t_product_purchase tpp on tpd.product_purchase_id = tpp.product_purchase_id
        left join (select value_code,value_name from t_dict_value  where type_code='pay_type') tct on tpp.pay_type=tct.value_code
        where purchase_status=2 and pay_type =98
        <if test="vemsId != null and vemsId != ''">
            and vems_id = #{vemsId}
        </if>
        <if test="productCode != null and productCode != ''">
            and tps.product_Code = #{productCode}
        </if>
        order by purchase_time desc
    </select>

    <select id="getOrderLstByPayType" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.VemsOrderDayQueryBase" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductDRPCountDto">
        SELECT
        purchase_time purchaseTime ,product_quantity productQuantity,tenant_Name tenantName,
        purchase_amount purchaseAmount,product_grade productGade,give_userName giveUserName,give_reason giveReason from t_product_purchase
        where purchase_status=2
        <if test="payType != null and payType != ''">
            and pay_type = #{payType}
        </if>
        <if test="startTime != null and startTime !=''">
            <![CDATA[ and purchase_time >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime != null and endTime !=''">
            <![CDATA[ and purchase_time < #{endTime,jdbcType=TIMESTAMP} ]]>
        </if>
        order by purchase_time desc
    </select>

    <select id="getOrderCountByPayType" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.VemsOrderDayQueryBase" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductDRPAllCount">
        SELECT sum(product_quantity) serviceCount,
        sum(purchase_amount) serviceCountAmount from t_product_purchase
        where purchase_status=2
        <if test="payType != null and payType != ''">
            and pay_type = #{payType}
        </if>
        <if test="startTime != null and startTime !=''">
            <![CDATA[ and purchase_time >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime != null and endTime !=''">
            <![CDATA[ and purchase_time < #{endTime,jdbcType=TIMESTAMP} ]]>
        </if>
    </select>

    <select id="productByDayOrder" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ProducedOrderDto">
      SELECT
          t1.purchase_no AS purchaseNo,
          t2.available_value AS availableValue,
          t1.purchase_amount AS purchaseAmount,
          t1.payment_amount AS paymentAmount,
          t1.discount_amount AS discountAmount,
          t1.pay_type AS payType,
          t1.purchase_time AS purchaseTime,
          t1.pay_time AS payTime
      FROM
          t_product_purchase t1
      LEFT JOIN t_product_detail t2 ON t1.product_purchase_id = t2.product_purchase_id
      WHERE 1 = 1
      AND t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
      AND t1.product_id = #{productId,jdbcType=VARCHAR}
      ORDER BY pay_time desc
  </select>

    <select id="productByCountOrder" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ProducedOrderDto">
      SELECT
          t1.purchase_no AS purchaseNo,
          t2.available_value AS availableValue,
          t1.purchase_amount AS purchaseAmount,
          t1.payment_amount AS paymentAmount,
          t1.discount_amount AS discountAmount,
          t1.pay_type AS payType,
          t1.purchase_time AS purchaseTime,
          t1.pay_time AS payTime
      FROM
          t_product_purchase t1
      LEFT JOIN t_product_detail t2 ON t1.product_purchase_id = t2.product_purchase_id
      WHERE 1 = 1
      AND t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
      AND t1.product_id = #{productId,jdbcType=VARCHAR}
      ORDER BY pay_time desc
  </select>

    <select id="getProductCount" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ProductByConutStatistics">
        SELECT
            sum( t2.available_value ) AS totalPurchases,
            t1.available_value AS remainingCount
        FROM
            t_product_available t1
            LEFT JOIN t_product_detail t2 ON t1.product_id = t2.product_id
            AND t1.tenant_id = t2.tenant_id
        WHERE
            1 = 1
          AND t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
          AND t1.product_id = #{productId,jdbcType=VARCHAR}
        GROUP BY
            t1.available_value
  </select>


    <select id="getProductRemaining" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ProductRemainingDto">
        SELECT
            SUM(available_value) as availableValueRemaining,
            due_time as dueTime
        FROM
            t_product_available
        WHERE
            1 = 1
         AND tenant_id = #{tenantId,jdbcType=VARCHAR}
         AND product_id = #{productId,jdbcType=VARCHAR}
           GROUP BY
            due_time
       ORDER BY
            due_time asc
        LIMIT 1
  </select>

    <!--查询按次计费的服务最近要到期的次数及日期-->
    <select id="getProductExpireInfo" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ProductByConutStatistics">
       SELECT
            SUM(available_value) AS clearCount,
            due_time AS clearDate
       FROM
            t_product_detail
       WHERE
            1 = 1
       AND tenant_id = #{tenantId,jdbcType=VARCHAR}
       AND product_id = #{productId,jdbcType=VARCHAR}
       GROUP BY
            due_time
       ORDER BY
            due_time ASC
        LIMIT 1
  </select>
</mapper>
