package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.market.common.entity.ErrorLog;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import com.snbc.bbpf.bus.product.market.common.mapper.ErrorLogMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PermissionMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * AuthorityServiceImpl 单元测试
 */
@DisplayName("权限服务实现类测试")
class AuthorityServiceImplTest {

    @Mock
    private ErrorLogMapper errorLogMapper;
    
    @Mock
    private PermissionMapper permissionMapper;
    
    @Mock
    private RabbitTemplate rabbitTemplate;

    @InjectMocks
    private AuthorityServiceImpl authorityService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("权限授权 - 成功")
    void testAuthority_Success() {
        // Given
        String productCode = "PROD001";
        String purchaseNo = "PO123456";
        String tenantId = "tenant123";
        
        Permission permission1 = new Permission();
        permission1.setPermissionId("perm1");
        permission1.setPermissionPath("/1/2/3");
        permission1.setPermissionName("权限1");
        
        Permission permission2 = new Permission();
        permission2.setPermissionId("perm2");
        permission2.setPermissionPath("/4/5");
        permission2.setPermissionName("权限2");
        
        List<Permission> perList = Arrays.asList(permission1, permission2);
        
        Permission pathPermission1 = new Permission();
        pathPermission1.setPermissionId("1");
        pathPermission1.setPermissionName("父权限1");
        
        Permission pathPermission2 = new Permission();
        pathPermission2.setPermissionId("2");
        pathPermission2.setPermissionName("父权限2");
        
        List<Permission> pathPermissions = Arrays.asList(pathPermission1, pathPermission2);
        
        when(permissionMapper.getPermissionByPurchaseNo(purchaseNo)).thenReturn(perList);
        when(permissionMapper.selectByIds(any(String[].class))).thenReturn(pathPermissions);
        
        // When
        boolean result = authorityService.authority(productCode, purchaseNo, tenantId);
        
        // Then
        assertFalse(result); // 方法总是返回false
        verify(rabbitTemplate).convertAndSend(
            eq(Constant.PRODUCT_AUTHORITY_EXCHANGE),
            eq(Constant.PRODUCT_AUTHORITY_ROUTING_KEY),
            anyString()
        );
        verify(errorLogMapper, never()).insert(any(ErrorLog.class));
    }

    @Test
    @DisplayName("权限授权 - 无权限列表")
    void testAuthority_NoPermissions() {
        // Given
        String productCode = "PROD001";
        String purchaseNo = "PO123456";
        String tenantId = "tenant123";
        
        when(permissionMapper.getPermissionByPurchaseNo(purchaseNo)).thenReturn(Collections.emptyList());
        
        // When
        boolean result = authorityService.authority(productCode, purchaseNo, tenantId);
        
        // Then
        assertFalse(result);
        verify(rabbitTemplate, never()).convertAndSend(anyString(), anyString(), anyString());
        verify(errorLogMapper, never()).insert(any(ErrorLog.class));
    }

    @Test
    @DisplayName("权限授权 - 权限列表为null")
    void testAuthority_NullPermissions() {
        // Given
        String productCode = "PROD001";
        String purchaseNo = "PO123456";
        String tenantId = "tenant123";
        
        when(permissionMapper.getPermissionByPurchaseNo(purchaseNo)).thenReturn(null);
        
        // When
        boolean result = authorityService.authority(productCode, purchaseNo, tenantId);
        
        // Then
        assertFalse(result);
        verify(rabbitTemplate, never()).convertAndSend(anyString(), anyString(), anyString());
        verify(errorLogMapper, never()).insert(any(ErrorLog.class));
    }

    @Test
    @DisplayName("权限授权 - 异常处理")
    void testAuthority_Exception() {
        // Given
        String productCode = "PROD001";
        String purchaseNo = "PO123456";
        String tenantId = "tenant123";
        
        when(permissionMapper.getPermissionByPurchaseNo(purchaseNo))
            .thenThrow(new RuntimeException("Database error"));
        when(errorLogMapper.insert(any(ErrorLog.class))).thenReturn(1);
        
        // When
        boolean result = authorityService.authority(productCode, purchaseNo, tenantId);
        
        // Then
        assertFalse(result);
        verify(errorLogMapper).insert(any(ErrorLog.class));
        verify(rabbitTemplate, never()).convertAndSend(anyString(), anyString(), anyString());
    }

    @Test
    @DisplayName("权限授权 - 权限路径为空")
    void testAuthority_EmptyPermissionPath() {
        // Given
        String productCode = "PROD001";
        String purchaseNo = "PO123456";
        String tenantId = "tenant123";
        
        Permission permission = new Permission();
        permission.setPermissionId("perm1");
        permission.setPermissionPath("");
        permission.setPermissionName("权限1");
        
        List<Permission> perList = Arrays.asList(permission);
        
        when(permissionMapper.getPermissionByPurchaseNo(purchaseNo)).thenReturn(perList);
        when(permissionMapper.selectByIds(any(String[].class))).thenReturn(Collections.emptyList());
        
        // When
        boolean result = authorityService.authority(productCode, purchaseNo, tenantId);
        
        // Then
        assertFalse(result);
        verify(rabbitTemplate).convertAndSend(
            eq(Constant.PRODUCT_AUTHORITY_EXCHANGE),
            eq(Constant.PRODUCT_AUTHORITY_ROUTING_KEY),
            anyString()
        );
    }

    @Test
    @DisplayName("权限授权 - 权限路径包含空字符串")
    void testAuthority_PermissionPathWithEmptyStrings() {
        // Given
        String productCode = "PROD001";
        String purchaseNo = "PO123456";
        String tenantId = "tenant123";
        
        Permission permission = new Permission();
        permission.setPermissionId("perm1");
        permission.setPermissionPath("/1//2/");
        permission.setPermissionName("权限1");
        
        List<Permission> perList = Arrays.asList(permission);
        
        Permission pathPermission = new Permission();
        pathPermission.setPermissionId("1");
        pathPermission.setPermissionName("父权限1");
        
        List<Permission> pathPermissions = Arrays.asList(pathPermission);
        
        when(permissionMapper.getPermissionByPurchaseNo(purchaseNo)).thenReturn(perList);
        when(permissionMapper.selectByIds(any(String[].class))).thenReturn(pathPermissions);
        
        // When
        boolean result = authorityService.authority(productCode, purchaseNo, tenantId);
        
        // Then
        assertFalse(result);
        verify(rabbitTemplate).convertAndSend(
            eq(Constant.PRODUCT_AUTHORITY_EXCHANGE),
            eq(Constant.PRODUCT_AUTHORITY_ROUTING_KEY),
            anyString()
        );
    }

    @Test
    @DisplayName("权限授权 - MQ发送异常")
    void testAuthority_RabbitMQException() {
        // Given
        String productCode = "PROD001";
        String purchaseNo = "PO123456";
        String tenantId = "tenant123";
        
        Permission permission = new Permission();
        permission.setPermissionId("perm1");
        permission.setPermissionPath("/1/2");
        permission.setPermissionName("权限1");
        
        List<Permission> perList = Arrays.asList(permission);
        
        when(permissionMapper.getPermissionByPurchaseNo(purchaseNo)).thenReturn(perList);
        when(permissionMapper.selectByIds(any(String[].class))).thenReturn(Collections.emptyList());
        doThrow(new RuntimeException("MQ error")).when(rabbitTemplate)
            .convertAndSend(anyString(), anyString(), anyString());
        when(errorLogMapper.insert(any(ErrorLog.class))).thenReturn(1);
        
        // When
        boolean result = authorityService.authority(productCode, purchaseNo, tenantId);
        
        // Then
        assertFalse(result);
        verify(errorLogMapper).insert(any(ErrorLog.class));
    }
}