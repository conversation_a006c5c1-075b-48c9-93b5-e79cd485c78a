package com.snbc.bbpf.bus.product.manager.config.payconfig;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

/**
 * 功能描述: <br>
 * 〈支付宝商品详情〉
 *
 * @param: null
 * @return:
 * @exception
 * @author: gs
 * @date: 2019-08-08 18:40
 */

@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-04-26T10:08:21.632Z")

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GoodsDetailDto {
    /**
     * quantity
     */
    @JsonProperty("quantity")
    private String quantity;

    /**
     * goodsId
     */
    @JsonProperty("goodsId")
    private String goodsId;

    /**
     * price
     */
    @JsonProperty("price")
    private String price;

    /**
     * goodsCategory
     */
    @JsonProperty("goodsCategory")
    private String goodsCategory;

    /**
     * showUrl
     */
    @JsonProperty("showUrl")
    private String showUrl;

    /**
     * body
     */
    @JsonProperty("body")
    private String body;

    /**
     * goodsName
     */
    @JsonProperty("goodsName")
    private String goodsName;

    /**
     * wxpayGoodsId
     */
    @JsonProperty("wxpayGoodsId")
    private String wxpayGoodsId;

}

