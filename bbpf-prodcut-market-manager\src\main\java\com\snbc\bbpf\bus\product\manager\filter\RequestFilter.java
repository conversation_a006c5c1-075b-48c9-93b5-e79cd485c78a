package com.snbc.bbpf.bus.product.manager.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * @ClassName: ParamsFilter
 * @Description: 参数过滤器
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/8/12
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Component
@WebFilter(urlPatterns = "/**", filterName = "ParamsFilter")
public class RequestFilter implements Filter {
    private static final Logger LOGGER = LoggerFactory.getLogger(RequestFilter.class);
    private static final String ROOTPATH = "console";

    /**
     * 过滤filter
     * @param request
     * @param response
     * @param chain
     * @throws IOException
     * @throws ServletException
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServletException, IOException {
        HttpServletRequest hsRequest = (HttpServletRequest) request;
        String path = hsRequest.getRequestURI().substring(hsRequest.getContextPath().length()).replaceAll("[/]+$", "");
        ParamRequestWrapper paramRequestWrapper = new ParamRequestWrapper(hsRequest);
        LOGGER.info("doFilter path={}", path);
        if (path.contains(ROOTPATH) || path.startsWith("/pay")) {
            chain.doFilter(paramRequestWrapper, response);
        } else {
            chain.doFilter(request, response);
        }
    }


    @Override
    public void init(FilterConfig filterConfig) {
        LOGGER.debug("init");
    }

    @Override
    public void destroy() {
        LOGGER.debug("destroy");
    }
}
