/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.market.common.vo.product;

import com.snbc.bbpf.bus.product.market.common.Constant;
import lombok.Data;
import org.springframework.validation.annotation.Validated;


/**
 * <AUTHOR> LiangJb
 * @ClassName : ProductOrderPageQuery
 * @Description : 服务订单查询入参
tenantId	查询条件	String	运营商编号	是	2222
deviceName	查询条件	String	设备名称	否	全部为0
startTime	查询条件	date	服务到期时间	否 	全部传0
endTime	查询条件	date	服务结束时间	否 	全部传0
dateType	查询条件	int	时间查询方式	是	1为默认查询 2为已到期3 为不足一个月



 * pageNum	查询条件	string	页数	是	1
 * pageSize	查询条件	string	每页行数	是	10
 * @Date: 2020-08-12 15:51
 */



@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T06:49:02.983Z")
@Data
public class VemsOrderPageQueryBase {
    
    private Integer pageSize= Constant.TEN;
    
    private Integer pageNum=Constant.ONE;
    
    private String vemsId;
    
    private String productCode;
}
