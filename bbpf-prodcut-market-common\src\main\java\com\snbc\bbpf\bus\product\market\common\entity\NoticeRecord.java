package com.snbc.bbpf.bus.product.market.common.entity;

import java.util.Date;
/**
 * 消息通知记录类型实体类
 */
public class NoticeRecord {
    /**
     * 主键ID
     */
    private String noticeRecordId;
    /**
     * 服务Id
     */
    private String productId;
    /**
     * 通知类型
     */
    private Integer noticeType;
    /**
     * 通知时间
     */
    private Date noticeTime;
    /**
     * 通知内容
     */
    private String noticeContent;
    /**
     * 通知租户
     */
    private String tenantId;
    /**
     * 通知用户
     */
    private String userId;

    public String getNoticeRecordId() {
        return noticeRecordId;
    }
    /**
     * 设置noticeRecordId
     * @param noticeRecordId
     */
    public void setNoticeRecordId(String noticeRecordId) {
        this.noticeRecordId = noticeRecordId == null ? null : noticeRecordId.trim();
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }

    public Integer getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(Integer noticeType) {
        this.noticeType = noticeType;
    }

    public Date getNoticeTime() {
        return noticeTime;
    }

    public void setNoticeTime(Date noticeTime) {
        this.noticeTime = noticeTime;
    }

    public String getNoticeContent() {
        return noticeContent;
    }
    /**
     * 设置noticeContent
     * @param noticeContent
     */
    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent == null ? null : noticeContent.trim();
    }

    public String getTenantId() {
        return tenantId;
    }
    /**
     * 设置tenantId
     * @param tenantId
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    public String getUserId() {
        return userId;
    }
    /**
     * 设置userId
     * @param userId
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }
}
