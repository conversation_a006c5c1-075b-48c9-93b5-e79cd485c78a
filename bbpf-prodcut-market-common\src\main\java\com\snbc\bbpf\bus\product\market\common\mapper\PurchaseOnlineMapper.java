package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.PurchaseOnline;
import org.apache.ibatis.annotations.Mapper;

/*
* 服务线下数据解析
* 作者：欧阳
* 时间2020-10-26
*
* */
@Mapper
public interface PurchaseOnlineMapper {
    //删除
    int deleteByPrimaryKey(String onlineId);
//添加
    int insert(PurchaseOnline purchaseOnline);

    int insertSelective(PurchaseOnline purchaseOnline);

    PurchaseOnline selectByPrimaryKey(String onlineId);

    int updateByPrimaryKeySelective(PurchaseOnline purchaseOnline);

    int updateByPrimaryKey(PurchaseOnline purchaseOnline);
}
