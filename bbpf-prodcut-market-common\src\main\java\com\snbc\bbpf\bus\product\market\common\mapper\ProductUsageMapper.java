package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.ProductUsage;
import org.apache.ibatis.annotations.Mapper;
/**
 * @author: LiangJB
 * 功能描述: <br>
 * 服务使用表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface ProductUsageMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 删除数据
     * @date: 2020/8/12 13:21
     */
    int deleteByPrimaryKey(String usageId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(ProductUsage productUsage);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(ProductUsage productUsage);

    ProductUsage selectByPrimaryKey(String usageId);

    int updateByPrimaryKeySelective(ProductUsage productUsage);

    int updateByPrimaryKey(ProductUsage productUsage);
}
