package com.snbc.bbpf.bus.product.manager.resp.vems;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 *  * <p>
 *  * 版权所有 山东新北洋信息技术股份有限公司
 *  * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 *  * @copyright Copyright: 2014-2020
 *  * @Description: 售货机分页
 *  * @Author: wangsong
 * 分页
 */

@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2017-04-17T01:33:03.272Z")
@Data
public class PageInfo {

    
    @JsonProperty("number")
    private Integer number = null;

    
    @JsonProperty("last")
    private Boolean last = null;

    
    @JsonProperty("numberOfElements")
    private Integer numberOfElements = null;

    
    @JsonProperty("size")
    private Integer size = null;

    
    @JsonProperty("totalPages")
    private Integer totalPages = null;

    
    @JsonProperty("sort")
    private String sort = null;

    
    @JsonProperty("first")
    private Boolean first = null;

    
    @JsonProperty("totalElements")
    private Long totalElements = null;

}

