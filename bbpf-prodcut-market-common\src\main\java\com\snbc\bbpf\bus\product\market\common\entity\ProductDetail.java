package com.snbc.bbpf.bus.product.market.common.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 服务可用情况类型实体类
 */
@Data
public class ProductDetail extends ProductVemsBase {
    /**
     * ID
     */
    private String detailId;
    /**
     * 服务购买Id
     */
    private String productPurchaseId;

    /**
     * 售货机name
     */
    private String vemsName;
    /**
     * 服务开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 服务结束时间
     */
    private LocalDateTime dueTime;
    /**
     * 有效值
     */
    private Integer availableValue;
    /**
     * 有效值单位
     */
    private String availableUnit;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 产品数量
     */
    private Integer productQuantity;
    /**
     * 产品金额
     */
    private BigDecimal purchaseAmount;
    /**
     *优惠金额
     */
    private BigDecimal discountAmount;
    /**
     * 实付金额
     */
    private BigDecimal paymentAmount;
    /**
     * 激活时间
     */
    private LocalDateTime activationTime;
    /**
     * 是否续费
     */
    private Integer isRenew;
}
