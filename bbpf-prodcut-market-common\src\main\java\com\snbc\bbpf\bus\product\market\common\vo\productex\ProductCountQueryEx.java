/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.market.common.vo.productex;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR> LiangJb
 * @ClassName : ProductCountQuery
 * @Description : 服务统计查询入参
productName	查询条件	String	服务名称	否	物联网卡
productCategory	查询条件	int	服务类目	是	全部 传-1
productType	查询条件	int	服务类型	是	全部传-1
chargeType	查询条件	int	服务支付方式	是	全部传-1

 * @Date: 2020-08-12 15:51
 */



@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T06:49:02.983Z")
@Data
public class ProductCountQueryEx {
    
    private String productName;
    
    private String productType;
    
    private String productCategory;
    
    private String chargeType;
}
