package com.snbc.bbpf.bus.product.manager.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountIdDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCount;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCountPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.RPOrderQuery;

import java.util.List;
/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: ProductCountService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 服务统计相关接口
 * @Author: LiangJb
 * @CreateDate: 2020/9/28 15:59
 */
public interface ProductCountService {
    /**
     * @author: LiangJB
     * 按类型进行服务统计
     *
     * @param productCountQuery 原来参数
     * @return
     */
    ProductAllCount seviceCountWithPage(ProductCountQuery productCountQuery);
    /**
     * @author: LiangJB
     * 按类型进行服务统计列表展示
     *
     * @param productCountPageQuery 原来参数
     * @return
     */
    PageInfo<ProductCountDto> seviceCountListWithPage(ProductCountPageQuery productCountPageQuery);


      /**
     * @author: LiangJB
     * 按类型进行服务统计列表展示
     *
     * @param productCountPageQuery 原来参数
     * @return
     */
    PageInfo<ShopMRPCountDto> seviceMRPCountListWithPage(ProductMRPCountPageQuery productCountPageQuery);

    /**
     * @author: LiangJB
     *月统计报表具体列表
     * @param productCount
     * @return
     */
    List<ShopMRPCountDto> getSeviceMRPCountList(ProductMRPCount productCount);

    /**
     * @author: LiangJB
     * 月统计报表标题
     * @param productCount
     * @return
     */
    ShopMRPAllCount getSeviceMRPCountSum(ProductMRPCount productCount);
    /**
     * @author: LiangJB
     *日统计报表具体列表
     * @param productCount
     * @return
     */
    List<ShopMRPCountDto> getSeviceDRPCountList(ProductMRPCount productCount);

    /**
     * @author: LiangJB
     * 日统计报表标题
     * @param productCount
     * @return
     */
    ShopMRPAllCount getSeviceDRPCountSum(ProductMRPCount productCount);
    /**
     * @author: LiangJB
     * 日期统计报表
     * @param recoud
     * @return
     */
    List<ShopMRPCountIdDto> getSeviceRPList(RPOrderQuery recoud);
    /**
     * @author: LiangJB
     * 插入日报表
     * @param rpOrderQuery
     * @return
     */
    int insertSeviceRPByTenant(RPOrderQuery rpOrderQuery);
    /**
     * @author: LiangJB
     * 插入月报表
     * @param rpOrderQuery
     * @return
     */
    int insertSeviceRPByTenantM(RPOrderQuery rpOrderQuery);
    /**
     * @author: LiangJB
     * 删除日报表
     * @param reportDay
     * @return
     */
    int deleteSeviceRPByTenant(String reportDay);
    /**
     * 删除月报表
     * @param reportDay
     * @return
     */
    int deleteSeviceRPByTenantM(String reportDay);
}
