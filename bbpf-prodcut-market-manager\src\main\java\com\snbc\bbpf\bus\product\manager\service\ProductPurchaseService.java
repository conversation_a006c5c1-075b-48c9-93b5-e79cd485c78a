package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;

/**
 * @author: LiangJB
 * 功能描述: <br>
 * 服务订单表数据解析
 * @date: 2020/8/12 13:21
 */
public interface ProductPurchaseService {

    /**
     * @author: liangJB
     * 根据订单号查询订单
     * @param purchaseNo
     * @return
     */
    ProductPurchase selectByPurchaseNo(String purchaseNo);

}
