package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.ProductPayService;
import com.snbc.bbpf.bus.product.manager.service.PurchasePayTrackService;
import com.snbc.bbpf.bus.product.manager.service.RemittanceService;
import com.snbc.bbpf.bus.product.manager.utils.OrderServiceUtils;
import com.snbc.bbpf.bus.product.market.common.dto.order.RemittanceDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductSimpleDto;
import com.snbc.bbpf.bus.product.market.common.entity.ProductDetail;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline;
import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductDetailMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductQueryMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseOfflineMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseTrackMapper;
import com.snbc.bbpf.bus.product.market.common.vo.RemittanceVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;


/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: RemittanceServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 汇款单服务实现类
 * @CreateDate: 2020/8/24 15:21
 * @UpdateDate: 2020/8/24 15:21
 */
@Service
@Transactional
public class RemittanceServiceImpl implements RemittanceService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RemittanceServiceImpl.class);
    public static final int SUCCESS = 1;
    private static final int TWO = 2;
    @Autowired
    private PurchasePayTrackService purchasePayTrackService;
    @Autowired
    private PurchaseOfflineMapper purchaseOfflineMapper;
    @Autowired
    private ProductPurchaseMapper productPurchaseMapper;
    @Autowired
    private PurchaseTrackMapper purchaseTrackMapper;
    @Autowired
    private OrderServiceUtils orderServiceUtils;
    @Autowired
    private ProductPayService productPayService;
    @Autowired
    private ProductDetailMapper productDetailMapper;
    @Autowired
    private ProductQueryMapper productQueryMapper;
    //日志记录
    @Override
    public RemittanceDto getRemittance(String purchaseNo) {
        PurchaseOffline purchaseOffline = purchaseOfflineMapper.selectByPurchaseNo(purchaseNo);
        if (null == purchaseOffline) {
            return null;
        }
        RemittanceDto remittanceDto = new RemittanceDto();
        remittanceDto.setId(purchaseOffline.getOfflineId());
        remittanceDto.setPurchaseNo(purchaseOffline.getPurchaseNo());
        remittanceDto.setRemitBankName(purchaseOffline.getRemitBankName());
        remittanceDto.setRemitTime(purchaseOffline.getRemitTime());
        remittanceDto.setRemitProof(purchaseOffline.getRemitProof());
        remittanceDto.setRemitBankNo(purchaseOffline.getRemitBankNo());
        remittanceDto.setRemitTradeNo(purchaseOffline.getRemitTradeNo());
        return remittanceDto;
    }

    /***
     * @Description: 银行流水提交
     * @Author: wangsong
     * @param :         productPurchaseId
     * @CreateDate: 2020/8/31 15:27
     * @UpdateUser: liang
     * @UpdateDate: 2020/8/31 15:27
     * @return :        boolean
     */
    @Override
    public ProductPurchase remittanceSubmit(RemittanceVo remittanceVo) throws BusinessException {
        ProductPurchase productPurchase = productPurchaseMapper.selectByPurchaseNo(remittanceVo.getPurchaseNo());
        //订单不存在,如果等于null 抛异常
        Errors.ORDER_NOEXIST.assertNotNull(productPurchase);
        //订单已经取消， 等于取消则抛异常
        Errors.ORDER_CANCEL.assertEquals(Constant.PURCHASE_STATUS_CANCEL == productPurchase.getPurchaseStatus(), false);

        //订单已经支付，抛异常
        Errors.ORDER_ISPAYED.assertEquals(TWO == productPurchase.getPurchaseStatus(), false);

        PurchaseOffline purchaseOffline = purchaseOfflineMapper.selectByPurchaseNo(remittanceVo.getPurchaseNo());
        //已经存在汇款单; 不为空抛异常
        Errors.REMITTANCE_EXIST.assertNull(purchaseOffline);

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //设置线下支付参数
        purchaseOffline = new PurchaseOffline();
        //获取操作ID
        String strId = UUID.randomUUID().toString();
        purchaseOffline.setOfflineId(strId);
        purchaseOffline.setRemitTime(LocalDate.parse(remittanceVo.getRemitTime(), df).atStartOfDay());
        purchaseOffline.setConfirmRemitTime(LocalDate.parse(remittanceVo.getRemitTime(), df).atStartOfDay());
        purchaseOffline.setPurchaseNo(remittanceVo.getPurchaseNo());
        purchaseOffline.setRemitBankName(remittanceVo.getRemitBankName());
        purchaseOffline.setRemitBankNo(remittanceVo.getRemitBankNo());
        purchaseOffline.setRemitTradeNo(remittanceVo.getRemitTradeNo());
        purchaseOffline.setRemitProof(remittanceVo.getRemitProof());
        try {
            if (SUCCESS == purchaseOfflineMapper.insert(purchaseOffline)) {
                PurchasePayTrack record = new PurchasePayTrack();
                record.setPurchaseNo(remittanceVo.getPurchaseNo());
                record.setPayStatus(Constant.PAY_TRACK_WAIT);
                List<PurchasePayTrack> trackList = purchasePayTrackService.selectPayTracks(record);
                //服务购买成功后更新redis
                if(!CollectionUtils.isEmpty(trackList)){
                    for(PurchasePayTrack ppt : trackList){
                        if(Constant.ALIPAY.equalsIgnoreCase(ppt.getPayType())){
                            productPayService.deleteLock(Constant.ALIPAY + "_" + ppt.getPayTrackNo());
                        }else if(Constant.WECHATPAY.equalsIgnoreCase(ppt.getPayType())){
                            productPayService.deleteLock(Constant.WECHATPAY + "_" + ppt.getPayTrackNo());
                        }else{
                            LOGGER.debug("no payType");
                        }
                    }
                }
                productPurchase.setPurchaseNo(remittanceVo.getPurchaseNo());
                productPurchase.setPurchaseStatus(Constant.PURCHASE_STATUS_WAITCONFIRM);
                productPurchase.setPayType(Constant.PAY_TYPE_OFFLINE);
                productPurchase.setBrankstartTime(LocalDateTime.now());
                productPurchaseMapper.updateByPurchaseNoSelective(productPurchase);
                PurchaseTrack purchaseTrack = orderServiceUtils.getPurchaseTrackEntity();
                purchaseTrack.setPurchaseStatus(Constant.PURCHASE_STATUS_WAITCONFIRM);
                purchaseTrack.setPurchaseNo(remittanceVo.getPurchaseNo());
                purchaseTrack.setPayType(Constant.PAY_TYPE_OFFLINE);
                purchaseTrack.setPayTime(LocalDateTime.now());
                purchaseTrackMapper.insertSelective(purchaseTrack);
                return productPurchase;
            }
            return null;
        } catch (Exception ex) {
            throw new BusinessException(Errors.REMITTANCE_EXCEPTION.getMessage(), Errors.REMITTANCE_EXCEPTION.getCode(), ex);
        }
    }

    /***
     * @Description: 服务购买成功后更新Redis服务状态
     * @Author: wangsong
     * @param :         productId
     * @param :         purchaseNo
     * @CreateDate: 2020/8/31 15:27
     * @UpdateUser: liang
     * @UpdateDate: 2020/8/31 15:27
     * @return :        boolean
     */
    @Override
    public void updateProductStatusOnRedis(String productId, String purchaseNo) {
        List<ProductDetail> productDetails = productDetailMapper.selectByPurchaseNo(purchaseNo);
        ProductSimpleDto productSimpleDto = productQueryMapper.getProductSimpleByProductId(productId);
        if (!CollectionUtils.isEmpty(productDetails)) {
            for (ProductDetail productDetail : productDetails) {
                String redisKey = Constant.PRODUCT_AVAILABLE_PREFIX +
                        "_" + productDetail.getTenantId();
                if(productDetail.getVemsId()!=null&&!"".equals(productDetail.getVemsId())){
                    redisKey+="_" + productDetail.getVemsId();
                }
                redisKey+="_" + productSimpleDto.getProductCode();
                LOGGER.info("Update the cache, cache key:{}",redisKey);
                productPayService.updateProductStatus(redisKey, Constant.PRODUCT_AVAILABLE_AVAILABLE);
            }
        }
    }
}
