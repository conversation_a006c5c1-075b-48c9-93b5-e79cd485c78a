/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.controller.rrq
 * @ClassName: OrderController
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 对订单服务进行操作的入口
 * @Author: ouyang，liangjunbin
 * @CreateDate: 2020/8/24 14:55
 * @UpdateUser: ouyang
 * @UpdateDate: 2020/10/10 14:55
 */
package com.snbc.bbpf.bus.product.manager.controller.tenant;

import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.resp.product.ProductCommonResp;
import com.snbc.bbpf.bus.product.manager.resp.product.RemittanceResp;
import com.snbc.bbpf.bus.product.manager.service.OrderService;
import com.snbc.bbpf.bus.product.manager.service.RemittanceService;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductStatusChangeDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.RemittanceDto;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.vo.OrderVo;
import com.snbc.bbpf.bus.product.market.common.vo.RemittanceVo;
import com.snbc.bbpf.bus.product.market.common.vo.RenewOrderVo;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.io.IOException;

/**
 * @author: LiangJB，oywp
 * 功能描述: <br>
 * 对订单服务进行操作的入口
 * @date: 2020/8/12 13:21
*/
@RestController
@RequestMapping("/console/v1/tenant/productservice")
public class OrderController {
    @Autowired
    private OrderService orderService;
    @Autowired
    private RemittanceService remittanceService;

    /**
     * @author: OY
     * 功能描述: <br>
     * 获取汇款详情服务的接口
     * @date: 2020/8/12 13:21
     * @param: productPurchaseId
     * @return: RemittanceDto
     */
    
    @GetMapping(value = "/getremittance")
    //@Buslog(opration = "获取汇款详情服务", target = "服务市场", zh = "订单编号:${purchaseNo}")
    public RemittanceResp getRemittance( String purchaseNo) {
        RemittanceResp response=new RemittanceResp();
        CallResponse header = getPurchaseNo(purchaseNo);
        response.setHead(header);
        if(header.getCode()== Errors.FAILED.getCode()){
            return response;
        }
        RemittanceDto remittanceDto =remittanceService.getRemittance(purchaseNo);
        header.setCode(Errors.SUCCESS.getCode());
        header.setMessage(Errors.SUCCESS.getMessage());
        response.setBody(remittanceDto);
        return response;
    }
    /**
     * @author: OY
     * 功能描述: <br>
     * 订单取消服务的接口
     * @date: 2020/8/12 13:21
     * @param: productPurchaseId
     * @return: CallResponse
     */

    @PostMapping(value = "/ordercancel/{purchaseNo}")
    @Buslog(opration = "canceOrder", target = "orderService", zh = "取消服务订单，订单号:[${purchaseNo}]"
    ,en = "Cancel service order, Order number :[${purchaseNo}]")
    public ProductCommonResp orderCancel(@PathVariable("purchaseNo")String purchaseNo){
        ProductCommonResp response=new ProductCommonResp();
        CallResponse header = getPurchaseNo(purchaseNo);
        response.setHead(header);
        if(Errors.FAILED.getCode().equals(header.getCode())){
            return response;
        }
        if(orderService.orderCancel(purchaseNo,Constant.PURCHASE_STATUS_CANCEL)) {
            header.setCode(Errors.SUCCESS.getCode());
            header.setMessage(Errors.SUCCESS.getMessage());
        }else{
            header.setCode(Errors.FAILED.getCode());
            header.setMessage(Errors.FAILED.getMessage());
        }
        return response;
    }
    /**
     * @author: OY
     * 功能描述: <br>
     * 产品服务下单的接口
     * @date: 2020/8/12 13:21
     * @param: OrderVo
     * @return: CallResponse
     */
    
    @PostMapping(value = "/order")
    @Buslog(opration = "purchaseProduct", target = "orderService", zh = "购买服务[${productName}],价格:[${purchaseAmount}]"
            ,en="Purchase service [${productName}],price:￥[${purchaseAmount}]")
    public CommonResp<ProductPurchase> order(@Valid @RequestBody OrderVo orderVo) throws BusinessException, IOException {
        CommonResp<ProductPurchase> response=new CommonResp<>();
        CallResponse result=new CallResponse();
        response.setHead(result);
        ProductPurchase productPurchase=orderService.order(orderVo);
        if(null != productPurchase) {
            result.setCode(Errors.SUCCESS.getCode());
            response.setBody(productPurchase);
        }else{
            result.setCode(Errors.FAILED.getCode());
        }
        return response;
    }
    /**
     * @author: OY
     * 功能描述: <br>
     * 产品服务续费的接口
     * @date: 2020/8/12 13:21
     * @param: OrderVo
     * @return: CallResponse
     */
    
    @PostMapping(value = "/renew")
    @Buslog(opration = "productRenewal", target = "orderService", zh = "服务续费【${productName}】,价格:[${purchaseAmount}]"
            ,en="Service renew [${productName}],price:￥[${purchaseAmount}]]")
    public CommonResp renew(@Valid @RequestBody RenewOrderVo orderVo) {
        CommonResp<ProductPurchase> response=new CommonResp();
        CallResponse result=new CallResponse();
        response.setHead(result);
        ProductPurchase productPurchase=orderService.renew(orderVo);
        if(null!= productPurchase) {
            result.setCode(Errors.SUCCESS.getCode());
            response.setBody(productPurchase);
        }else{
            result.setCode(Errors.FAILED.getCode());
        }
        return response;
    }
    /**
     * @author: OY
     * 功能描述: <br>
     * 确认商户已付款的接口
     * @date: 2020/8/12 13:21
     * @param: productPurchaseId
     * @return: CallResponse
     */
    
    @PostMapping(value = "/paycomfirm")
    public ProductCommonResp payComfirm(String purchaseNo) throws BusinessException {
        ProductCommonResp response=new ProductCommonResp();
        CallResponse header= getPurchaseNo(purchaseNo);
        response.setHead(header);
        if(Errors.FAILED.getCode().equals(header.getCode())){
            return response;
        }
        if(orderService.payConfirm(purchaseNo, Constant.PAY_TYPE_OFFLINE)) {
            orderService.sendMsg(purchaseNo,BusTemplateCodeEnum.MSG_TYPE_PAY_CONFIRM.getCode());
            //开通成功后调发消息
            header.setCode(Errors.SUCCESS.getCode());
            header.setMessage(Errors.SUCCESS.getMessage());
        }else{
            header.setCode(Errors.FAILED.getCode());
            header.setMessage(Errors.FAILED.getMessage());
        }
        return  response;
    }
    /**
     * @author: OY
     * 功能描述: <br>
     * 确认商户已付款的接口
     * @date: 2020/8/12 13:21
     * @param: productPurchaseId
     * @return: CallResponse
     */
    
    @PostMapping(value = "/updatestatus")
    //@Buslog(opration = "更新订单状态接口", target = "服务市场", zh = "订单编号：${purchaseNo}," +
    //        "服务购买订单状态：${status}")
    public ProductCommonResp updateStatus(@Valid @RequestBody ProductStatusChangeDto productStatusDto) throws IOException, BusinessException {
        ProductCommonResp response=new ProductCommonResp();
        CallResponse header= getPurchaseNo(productStatusDto.getPurchaseNo());
        response.setHead(header);
        if(Errors.FAILED.getCode().equals(header.getCode())){
            return response;
        }
        if(orderService.updateStatus(productStatusDto.getPurchaseNo(),productStatusDto.getStatus())) {
            header.setCode(Errors.SUCCESS.getCode());
            header.setMessage(Errors.SUCCESS.getMessage());
        }else{
            header.setCode(Errors.FAILED.getCode());
            header.setMessage(Errors.FAILED.getMessage());
        }
        return  response;
    }
    /**
     * @author: OY
     * 功能描述: <br>
     * 填写汇款单信息的接口
     * @date: 2020/8/12 13:21
     * @param: RemittanceVo
     * @return: CallResponse
     */
    
    @PostMapping(value = "/remittancesubmit")
    @Buslog(opration = "fillRremittance", target = "orderService", zh = "汇款信息，" +
            "订单编号：[${purchaseNo}],汇款银行名称：[${remitBankName}],汇款账号：[${remitBankNo}],汇款时间：[${remitTime}]"
            ,en = "Remittance Information, Order Number: [${purchaseNo}], remitBankName: " +
            "[${PurchasENO}], RemitBank Account: [${PurchasENO}], remitTime: [${PurchasENO}]")
    public CommonResp remittanceSubmit(@Valid @RequestBody RemittanceVo remittanceVo) throws BusinessException {
        CommonResp<ProductPurchase> response=new CommonResp();
        CallResponse result=new CallResponse();
        response.setHead(result);
        ProductPurchase productPurchase=remittanceService.remittanceSubmit(remittanceVo);
        if(null==productPurchase) {
            result.setCode(Errors.FAILED.getCode());
        }else{
            response.setBody(productPurchase);
            result.setCode(Errors.SUCCESS.getCode());
        }
        return response;
    }
    /**
     * @author: OY
     * 功能描述: <br>
     * 判断订单ID是否存在
     * @date: 2020/8/12 13:21
     * @param: productPurchaseNo
     * @return: CallResponse
     */
    private CallResponse getPurchaseNo(String productPurchaseNo) {
        CallResponse header =new CallResponse();
        if(null== productPurchaseNo ||"".equals(productPurchaseNo)){
            header.setCode(Errors.FAILED.getCode());
            header.setMessage("订单号不能为空");
        }
        return header;
    }
}

