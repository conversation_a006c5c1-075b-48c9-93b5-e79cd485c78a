package com.snbc.bbpf.bus.product.market.common.vo.product;
/*
服务统计月报查询入参"
* */

import lombok.Data;
import org.springframework.validation.annotation.Validated;


@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T06:49:02.983Z")
@Data
public class ProductMRPCount {
    //商户名称
    
    private String shopName;
    //统计开始时间
    
    private String startTime;
    //统计结束时间
    
    private String endTime;
}
