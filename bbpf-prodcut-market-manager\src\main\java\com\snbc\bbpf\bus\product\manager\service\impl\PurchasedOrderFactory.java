package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.config.Container;
import com.snbc.bbpf.bus.product.manager.service.PurchasedProductOrderService;
import com.snbc.bbpf.bus.product.market.common.entity.ProductServices;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductServicesMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ProjectName: product-manager
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: PurchasedOrderFactory
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2021/3/17 13:08
 * @UpdateUser: wangsong
 * @UpdateDate: 2021/3/17 13:08
 */
@Component
public class PurchasedOrderFactory {

    @Autowired
    private ProductServicesMapper productServicesMapper;
    private static final String PURCHASEDPRODUCTORDERBYDAY = "purchasedProductOrderByDay";
    private static final String PURCHASEDPRODUCTORDERBYCOUNT = "purchasedProductOrderByCount";
    private static final int THREE = 3;
    private static final int TWO = 2;

    /***
     * @Description:    查询已购买服务（按天/按次数）的订单列表
     * @Author:         wangsong
     * @param :         tenantId
     * @param :         productId
     * @param :         pageNum
     * @param :         pageSize
     * @param :         productType
     * @CreateDate:     2021/2/25 19:01
     * @UpdateUser:     wangsong
     * @UpdateDate:     2021/2/25 19:01
     * @return :        com.github.pagehelper.PageInfo<java.lang.Object>
     */
    public PageInfo<Object> purchasedOrderWithPage(String tenantId, String productId, Integer pageNum, Integer pageSize) {
        ProductServices productServices = productServicesMapper.selectByPrimaryKey(productId);
        PurchasedProductOrderService purchasedProductOrder;
        //查询按天类型服务订单列表
        if(productServices.getChargeType() == TWO){
            purchasedProductOrder = Container.getBean(PURCHASEDPRODUCTORDERBYDAY, PurchasedProductOrderService.class);
            return purchasedProductOrder.purchasedOrderList(tenantId,productId,pageNum,pageSize);
        }
        //查询按次数类型服务订单列表
        if(productServices.getChargeType() == THREE){
            purchasedProductOrder = Container.getBean(PURCHASEDPRODUCTORDERBYCOUNT, PurchasedProductOrderService.class);
            return purchasedProductOrder.purchasedOrderList(tenantId,productId,pageNum,pageSize);
        }
        return null;
    }

    public Object productRemainingUse(String tenantId, String productId) {
        ProductServices productServices = productServicesMapper.selectByPrimaryKey(productId);
        PurchasedProductOrderService purchasedProductOrder;
        //查询按天类型服务订单列表
        if(productServices.getChargeType() == TWO){
            purchasedProductOrder = Container.getBean(PURCHASEDPRODUCTORDERBYDAY, PurchasedProductOrderService.class);
            return purchasedProductOrder.productRemainingStatis(tenantId,productId);
        }
        //查询按次数类型服务订单列表
        if(productServices.getChargeType() == THREE){
            purchasedProductOrder = Container.getBean(PURCHASEDPRODUCTORDERBYCOUNT, PurchasedProductOrderService.class);
            return purchasedProductOrder.productRemainingStatis(tenantId,productId);
        }
        return null;
    }

}
