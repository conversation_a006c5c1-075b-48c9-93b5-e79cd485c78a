package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductAvailablePageDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductLOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderListDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductShopOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceBuyPDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceShopOrderDto;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductAvailableMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductOrderMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseOfflineMapper;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductAvailablePageQueryBase;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderVemsPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductShopVemsPageQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ProductOrderServiceImpl 单元测试
 */
class ProductOrderServiceImplTest {

    @Mock
    private ProductOrderMapper productOrderMapper;
    
    @Mock
    private ProductAvailableMapper productAvailableMapper;
    
    @Mock
    private ProductOrderServiceExtImpl productOrderServiceExtImpl;
    
    @Mock
    private PurchaseOfflineMapper purchaseOfflineMapper;

    @InjectMocks
    private ProductOrderServiceImpl productOrderService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("统计服务订单记录 - 成功")
    void testProductOrderListWithPage_Success() {
        // Given
        ProductOrderPageQuery query = new ProductOrderPageQuery();
        query.setPageNum(1);
        query.setPageSize(10);
        
        ProductOrderListDto dto = new ProductOrderListDto();
        dto.setPurchaseNo("PO123456");
        dto.setChargeTypeName("按月计费");
        dto.setProductName("测试产品");
        
        List<ProductOrderListDto> mockList = Arrays.asList(dto);
        when(productOrderMapper.selectProductOrderList(any(ProductOrderPageQuery.class))).thenReturn(mockList);
        
        // When
        try (MockedStatic<PageMethod> pageMethodMock = mockStatic(PageMethod.class)) {
            PageInfo<ProductShopOrderDto> result = productOrderService.productOrderListWithPage(query);
            
            // Then
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            assertEquals("PO123456", result.getList().get(0).getPurchaseNo());
            pageMethodMock.verify(() -> PageMethod.startPage(1, 10), times(1));
            verify(productOrderMapper, times(1)).selectProductOrderList(query);
        }
    }

    @Test
    @DisplayName("服务订单记录 - 成功")
    void testServiceOrderListWithPage_Success() {
        // Given
        ProductOrderPageQuery query = new ProductOrderPageQuery();
        query.setPageNum(1);
        query.setPageSize(10);
        
        ProductOrderListDto dto = new ProductOrderListDto();
        dto.setPurchaseNo("PO123456");
        dto.setProductName("测试产品");
        
        List<ProductOrderListDto> mockList = Arrays.asList(dto);
        when(productOrderMapper.selectProductOrderList(any(ProductOrderPageQuery.class))).thenReturn(mockList);
        
        // When
        try (MockedStatic<PageMethod> pageMethodMock = mockStatic(PageMethod.class)) {
            PageInfo<ProductOrderDto> result = productOrderService.serviceOrderListWithPage(query);
            
            // Then
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            assertEquals("PO123456", result.getList().get(0).getPurchaseNo());
            pageMethodMock.verify(() -> PageMethod.startPage(1, 10), times(1));
            verify(productOrderMapper, times(1)).selectProductOrderList(query);
        }
    }

    @Test
    @DisplayName("服务订单列表 - 成功")
    void testRrqserviceOrderListWithPage_Success() {
        // Given
        ProductOrderPageQuery query = new ProductOrderPageQuery();
        query.setPageNum(1);
        query.setPageSize(10);
        
        ProductOrderListDto dto = new ProductOrderListDto();
        dto.setPurchaseNo("PO123456");
        
        List<ProductOrderListDto> mockList = Arrays.asList(dto);
        List<ServiceShopOrderDto> convertedList = Arrays.asList(new ServiceShopOrderDto());
        
        when(productOrderMapper.selectProductOrderList(any(ProductOrderPageQuery.class))).thenReturn(mockList);
        when(productOrderServiceExtImpl.convertRrqServiceOrderListResult(any(List.class))).thenReturn(convertedList);
        
        // When
        try (MockedStatic<PageMethod> pageMethodMock = mockStatic(PageMethod.class)) {
            PageInfo<ServiceShopOrderDto> result = productOrderService.rrqserviceOrderListWithPage(query);
            
            // Then
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            pageMethodMock.verify(() -> PageMethod.startPage(1, 10), times(1));
            verify(productOrderMapper, times(1)).selectProductOrderList(query);
            verify(productOrderServiceExtImpl, times(1)).convertRrqServiceOrderListResult(mockList);
        }
    }

    @Test
    @DisplayName("服务可用列表 - 成功")
    void testSeviceAvailableListWithPage_Success() {
        // Given
        ProductAvailablePageQueryBase query = new ProductAvailablePageQueryBase();
        query.setPageNum(1);
        query.setPageSize(10);
        
        ProductAvailablePageDto dto = new ProductAvailablePageDto();
        dto.setAvailableId("AV123456");
        
        List<ProductAvailablePageDto> mockList = Arrays.asList(dto);
        when(productAvailableMapper.selectseviceAvailableList(any(ProductAvailablePageQueryBase.class))).thenReturn(mockList);
        
        // When
        try (MockedStatic<PageMethod> pageMethodMock = mockStatic(PageMethod.class)) {
            PageInfo<ProductAvailablePageDto> result = productOrderService.seviceAvailableListWithPage(query);
            
            // Then
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            assertEquals("AV123456", result.getList().get(0).getAvailableId());
            pageMethodMock.verify(() -> PageMethod.startPage(1, 10), times(1));
            verify(productAvailableMapper, times(1)).selectseviceAvailableList(query);
        }
    }

    @Test
    @DisplayName("可用详情 - 服务已开通")
    void testAvailableDetail_ServiceActive() {
        // Given
        String availableId = "AV123456";
        String tenantId = "tenant123";
        
        ProductAvailablePageDto dto = new ProductAvailablePageDto();
        dto.setAvailableId(availableId);
        dto.setDueTime(LocalDateTime.now().plusDays(30)); // 30天后到期
        
        when(productAvailableMapper.selectseviceAvailable(anyString(), anyString())).thenReturn(dto);
        
        // When
        ProductAvailablePageDto result = productOrderService.availableDetail(availableId, tenantId);
        
        // Then
        assertNotNull(result);
        assertEquals("已开通", result.getServiceStatus());
        assertTrue(result.getResidueDay().contains("天"));
        verify(productAvailableMapper, times(1)).selectseviceAvailable(availableId, tenantId);
    }

    @Test
    @DisplayName("可用详情 - 服务已到期")
    void testAvailableDetail_ServiceExpired() {
        // Given
        String availableId = "AV123456";
        String tenantId = "tenant123";
        
        ProductAvailablePageDto dto = new ProductAvailablePageDto();
        dto.setAvailableId(availableId);
        dto.setDueTime(LocalDateTime.now().minusDays(1)); // 1天前已到期
        
        when(productAvailableMapper.selectseviceAvailable(anyString(), anyString())).thenReturn(dto);
        
        // When
        ProductAvailablePageDto result = productOrderService.availableDetail(availableId, tenantId);
        
        // Then
        assertNotNull(result);
        assertEquals("已到期", result.getServiceStatus());
        assertEquals("0天", result.getResidueDay());
        verify(productAvailableMapper, times(1)).selectseviceAvailable(availableId, tenantId);
    }

    @Test
    @DisplayName("可用详情 - 数据不存在")
    void testAvailableDetail_NotFound() {
        // Given
        String availableId = "AV123456";
        String tenantId = "tenant123";
        
        when(productAvailableMapper.selectseviceAvailable(anyString(), anyString())).thenReturn(null);
        
        // When
        ProductAvailablePageDto result = productOrderService.availableDetail(availableId, tenantId);
        
        // Then
        assertNull(result);
        verify(productAvailableMapper, times(1)).selectseviceAvailable(availableId, tenantId);
    }

    @Test
    @DisplayName("订单详情 - 在线支付")
    void testOrderDetail_OnlinePayment() {
        // Given
        String purchaseNo = "PO123456";
        
        ProductOrderDetailDto dto = new ProductOrderDetailDto();
        dto.setPurchaseNo(purchaseNo);
        dto.setPayType("1"); // 在线支付
        
        when(productOrderMapper.getProductOrderDetailByPurchaseNo(anyString())).thenReturn(dto);
        
        // When
        ProductOrderDetailDto result = productOrderService.orderDetail(purchaseNo);
        
        // Then
        assertNotNull(result);
        assertEquals(purchaseNo, result.getPurchaseNo());
        verify(productOrderMapper, times(1)).getProductOrderDetailByPurchaseNo(purchaseNo);
        verify(purchaseOfflineMapper, never()).selectByPurchaseNo(anyString());
    }

    @Test
    @DisplayName("订单详情 - 线下支付")
    void testOrderDetail_OfflinePayment() {
        // Given
        String purchaseNo = "PO123456";
        
        ProductOrderDetailDto dto = new ProductOrderDetailDto();
        dto.setPurchaseNo(purchaseNo);
        dto.setPayType(String.valueOf(Constant.PAY_TYPE_OFFLINE)); // 线下支付
        
        PurchaseOffline purchaseOffline = new PurchaseOffline();
        purchaseOffline.setRemitTradeNo("TRADE123456");
        
        when(productOrderMapper.getProductOrderDetailByPurchaseNo(anyString())).thenReturn(dto);
        when(purchaseOfflineMapper.selectByPurchaseNo(anyString())).thenReturn(purchaseOffline);
        
        // When
        ProductOrderDetailDto result = productOrderService.orderDetail(purchaseNo);
        
        // Then
        assertNotNull(result);
        assertEquals(purchaseNo, result.getPurchaseNo());
        assertEquals("TRADE123456", result.getPurchaseChannelNo());
        verify(productOrderMapper, times(1)).getProductOrderDetailByPurchaseNo(purchaseNo);
        verify(purchaseOfflineMapper, times(1)).selectByPurchaseNo(purchaseNo);
    }

    @Test
    @DisplayName("服务订单设备详细记录 - 成功")
    void testSeviceVemsOrderListWithPage_Success() {
        // Given
        ProductOrderVemsPageQuery query = new ProductOrderVemsPageQuery();
        query.setPageNum(1);
        query.setPageSize(10);
        query.setPurchaseNo("PO123456");
        
        ProductLOrderDto dto = new ProductLOrderDto();
        // ProductLOrderDto does not have purchaseNo field, removed setPurchaseNo call
        
        List<ProductLOrderDto> mockList = Arrays.asList(dto);
        when(productOrderMapper.getProductRecordLstByPurchaseNo(anyString())).thenReturn(mockList);
        
        // When
        try (MockedStatic<PageMethod> pageMethodMock = mockStatic(PageMethod.class)) {
            PageInfo<ProductLOrderDto> result = productOrderService.seviceVemsOrderListWithPage(query);
            
            // Then
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            // ProductLOrderDto does not have purchaseNo field, removed assertion
            pageMethodMock.verify(() -> PageMethod.startPage(1, 10), times(1));
            verify(productOrderMapper, times(1)).getProductRecordLstByPurchaseNo("PO123456");
        }
    }

    @Test
    @DisplayName("设备标准列表 - 默认查询")
    void testSevicesShopListWithPage_DefaultQuery() {
        // Given
        ProductShopVemsPageQuery query = new ProductShopVemsPageQuery();
        query.setPageNum(1);
        query.setPageSize(10);
        query.setDateType(1); // 默认查询
        
        ServiceBuyPDto dto = new ServiceBuyPDto();
        dto.setVemsId("VEMS123");
        
        List<ServiceBuyPDto> mockList = Arrays.asList(dto);
        when(productOrderMapper.getServiceBuyLstByTenantId(any(ProductShopVemsPageQuery.class))).thenReturn(mockList);
        
        // When
        try (MockedStatic<PageMethod> pageMethodMock = mockStatic(PageMethod.class)) {
            PageInfo<ServiceBuyPDto> result = productOrderService.sevicesShopListWithPage(query);
            
            // Then
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            assertEquals("VEMS123", result.getList().get(0).getVemsId());
            pageMethodMock.verify(() -> PageMethod.startPage(1, 10), times(1));
            verify(productOrderMapper, times(1)).getServiceBuyLstByTenantId(query);
            verify(productOrderServiceExtImpl, never()).convertTime(any(), any(), any());
            verify(productOrderServiceExtImpl, times(1)).convertServiceBuyPDto(any(), any());
        }
    }

    @Test
    @DisplayName("设备标准列表 - 非默认查询")
    void testSevicesShopListWithPage_NonDefaultQuery() {
        // Given
        ProductShopVemsPageQuery query = new ProductShopVemsPageQuery();
        query.setPageNum(1);
        query.setPageSize(10);
        query.setDateType(2); // 非默认查询
        
        ServiceBuyPDto dto = new ServiceBuyPDto();
        dto.setVemsId("VEMS123");
        
        List<ServiceBuyPDto> mockList = Arrays.asList(dto);
        when(productOrderMapper.getServiceBuyLstByTenantId(any(ProductShopVemsPageQuery.class))).thenReturn(mockList);
        
        // When
        try (MockedStatic<PageMethod> pageMethodMock = mockStatic(PageMethod.class)) {
            PageInfo<ServiceBuyPDto> result = productOrderService.sevicesShopListWithPage(query);
            
            // Then
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            pageMethodMock.verify(() -> PageMethod.startPage(1, 10), times(1));
            verify(productOrderMapper, times(1)).getServiceBuyLstByTenantId(query);
            verify(productOrderServiceExtImpl, times(1)).convertTime(any(), any(), any());
            verify(productOrderServiceExtImpl, times(1)).convertServiceBuyPDto(any(), any());
        }
    }

    @Test
    @DisplayName("设备标准列表 - 空结果")
    void testSevicesShopListWithPage_EmptyResult() {
        // Given
        ProductShopVemsPageQuery query = new ProductShopVemsPageQuery();
        query.setPageNum(1);
        query.setPageSize(10);
        query.setDateType(1);
        
        when(productOrderMapper.getServiceBuyLstByTenantId(any(ProductShopVemsPageQuery.class))).thenReturn(new ArrayList<>());
        
        // When
        try (MockedStatic<PageMethod> pageMethodMock = mockStatic(PageMethod.class)) {
            PageInfo<ServiceBuyPDto> result = productOrderService.sevicesShopListWithPage(query);
            
            // Then
            assertNotNull(result);
            assertEquals(0, result.getList().size());
            pageMethodMock.verify(() -> PageMethod.startPage(1, 10), times(1));
            verify(productOrderMapper, times(1)).getServiceBuyLstByTenantId(query);
            verify(productOrderServiceExtImpl, never()).convertTime(any(), any(), any());
            verify(productOrderServiceExtImpl, never()).convertServiceBuyPDto(any(), any());
        }
    }
}