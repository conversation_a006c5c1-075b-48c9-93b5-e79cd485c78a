package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.market.common.dto.order.AuthorityCallBackDto;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.vo.OrderVo;
import com.snbc.bbpf.bus.product.market.common.vo.RenewOrderVo;

/**
 * 服务购买订单服务接口
 */
public interface    OrderService {


    /**
     * 取消订单
     * @param productPurchaseId
     * @return
     */
    boolean orderCancel(String productPurchaseId,int purchaseStatus);

    /**
     * 确认付款
     * @param purchaseNo 订单号
     * @param payType 支付类型
     * @return
     */
    boolean payConfirm(String purchaseNo, Integer payType) throws BusinessException;

    /**
     * 更新订单状态
     * @param productPurchaseId 订单号
     * @param status 订单状态
     * @return
     */
    boolean updateStatus(String productPurchaseId,Integer status) throws BusinessException;

    /**
     *产品服务购买下单
     * @param orderVo
     * @return
     */
    ProductPurchase order(OrderVo orderVo) throws BusinessException;
    void authorityCallBack(AuthorityCallBackDto authorityCallBackDto );
    /**
     *产品服务续费下单
     * @param orderVo
     * @return
     */
    ProductPurchase renew(RenewOrderVo orderVo);

    /**
     * 发消息
     * @param purchaseNo
     */
    void sendMsg(String purchaseNo,String msgType);
}

