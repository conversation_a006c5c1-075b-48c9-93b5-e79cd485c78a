/**   
* 版权所有 2019山东新北洋信息技术股份有限公司
* 保留所有权利。
*/
package com.snbc.bbpf.bus.product.manager.config.payconfig.request;

import com.snbc.pay.entity.GoodsDetail;
import lombok.Data;

import java.util.List;

/**
* @ClassName: TradeRefundRequest 
* @Description: 退款请求 
* <AUTHOR> newbeiyang.com
* @date 2019年10月11日 下午1:21:03 
* @version V1.0
 */
@Data
public class RefundRequest extends BaseRefundRequest {
	/**
	 * 京东聚合-分账参数
	 */
	
	private String tenantId;
	/**
	 * 京东聚合-分账参数
	 */
	
	private String refundEmail;
	
	/**
	 * 京东聚合-分账参数
	 */
	
	private String billSplitList;
	
	/**
	 * 光大银行-特殊参数
	 */
	
	private String groupNo;
	
	/**终端号**/
	
	private String tid;
	
	/**app_auth_token服务商用该字段*/
	
	private String appAuthToken;
	
	/**银联独有参数-原交易商户发送交易时间yyyyMMddHHmmss*/
	
	private String origTxnTime;
	
	/**银联 :订单发送时间，格式为YYYYMMDDhhmmss，必须取当前时间，否则会报txnTime无效 */
	
	private String txnTime;
	
	/**商户订单号*/
	
	private String outTradeNo;

	/**需要退款的金额*/
	
	private String refundAmount;
	/**交易号*/
	
	private String tradeNo;
	/**多次退款时使用*/
	
	private String outRequestNo;
	/**订单总金额，单位为元*/
	
	private String totalAmount;		
	
	/**
	 *  退款包含的商品列表信息，Json格式。
		其它说明详见：“商品明细说明”
	 */
	
	
	private List<GoodsDetail> goodsDetail;
	/**
	 * 商户的操作员编号
	 */
	
	private String operatorId;
	/**以下4个属性，在console-api中 退款时用到**/
	/**
	 * 商户的操作员登录密码， wjc1 add ，qq钱包 退款专用
	 */
	
	private String operationKey;
	
	
	private String cstNo;
	
	// 交易类型
	
	private String tradeType; 
	
	// 订单类型
	
	private String channelType; 
	

	
}
