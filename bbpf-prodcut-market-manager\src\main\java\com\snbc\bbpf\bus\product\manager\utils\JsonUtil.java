package com.snbc.bbpf.bus.product.manager.utils;

import org.codehaus.jackson.map.ObjectMapper;

import java.io.IOException;

/**
 * <AUTHOR>
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.utils
 * @ClassName: JsonUtil
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: json工具类
 * @CreateDate: 2020/8/24 15:21
 * @UpdateDate: 2020/8/24 15:21
 */
public final class JsonUtil {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将对象转成json字符串
     * @param entity
     * @return
     * @throws IOException
     */
    public static String toJsonString(Object entity) throws IOException {
        return objectMapper.writeValueAsString(entity);
    }
}
