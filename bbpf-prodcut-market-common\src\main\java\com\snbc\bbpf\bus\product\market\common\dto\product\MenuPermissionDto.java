/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: RoleMenuPermission
 * @Description: 角色详情菜单权限
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/21
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MenuPermissionDto {
    /**
     * 权限id
     */
    private String permissionId;
    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限叶子结点
     */
    private List<NodePermissionDto> nodes;
}
