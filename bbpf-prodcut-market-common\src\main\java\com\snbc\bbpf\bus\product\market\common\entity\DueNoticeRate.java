package com.snbc.bbpf.bus.product.market.common.entity;
/**
 * 到期通知频率类型实体类
 */
public class DueNoticeRate {
    /**
     * 主键ID
     */
    private String noticeRateId;
    /**
     * 服务编号
     */
    private String productId;
    /**
     * 通知频率
     */
    private Integer noticeRate;
    /**
     * 获取字典类型编码
     */
    public String getNoticeRateId() {
        return noticeRateId;
    }
    /**
     * 设置noticeRateId
     * @param noticeRateId
     */
    public void setNoticeRateId(String noticeRateId) {
        this.noticeRateId = noticeRateId == null ? null : noticeRateId.trim();
    }
    /**
     * 获取productId
     */
    public String getProductId() {
        return productId;
    }
    /**
     * 设置productId
     * @param productId
     */
    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }
    /**
     * 获取noticeRate
     */
    public Integer getNoticeRate() {
        return noticeRate;
    }
    /**
     * 设置noticeRate
     * @param noticeRate
     */
    public void setNoticeRate(Integer noticeRate) {
        this.noticeRate = noticeRate;
    }
}
