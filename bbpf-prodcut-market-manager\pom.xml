<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 父项目信息 -->
    <parent>
        <groupId>com.snbc.bbpf</groupId>
        <artifactId>bbpf-product-market</artifactId>
        <version>3.0.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!-- 当前模块的基本信息 -->
    <groupId>com.snbc.bbpf</groupId>
    <artifactId>bbpf-bus-product-market-manager</artifactId>
    <version>3.0.1</version>
    <name>bbpf-bus-product-market-manager</name>
    <description>bbpf-bus-product-market-manager</description>

    <!-- 项目属性定义 -->
    <properties>
        <java.version>21</java.version>
    </properties>

    <!-- 项目依赖管理 -->
    <dependencies>
        <!-- 内部公共依赖 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-product-market-common</artifactId>
            <version>3.0.1</version>
        </dependency>

        <!-- JetBrains Annotations -->
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>26.0.2</version>
            <scope>provided</scope>
        </dependency>

        <!-- Prometheus 监控依赖 -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <!-- Spring Boot Actuator 依赖，排除默认的日志依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 灰度发布组件，排除不必要的依赖 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-gray-spring-boot-starter</artifactId>
            <version>3.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-cloud-gateway-server</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- MySQL 数据库驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
            <scope>runtime</scope>
        </dependency>

        <!-- 标准依赖，排除默认的日志依赖 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-spring-boot-starter</artifactId>
            <version>3.0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Feign 客户端依赖 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- 日志组件 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-buslog-spring-boot-starter</artifactId>
            <version>3.0.1</version>
        </dependency>

        <!-- 消息组件，使用最新版本 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-message-spring-boot-starter</artifactId>
			<version>2.0.13</version>
        </dependency>

        <!-- 对象存储组件 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-oss-spring-boot-starter</artifactId>
            <version>3.0.1</version>
        </dependency>

        <!-- Apollo 配置中心客户端 -->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>

        <!-- Eureka 客户端 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>

        <!-- Servlet API，仅在编译时提供 -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Spring Boot AMQP 支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
        </dependency>

        <!-- 分页插件 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>

        <!-- Javassist - 升级到支持Java 21的版本 -->
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.30.0-GA</version>
        </dependency>

        <!-- 常用集合工具类 -->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>

        <!-- EasyExcel 表格处理工具 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <!-- 数据库加密组件 -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>

        <!-- 拼音转换工具 -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
        </dependency>

        <!-- Jackson 注解支持 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <!-- Spring AOP 支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- MapStruct 对象映射工具 -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <!-- bbpf-commons 公共组件 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-commons</artifactId>
            <version>1.6.0</version>
        </dependency>

        <!-- JWT 工具类 -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
        </dependency>

        <!-- IO 工具类 -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <!-- 支付组件，排除冲突的依赖 -->
        <dependency>
            <groupId>com.snbc</groupId>
            <artifactId>snbcPay</artifactId>
            <version>1.3.29-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-httpclient</groupId>
                    <artifactId>commons-httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dom4j</artifactId>
                    <groupId>dom4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- HTTP 客户端 -->
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
        </dependency>

        <!-- 支付宝 SDK -->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dom4j</artifactId>
                    <groupId>dom4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- QRCode 生成和解析 -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>

        <!-- 以下是单元测试相关的依赖，范围为 test，表示仅在测试时使用 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.opentest4j</groupId>
            <artifactId>opentest4j</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <classifier>runtime</classifier>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Jackson 旧版支持 -->
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
        </dependency>

        <!-- SkyWalking 日志监控 -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-log4j-2.x</artifactId>
        </dependency>
    </dependencies>

    <!-- 构建配置 -->
    <build>
        <finalName>bbpf-bus-product-market</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <!-- Spring Boot 的 Maven 插件，用于将应用程序打包为可执行的 JAR 文件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <finalName>${project.build.finalName}</finalName>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Maven 的 Surefire 插件，用于运行单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- 系统属性变量，用于存储 Jacoco 代理文件的位置 -->
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>${project.build.directory}/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                    <!-- JVM 参数配置，解决 Java 模块系统访问限制问题 -->
                    <argLine>--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.util.concurrent=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED --add-opens java.desktop/java.awt.font=ALL-UNNAMED -Djdk.net.URLClassPath.disableClassPathURLCheck=true</argLine>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>${junit-jupiter.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <!-- Jacoco 插件，用于代码覆盖率分析 - 暂时禁用以解决插桩问题 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>pre-unit-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <append>false</append>
                            <destFile>${basedir}/target/jacoco.exec</destFile>
                            <propertyName>surefireArgLine</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <dataFile>${basedir}/target/jacoco.exec</dataFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Maven 编译器插件，用于编译 Java 代码 -->
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <!-- 代码的编码方式，使用 UTF-8 -->
                    <encoding>UTF-8</encoding>
                    <!-- 编译器参数，-parameters 可在运行时保留方法的参数名称 -->
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <release>${java.version}</release>
                </configuration>
            </plugin>
        </plugins>

    </build>
    <!-- 不同数据库环境的 profile 配置 -->
    <profiles>
        <profile>
            <id>opengauss</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>com.snbc.bbpf</groupId>
                    <artifactId>bbpf-product-market-opengauss</artifactId>
                    <version>${project.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>mysql</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>com.snbc.bbpf</groupId>
                    <artifactId>bbpf-product-market-mysql</artifactId>
                    <version>${project.version}</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>
