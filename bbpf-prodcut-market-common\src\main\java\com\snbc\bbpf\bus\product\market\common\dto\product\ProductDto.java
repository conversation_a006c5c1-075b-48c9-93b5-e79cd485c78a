package com.snbc.bbpf.bus.product.market.common.dto.product;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/*
* @author: liangJB
     * 功能描述: <br>
     * 产品基础类
     * @date: 2020/8/12 13:13
* 字段名称	类型	是否必填	描述
productId	String	是	服务ID 内部关联
productCode	String	是	服务编号 外部展示
productName	String	是	服务名称
productCategoryName	String	是	服务类目名称
productTypeName	String	是	服务类型名称
chargeTypeName	String	是	收费方式名称
createTime	Date	是	添加时间
* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductDto {

    private String productId;

    private String productCode;

    private String productName;

    private String productCategoryName;

    private String productTypeName;

    private String chargeTypeName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime publishTime;
}
