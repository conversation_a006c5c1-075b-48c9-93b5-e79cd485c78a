package com.snbc.bbpf.bus.product.manager.controller.pay;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.config.payconfig.NotifyCommon;
import com.snbc.bbpf.bus.product.manager.utils.StreamUtil;
import com.snbc.bbpf.buslog.filter.RequestWrapper;
import com.snbc.weixin.api.internal.parser.xml.XmlToMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 支付宝回调控制器
 * @ClassName AlipayNotifyController
 * @date 2019-08-15 10:37
 */
@Slf4j
@RestController
@RequestMapping("/paycallback")
public class NotifyController {
    /**
     * 等待支付
     */
    private static final String WAIT_BUYER_PAY = "WAIT_BUYER_PAY";
    /**
     * 订单关闭
     */
    private static final String TRADE_CLOSED = "TRADE_CLOSED";
    /**
     * 订单支付
     */
    private static final String TRADE_SUCCESS = "TRADE_SUCCESS";
    public static final String TRADE_STATUS = "trade_status";
    public static final String RESULT_CODE = "result_code";
    public static final String RETURN_CODE = "return_code";
    public static final String WX_TRADE_SUCCESS = "SUCCESS";

    @Autowired
    private NotifyCommon notifyCommon;
    /**
     * rabitMQ
     */
    @Autowired
    private AmqpTemplate amqpTemplate;

    /**
     * 功能描述: <br>
     * 〈支付宝回调入库〉
     * @throws
     * @param: request
     * @return: java.lang.String
     * @author: gs
     * @date: 2019-08-15 10:54
     */
    
    @PostMapping(value = "/aliNotify")
    public void alipayNotify(HttpServletRequest request, HttpServletResponse httpServletResponse) throws IOException {
        log.debug("---------------Alipay call back with the beginning---------------");
        notifyCommon.printHeader(request);
        RequestWrapper req=RequestWrapper.wrapRequest(request);
        log.info("get Alipay content:{}",req.getBodyString());
        Map<String, String[]> params =  req.getParameterMap();
        // 解析
        try {
            Map<String, String> resultParams = notifyCommon.getResultParams(params);
            // 卖家等待支付
            if (WAIT_BUYER_PAY.equals(resultParams.get(TRADE_STATUS))) {
                log.debug("trade_status:{},wait buyer pay", resultParams.get(TRADE_STATUS));
            } else if (TRADE_CLOSED.equals(resultParams.get(TRADE_STATUS))) {
                // 订单关闭
                log.debug("trade_status:{},order Close end", resultParams.get(TRADE_STATUS));
            } else if (TRADE_SUCCESS.equals(resultParams.get(TRADE_STATUS))) {
                //插入回调标识
                resultParams.put("payType", Constant.ALIPAY);
                amqpTemplate.convertAndSend(Constant.PRODUCT_PAY, JSON.toJSONString(resultParams));
            }else {
                log.debug("Alipay callback unknown payment status does not do any processing");
            }
            httpServletResponse.getWriter().print(Constant.SUCCESS);
        } catch (Exception e) {
            log.error("Abnormal analysis of alipay callback information.params:{}",params, e);
        }
        log.debug("-----------------Alipay callback is over-------------------");
    }
    /**
     * 功能描述: <br>
     * 〈微信回调处理〉
     * @throws
     * @param: request
     * @param: response
     * @return: void
     * @author: gs
     * @date: 2019-08-15 14:16
     */
    
    @PostMapping(value = "/wxNotify")
    public void wechatNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.debug("-----------------Wechat callback started-------------------");
        String returnXML = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        printHeader(request);
        ServletInputStream in = null;
        try {
            in = request.getInputStream();
            String xmlMsg = StreamUtil.inputStream2String(in);
            log.debug("weixin result xml:{}", xmlMsg);
            Map<String, String> map = XmlToMap.xmlToMap(xmlMsg);
            if (WX_TRADE_SUCCESS.equals(map.get(RETURN_CODE)) && WX_TRADE_SUCCESS.equals(map.get(RESULT_CODE))) {
                //插入回调标识
                map.put("payType", Constant.WECHATPAY);
                amqpTemplate.convertAndSend(Constant.PRODUCT_PAY, JSON.toJSONString(map));
            }else {
                log.debug("Wechat calls back unknown payment status without doing any processing");
            }
        } catch (Exception e) {
            log.error("wechat pay result exception", e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("ServletInputStream close error", e);
                }
            }
        }
        response.getWriter().println(returnXML);
        log.debug("-----------------The wechat callback is finished------------------");
    }

    @SuppressWarnings("rawtypes")
    private void printHeader(HttpServletRequest request) {
        Enumeration headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String key = (String) headerNames.nextElement();
            log.debug("request header:{}  {}", key, request.getHeader(key));
        }
    }
}
