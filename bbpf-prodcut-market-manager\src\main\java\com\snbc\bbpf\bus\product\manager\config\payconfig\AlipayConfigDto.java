package com.snbc.bbpf.bus.product.manager.config.payconfig;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 功能描述: <br>
 * 〈支付宝配置〉
 *
 * @param: null
 * @return:
 * @exception
 * @author: gs
 * @date: 2019-08-08 18:39
 */

@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2017-09-08T08:14:27.030Z")

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlipayConfigDto {
    /**
     * charset
     */
    @JsonProperty("charset")
    private String charset;

    /**
     * callbackAddr
     */
    @JsonProperty("callbackAddr")
    private String callbackAddr;

    /**
     * appPrivateKey
     */
    @JsonProperty("appPrivateKey")
    private String appPrivateKey;

    /**
     * appId
     */
    @JsonProperty("appId")
    private String appId;

    /**
     * tenantId
     */
    @JsonProperty("tenantId")
    private String tenantId;

    /**
     * signType
     */
    @JsonProperty("signType")
    private String signType;

    /**
     * pid
     */
    @JsonProperty("pid")
    private String pid;

    /**
     * publicKey
     */
    @JsonProperty("publicKey")
    private String publicKey;

    /**
     * alipayVersion
     */
    @JsonProperty("alipayVersion")
    private String alipayVersion;

}

