package com.snbc.bbpf.bus.product.market.common.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付流水号表
 */
@Data
public class PurchasePayTrack {
    //ID
    private String purchasePayTrackId;
    //点单号
    private String purchaseNo;
    //创建流水时间
    private LocalDateTime payTrackTime;
    /**
     * 支付状态 0待支付1支付成功2关闭
     */
    private Integer payStatus;
    /**
     * 流水号
     */
    private String payTrackNo;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 支付渠道
     */
    private String payType;
}
