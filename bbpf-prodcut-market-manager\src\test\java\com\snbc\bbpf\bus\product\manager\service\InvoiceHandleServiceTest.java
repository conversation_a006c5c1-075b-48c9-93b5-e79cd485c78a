/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: InvoiceHandleServiceTest
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/12/24 14:26
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/12/24 14:26
 */
package com.snbc.bbpf.bus.product.manager.service;


import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.impl.InvoiceHandleServiceImpl;
import com.snbc.bbpf.bus.product.market.common.entity.InvoiceApply;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceApplyMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceDeliveryMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceTitleMapper;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ApplyInvoiceParam;
import org.codehaus.jackson.map.ObjectMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.times;

public class InvoiceHandleServiceTest {
    @Mock
    private InvoiceTitleMapper invoiceTitleMapper;
    @Mock
    private InvoiceApplyMapper invoiceApplyMapper;
    @Mock
    InvoiceDeliveryMapper invoiceDeliveryMapper;
    @InjectMocks
    InvoiceHandleService invoiceHandleService = new InvoiceHandleServiceImpl();

    private ApplyInvoiceParam applyInvoiceParam;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Before
    public void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);
        String paramJson = "{\n" +
                "    \"tenantId\": \"143\",\n" +
                "    \"applyType\": 2,\n" +
                "    \"invoiceType\": 2,\n" +
                "    \"invoiceTitle\": \"企业\",\n" +
                "    \"invoiceContent\": \"增值服务\",\n" +
                "    \"applyUser\": \"1\",\n" +
                "    \"invoiceCarrier\": 1,\n" +
                "    \"invoiceAmount\": 400,\n" +
                "\t\"productPurchaseId\":[\"2\"],\n" +
                "    \"expressCompany\": null,\n" +
                "    \"expressNo\": null,\n" +
                "    \"taxRegisterNo\": \"**************\",\n" +
                "    \"bankName\": \"农业银行\",\n" +
                "    \"bankNo\": \"37001111222233330137\",\n" +
                "    \"registerAddress\": \"北京市海淀区\",\n" +
                "    \"registerPhonenum\": \"***********\",\n" +
                "    \"mail\": \"<EMAIL>\",\n" +
                "    \"tenantName\": \"农夫山泉\",\n" +
                "    \"isUpdateInvoiceTit\":true,\n" +
                "    \"receiverAddress\":null,\n" +
                "    \"receiverTel\":\"***********\",\n" +
                "    \"receiverName\":\"laowang\"\n" +
                "}";

        applyInvoiceParam = objectMapper.readValue(paramJson, ApplyInvoiceParam.class);

    }

    /***
     * @Description: 测试纸质发票不填写发送地址
     * @Author: wangsong
     * @CreateDate: 2020/12/24 14:31
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/12/24 14:31
     * @return :        void
     */
    @Test
    public void testInvoiceDeliveryProcess_receiverAddressIsNull() {
        try {
            invoiceHandleService.invoiceDeliveryProcess(applyInvoiceParam);
        } catch (BusinessException e) {
            Assert.assertEquals(e.getMessage(), Errors.DELIVERYNOTNULL.getMessage());
        }
    }

    /***
     * @Description: 电子发票无快递信息
     * @Author: wangsong
     * @CreateDate: 2020/12/24 14:40
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/12/24 14:40
     * @return :        void
     */
    @Test
    public void testInvoiceDeliveryProcess_electronicInvoiceCannotDelivery() throws BusinessException {
        //电子类型发票
        applyInvoiceParam.setInvoiceCarrier(2);
        String deliveryId = invoiceHandleService.invoiceDeliveryProcess(applyInvoiceParam);
        //电子发票没有邮寄信息，所以快递id为空
        Assert.assertEquals(deliveryId, "");
    }

    /***
     * @Description: 新增发票抬头测试
     * @Author: wangsong
     * @CreateDate: 2020/12/24 14:52
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/12/24 14:52
     * @return :        void
     */
    @Test
    public void testInvoiceTitleProcess_invoiceTitleProcess() {
        Mockito.when(invoiceTitleMapper.selectInvoiceByTenantId(Mockito.any())).thenReturn(0);
        invoiceHandleService.invoiceTitleProcess(applyInvoiceParam);
        //断言是否调用了新增发票抬头方法
        Mockito.verify(invoiceTitleMapper,times(1))
                .insertInvoiceTitle(Mockito.any());
    }


    /***
      * @Description:    更新发票抬头测试
      * @Author:         wangsong
      * @CreateDate:     2020/12/24 15:05
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/12/24 15:05
      * @return :        void
     */
    @Test
    public void testInvoiceTitleProcess_updateInvoiceTitle() {
        Mockito.when(invoiceTitleMapper.selectInvoiceByTenantId(Mockito.any())).thenReturn(0);
        //更新发票抬头
        applyInvoiceParam.setIsUpdateInvoiceTit(true);
        invoiceHandleService.invoiceTitleProcess(applyInvoiceParam);
        //判断是否调用了更新发票抬头方法
        Mockito.verify(invoiceTitleMapper,times(1)).updateInvoiceTit(Mockito.any());
    }

    /***
      * @Description:    纸质发票金额小于300抛出业务异常
      * @Author:         wangsong
      * @CreateDate:     2020/12/24 16:20
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/12/24 16:20
      * @return :        void
     */
    @Test
    public void testCreateInvoiceApply_invoiceAmountMoreThreeHundred() throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException, InstantiationException {
        try {
            //反射获取类
            Class<?> invoiceHandleServiceImpl = Class.forName("com.snbc.bbpf.bus.product.manager.service.impl.InvoiceHandleServiceImpl");
            List<BigDecimal> amountList = new ArrayList<>();
            //模拟数据库返回的订单金额
            amountList.add(new BigDecimal(100.00));
            //反射获取方法
            Method method = invoiceHandleServiceImpl.getDeclaredMethod("createInvoiceApply",
                    ApplyInvoiceParam.class, InvoiceApply.class, String.class, List.class);
            method.setAccessible(true);
            //反射调用方法，抛出发票金额小于300业务异常
            method.invoke(invoiceHandleServiceImpl.newInstance(), applyInvoiceParam, new InvoiceApply(), "12321", amountList);
        } catch (InvocationTargetException e) {
            Assert.assertEquals(e.getTargetException().getMessage(),Errors.AMOUNTERROR.getMessage());
        }
    }
}
