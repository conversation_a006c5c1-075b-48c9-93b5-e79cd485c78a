/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: ProductOrderServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 负责产品订单查询先关的接口实现
 * @Author: LiangJb
 * @CreateDate: 2020/9/28 15:59
 */
package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.service.ProductOrderService;
import com.snbc.bbpf.bus.product.manager.converter.ProductOrderListDtoToProductShopOrderDtoConverter;
import com.snbc.bbpf.bus.product.manager.converter.ProductOrderListDtoToProductOrderDtoConverter;
import java.util.stream.Collectors;
import com.snbc.bbpf.bus.product.manager.utils.PageConvertUtil;
import com.snbc.bbpf.bus.product.manager.utils.ProductQueryUtil;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductAvailablePageDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductLOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderListDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductShopOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceBuyPDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceShopOrderDto;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductAvailableMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductOrderMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseOfflineMapper;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductAvailablePageQueryBase;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderVemsPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductShopVemsPageQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

@Service
public class ProductOrderServiceImpl implements ProductOrderService {
    private static final int ONE = 1;
    @Autowired
    private ProductOrderMapper productOrderMapper;
    @Autowired
    private ProductAvailableMapper productAvailableMapper;
    @Autowired
    private ProductOrderServiceExtImpl productOrderServiceExtImpl;
    @Autowired
    private PurchaseOfflineMapper purchaseOfflineMapper;
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 1.3.3. 统计服务订单记录
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: ProductShopOrderDto
     */
    @Override
    public PageInfo<ProductShopOrderDto> productOrderListWithPage(ProductOrderPageQuery productOrderPageQuery) {
        PageMethod.startPage(productOrderPageQuery.getPageNum(), productOrderPageQuery.getPageSize());

        List<ProductOrderListDto> productList = productOrderMapper.selectProductOrderList(productOrderPageQuery);
        PageInfo<ProductOrderListDto> pageInfoOld = new PageInfo<>(productList);
        List<ProductShopOrderDto> productVoList = new ArrayList<>();
        productList.forEach(pitem -> {
            ProductShopOrderDto productShopOrderDto = ProductOrderListDtoToProductShopOrderDtoConverter.INSTANCE.to(pitem);
            productShopOrderDto.setProductGradeUnit (ProductQueryUtil.getProductGradeUnit( pitem.getChargeTypeName()));
            productVoList.add(productShopOrderDto);
        });
        PageInfo<ProductShopOrderDto> pageInfo = new PageInfo<>(productVoList);
        PageConvertUtil.convert(pageInfo, pageInfoOld);
        return pageInfo;
    }



    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 1.6.1. 服务订单记录
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: ProductShopOrderDto
     */
    @Override
    public PageInfo<ProductOrderDto> serviceOrderListWithPage(ProductOrderPageQuery productOrderPageQuery) {
        PageMethod.startPage(productOrderPageQuery.getPageNum(), productOrderPageQuery.getPageSize());
        List<ProductOrderListDto> productList = productOrderMapper.selectProductOrderList(productOrderPageQuery);
        PageInfo<ProductOrderListDto> pageInfoOld = new PageInfo<>(productList);
        List<ProductOrderDto> productVoList = new ArrayList<>();
        productList.forEach(pitem ->productVoList.add(ProductOrderListDtoToProductOrderDtoConverter.INSTANCE.to(pitem)));
        PageInfo<ProductOrderDto> pageInfo = new PageInfo<>(productVoList);
        PageConvertUtil.convert(pageInfo, pageInfoOld);
        return pageInfo;
    }

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 2.3.1. 服务订单列表
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: ProductShopOrderDto
     */
    @Override
    public PageInfo<ServiceShopOrderDto> rrqserviceOrderListWithPage(ProductOrderPageQuery productOrderPageQuery) {
        PageMethod.startPage(productOrderPageQuery.getPageNum(), productOrderPageQuery.getPageSize());
        List<ProductOrderListDto> productList = productOrderMapper.selectProductOrderList(productOrderPageQuery);
        // 字典值转换
        List<ServiceShopOrderDto> productVoList = productOrderServiceExtImpl.convertRrqServiceOrderListResult(productList);
        PageInfo<ServiceShopOrderDto> pageInfo = new PageInfo<>(productVoList);
        PageInfo<ProductOrderListDto> pageInfoOld = new PageInfo<>(productList);
        PageConvertUtil.convert(pageInfo, pageInfoOld);
        return pageInfo;
    }

    @Override
    public PageInfo<ProductAvailablePageDto> seviceAvailableListWithPage(ProductAvailablePageQueryBase productAvailablePageQueryBase) {
        PageMethod.startPage(productAvailablePageQueryBase.getPageNum(), productAvailablePageQueryBase.getPageSize());
        List<ProductAvailablePageDto> productList = productAvailableMapper.selectseviceAvailableList(productAvailablePageQueryBase);
        return new PageInfo<>(productList);
    }
    @Override
    public ProductAvailablePageDto availableDetail(String availableId,String tenantId) {
        ProductAvailablePageDto paData = productAvailableMapper.selectseviceAvailable(availableId,tenantId);
        if(null != paData){
            if(null != paData.getDueTime() && paData.getDueTime().isAfter(LocalDateTime.now())){
                paData.setServiceStatus("已开通");
                paData.setResidueDay(LocalDateTime.now().until(paData.getDueTime(), ChronoUnit.DAYS) +"天");
            }else{
                paData.setServiceStatus("已到期");
                paData.setResidueDay("0天");
            }
            return paData;
        }
        return null;
    }
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 1.6.2.1. 订单详情（修改）
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: ProductShopOrderDto
     */
    @Override
    public ProductOrderDetailDto orderDetail(String purchaseNo) {
        ProductOrderDetailDto detail = productOrderMapper.getProductOrderDetailByPurchaseNo(purchaseNo);
        if(String.valueOf(Constant.PAY_TYPE_OFFLINE).equalsIgnoreCase(detail.getPayType())){
            PurchaseOffline purchaseOffline = purchaseOfflineMapper.selectByPurchaseNo(purchaseNo);
            if (null != purchaseOffline) {
                detail.setPurchaseChannelNo(purchaseOffline.getRemitTradeNo());
            }
        }
        return detail;
    }


    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 1.6.2.2. 服务订单设备详细记录（修改）
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: ProductShopOrderDto
     */
    @Override
    public PageInfo<ProductLOrderDto> seviceVemsOrderListWithPage(ProductOrderVemsPageQuery productOrderVemsPageQuery) {
        PageMethod.startPage(productOrderVemsPageQuery.getPageNum(), productOrderVemsPageQuery.getPageSize());
        List<ProductLOrderDto> productLorderList = productOrderMapper.getProductRecordLstByPurchaseNo(productOrderVemsPageQuery.getPurchaseNo());
        return new PageInfo<>(productLorderList);
    }


    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 2.5.2.1. 设备标准列表
     * dateType	查询条件	int	时间查询方式	是	1为默认查询 2为已到期3 为不足一个月
     * sb.add("GNFSQCN001000116"); sb.add("GNFSQCN001000117");
     * 临时转换的调试数据ObjectMapper objectMapper = new ObjectMapper();
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: ProductShopOrderDto
     */
    @Override
    public PageInfo<ServiceBuyPDto> sevicesShopListWithPage(ProductShopVemsPageQuery productOrderVemsPageQuery) {
        PageMethod.startPage(productOrderVemsPageQuery.getPageNum(), productOrderVemsPageQuery.getPageSize());
        //设置日期格式
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime date = LocalDateTime.now();
        //不为默认条件查询
        if (productOrderVemsPageQuery.getDateType() != ONE) {
            productOrderServiceExtImpl.convertTime(productOrderVemsPageQuery, df, date);
        }
        List<ServiceBuyPDto> serviceBuyLst = productOrderMapper.getServiceBuyLstByTenantId(productOrderVemsPageQuery);
        if (serviceBuyLst != null && !serviceBuyLst.isEmpty()) {
            //循环计算时间，并统计要查询售货机的编号，去业务平台对接
            ArrayList<String> sb = new ArrayList<>();
            serviceBuyLst.forEach(item ->sb.add(item.getVemsId()));
            String[] dArray = new String[sb.size()];
            sb.toArray(dArray);
            //不带到期时间使用feign调用云平台售货机分页接口
            serviceBuyLst.forEach(item -> productOrderServiceExtImpl.convertServiceBuyPDto(item, date));
        }
        return new PageInfo<>(serviceBuyLst);
    }
}
