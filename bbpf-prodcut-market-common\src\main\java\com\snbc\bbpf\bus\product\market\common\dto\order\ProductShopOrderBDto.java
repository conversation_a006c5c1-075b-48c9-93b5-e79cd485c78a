package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/*
* @author: liangJB
     * 功能描述: <br>
     * 基础订单列表
     * @date: 2020/8/12 13:13
purchaseNo	String	是	订单编号
productName	String	是	服务名称
shopName	String	是	商户名称

productQuantity	int	是	购买量/台
purchaseAmount	Decimal	是	总金额(元)
discountAmount	Decimal	是	优惠金额(元)
paymentAmount	Decimal	是	实付金额(元)
payTypeName	String	是	支付方式
purchaseStatus	int	是	具体直参考订单状态0：待支付 1：已支付 2：购买成功 3:已取消
purchaseTime	Date	是	下单时间
payTime	Date	是	支付时间

* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductShopOrderBDto {
    
    private String productId;
    
    private String purchaseNo;
    
    private String productName;
    
    private String shopName;
    
    private String payType;
    
    private String payTypeName;
    
    private String productImage;
    
    private Integer productQuantity;
    
    private String productGrade;
    
    private Integer purchaseStatus;
    
    private String purchaseStatusName;
    
    private Integer probation;

    
    private BigDecimal purchaseAmount;
    
    private BigDecimal discountAmount;
    
    private BigDecimal paymentAmount;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchaseTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime brankstartTime;
    /*
    * @author: liangJB
         * 功能描述: <br>
         * 给各个值赋值，除去重复赋值代码
         * @date: 2020/8/12 13:13
    purchaseNo	String	是	订单编号
    productName	String	是	服务名称
    shopName	String	是	商户名称

    productQuantity	int	是	购买量/台
    purchaseAmount	Decimal	是	总金额(元)
    discountAmount	Decimal	是	优惠金额(元)
    paymentAmount	Decimal	是	实付金额(元)
    payTypeName	String	是	支付方式
    purchaseStatus	int	是	具体直参考订单状态0：待支付 1：已支付 2：购买成功 3:已取消
    purchaseTime	Date	是	下单时间
    payTime	Date	是	支付时间

    * */
    public void setProductShopOrderBDto(ProductShopOrderBDto productShopOrderBDto) {

        setProductId(productShopOrderBDto.getProductId());
        setPurchaseNo(productShopOrderBDto.getPurchaseNo());
        setProductName(productShopOrderBDto.getProductName());
        setShopName(productShopOrderBDto.getShopName());
        setPayTypeName(productShopOrderBDto.getPayTypeName());
        setPayType(productShopOrderBDto.getPayType());

        setProductQuantity(productShopOrderBDto.getProductQuantity());
        setPurchaseStatus(productShopOrderBDto.getPurchaseStatus());
        setPurchaseStatusName(productShopOrderBDto.getPurchaseStatusName());

        setPurchaseAmount(productShopOrderBDto.getPurchaseAmount());
        setDiscountAmount(productShopOrderBDto.getDiscountAmount());
        setPaymentAmount(productShopOrderBDto.getPaymentAmount());

        setPurchaseTime(productShopOrderBDto.getPurchaseTime());
        setPayTime(productShopOrderBDto.getPayTime());
        setCancelTime(productShopOrderBDto.getCancelTime());
    }

}
