<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.PurchaseTrackMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack" >
    <result column="purchase_track_id" property="purchaseTrackId" jdbcType="VARCHAR" />
    <result column="purchase_no" property="purchaseNo" jdbcType="VARCHAR" />
    <result column="purchase_time" property="purchaseTime" jdbcType="TIMESTAMP" />
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
    <result column="success_time" property="successTime" jdbcType="TIMESTAMP" />
    <result column="cancel_time" property="cancelTime" jdbcType="TIMESTAMP" />
    <result column="cancel_reson" property="cancelReson" jdbcType="VARCHAR" />
    <result column="pay_type" property="payType" jdbcType="INTEGER" />
    <result column="purchase_amount" property="purchaseAmount" jdbcType="DOUBLE" />
    <result column="payment_amount" property="paymentAmount" jdbcType="DOUBLE" />
    <result column="discount_amount" property="discountAmount" jdbcType="DOUBLE" />
    <result column="purchase_status" property="purchaseStatus" jdbcType="INTEGER" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="user_id" property="userId" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack" >
    insert into t_purchase_track (purchase_track_id, purchase_no, purchase_time, 
      pay_time, success_time, cancel_time, 
      cancel_reson, pay_type, purchase_amount, 
      payment_amount, discount_amount, purchase_status, 
      tenant_id, user_id, create_time
      )
    values (#{purchaseTrackId,jdbcType=VARCHAR}, #{purchaseNo,jdbcType=VARCHAR}, #{purchaseTime,jdbcType=TIMESTAMP}, 
      #{payTime,jdbcType=TIMESTAMP}, #{successTime,jdbcType=TIMESTAMP}, #{cancelTime,jdbcType=TIMESTAMP}, 
      #{cancelReson,jdbcType=VARCHAR}, #{payType,jdbcType=INTEGER}, #{purchaseAmount,jdbcType=DOUBLE}, 
      #{paymentAmount,jdbcType=DOUBLE}, #{discountAmount,jdbcType=DOUBLE}, #{purchaseStatus,jdbcType=INTEGER}, 
      #{tenantId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack" >
    insert into t_purchase_track
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="purchaseTrackId != null" >
        purchase_track_id,
      </if>
      <if test="purchaseNo != null" >
        purchase_no,
      </if>
      <if test="purchaseTime != null" >
        purchase_time,
      </if>
      <if test="payTime != null" >
        pay_time,
      </if>
      <if test="successTime != null" >
        success_time,
      </if>
      <if test="cancelTime != null" >
        cancel_time,
      </if>
      <if test="cancelReson != null" >
        cancel_reson,
      </if>
      <if test="payType != null" >
        pay_type,
      </if>
      <if test="purchaseAmount != null" >
        purchase_amount,
      </if>
      <if test="paymentAmount != null" >
        payment_amount,
      </if>
      <if test="discountAmount != null" >
        discount_amount,
      </if>
      <if test="purchaseStatus != null" >
        purchase_status,
      </if>
      <if test="tenantId != null" >
        tenant_id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="purchaseTrackId != null" >
        #{purchaseTrackId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseNo != null" >
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null" >
        #{purchaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null" >
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="successTime != null" >
        #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null" >
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelReson != null" >
        #{cancelReson,jdbcType=VARCHAR},
      </if>
      <if test="payType != null" >
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="purchaseAmount != null" >
        #{purchaseAmount,jdbcType=DOUBLE},
      </if>
      <if test="paymentAmount != null" >
        #{paymentAmount,jdbcType=DOUBLE},
      </if>
      <if test="discountAmount != null" >
        #{discountAmount,jdbcType=DOUBLE},
      </if>
      <if test="purchaseStatus != null" >
        #{purchaseStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>