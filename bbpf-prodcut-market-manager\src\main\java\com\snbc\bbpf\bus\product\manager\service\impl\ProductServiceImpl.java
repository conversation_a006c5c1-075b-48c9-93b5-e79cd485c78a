/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: ProductServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 服务信息业务处理层
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:21
 */
package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.DictValueService;
import com.snbc.bbpf.bus.product.manager.service.ProductService;
import com.snbc.bbpf.bus.product.manager.converter.ProductListDtoToProductStatusDtoConverter;
import java.util.stream.Collectors;
import com.snbc.bbpf.bus.product.manager.utils.PageConvertUtil;
import com.snbc.bbpf.bus.product.manager.utils.ProductQueryUtil;
import com.snbc.bbpf.bus.product.market.common.dto.product.PermissionLevelDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailExDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductGradeDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductListDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductSimpleDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductStatusDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ServicePDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ServicePPDto;
import com.snbc.bbpf.bus.product.market.common.entity.DictValue;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import com.snbc.bbpf.bus.product.market.common.entity.ProductServices;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPermissionMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductQueryMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductServicesMapper;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductPageQuery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class ProductServiceImpl implements ProductService {
    private final ProductQueryMapper productQueryMapper;
    private final ProductServicesMapper productServicesMapper;
    private final ProductPermissionMapper productPermissionMapper;
    private final DictValueService dictValueService;

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * //按日期、名字、查询服务列表不含状态
     * @date: 2020/8/12 13:21
     * @param: productPageQuery
     * @return: PageInfo<ProductDto>
     */
    @Override
    public PageInfo<ProductDto> seviceListWithPage(ProductPageQuery productPageQuery) {
        PageHelper.startPage(productPageQuery.getPageNum(), productPageQuery.getPageSize());
        List<ProductListDto> productList = productQueryMapper.selectProductList(productPageQuery);
        PageInfo<ProductListDto> pageInfoOld = new PageInfo<>(productList);
        List<ProductStatusDto> productVoList = productList.stream()
            .map(ProductListDtoToProductStatusDtoConverter.INSTANCE::to)
            .collect(Collectors.toList());
        PageInfo<ProductDto> pageInfo = new PageInfo(productVoList);
        PageConvertUtil.convert(pageInfo, pageInfoOld);
        return pageInfo;
    }

    // 将付费类型单位转为map:wjc 20211202 add
    private Map<String, String> convertChargeType() {
        try {
            List<DictValue> dictValueList = dictValueService.selectDictValueByTypeCode("charge_type_unit");
            return dictValueList.stream().collect(Collectors.toMap(DictValue::getValueCode, DictValue::getValueName));
        } catch (Exception e) {
            log.error("Get paid type unit exception", e);
            return new HashMap<>();
        }
    }


    /**
     * @author: wjc1  更新
     * 功能描述: <br>
     * //类型、支付方式负责查询产品发布，新增关于是否是新产品判断：1，满足发布时间小于1个月
     * @date: 2021/11/12 13:21
     * @param: productPageQuery
     * @return: PageInfo<ServicePDto>
     */
    @Override
    public PageInfo<ServicePDto> sevicesListWithPage(ProductPageQuery productPageQuery) {
        PageMethod.startPage(productPageQuery.getPageNum(), productPageQuery.getPageSize());
        List<ProductListDto> productList = productQueryMapper.selectProductListForTenant(productPageQuery);
        // 根据服务id查询默认的梯度
        List<ProductGradeDto> productGradeDtoList = productQueryMapper.selectDefaultGradeByProductIds(productList.stream()
                .map(ProductListDto::getProductId).collect(Collectors.toList()));
        Map<String, ProductGradeDto> productGradeDtoMap = productGradeDtoList.stream()
                .collect(Collectors.toMap(ProductGradeDto::getProductId, productGradeDto -> productGradeDto));
        Date dtNow = Date.from(Instant.now());
        List<ServicePDto> productVoList = new ArrayList<>();
        //将付费类型单位转为map
        Map<String, String> finalMap = convertChargeType();
        productList.forEach(item -> {
            ServicePDto productDto = getServicePDto(dtNow, finalMap, productGradeDtoMap, item);
            productVoList.add(productDto);
        });
        PageInfo<ServicePDto> pageInfo = new PageInfo<>(productVoList);
        PageInfo<ProductListDto> pageInfoOld = new PageInfo<>(productList);
        PageConvertUtil.convert(pageInfo, pageInfoOld);
        return pageInfo;
    }

    /**
     *
     * @param dtNow 当前时间
     * @param finalMap 付费类型单位
     * @param productGradeDtoMap  梯度map
     * @param item 产品
     * @return
     */
    @NotNull
    private static ServicePDto getServicePDto(Date dtNow, Map<String, String> finalMap, Map<String, ProductGradeDto> productGradeDtoMap, ProductListDto item) {
        ServicePDto productDto = ServicePDto.builder().isNewProduct(ProductQueryUtil.ZERO)
                .productBrief(item.getProductBrief()).productImage(item.getProductImage())
                .productId(item.getProductId()).productCode(item.getProductCode())
                .productName(item.getProductName()).productTypeName(item.getProductTypeName()).build();
        //发布时间转为 date类型
        Date date = Date.from(item.getPublishTime().atZone(ZoneId.systemDefault()).toInstant());
        //最近1个月发布的服务，新服务标志不再展示。
        if (dtNow.getTime() - date.getTime() < ProductQueryUtil.NO_EIGHT_SIXM_FOURHK * ProductQueryUtil.NO_THIRTY) {
            productDto.setIsNewProduct(ProductQueryUtil.ONE);
        }
        ProductGradeDto grade = productGradeDtoMap.get(item.getProductId());
        if (grade == null) {
            throw new BusinessException(item.getProductName() + Errors.PRODUCTNOTHASGRAGE.getMessage(),
                    Errors.PRODUCTNOTHASGRAGE.getCode());
        }
        productDto.setDefaultPrice(String.valueOf(grade.getPrice()));
        productDto.setDefaultPriceUnit("");
        productDto.setDefaultGrade(grade.getGrade());
        if (StringUtils.isNotBlank(finalMap.get(grade.getGradeUnit()))) {
            productDto.setDefaultUnitCode(finalMap.get(grade.getGradeUnit()));
            productDto.setDefaultPriceUnit("/" + grade.getGrade() + finalMap.get(grade.getGradeUnit()));
        }
        return productDto;
    }


    /**
     * @author: LiangJB
     * 功能描述: <br>
     * //按日期、名字、类型、支付方式负责对现有列表的综合查询
     * //已购产品列表从订单列表获取已够产品信息
     * @date: 2020/8/12 13:21
     * @param: productPageQuery
     * @return: PageInfo<ServicePPDto>
     */
    @Override
    public PageInfo<ServicePPDto> sevicesOrderListWithPage(ProductPageQuery productPageQuery) {
        PageHelper.startPage(productPageQuery.getPageNum(), productPageQuery.getPageSize());
        List<ServicePPDto> productList = productQueryMapper.getProductLstByTranid(productPageQuery);
        return new PageInfo<>(productList);
    }

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * //根据具体ID，获取可编辑的简单服务信息
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: ProductSimpleDto
     */
    @Override
    public ProductSimpleDto seviceDetail(String productId) {
        ProductSimpleDto productSimpleDto = productQueryMapper.getProductSimpleByProductId(productId);
        if (null == productSimpleDto) {
            throw new BusinessException(Errors.SERVICEDOESNOTEXIST.getMessage(), Errors.SERVICEDOESNOTEXIST.getCode(), null);
        }
        //获取与产品服务关联的功能
        List<Permission> permissions = productPermissionMapper.selelctPermissionByProductId(productId);
        //根据功能的父级id分组
        Map<String, List<Permission>> groupByParentIdMap = permissions.stream()
                .collect(Collectors.groupingBy(Permission::getParentId));
        List<PermissionLevelDto> permissionInfo = new ArrayList<>();
        //循环分组数据，筛选前端需要勾选的权限id
        groupByParentIdMap.keySet().forEach(parentId -> {
            //获取该父级id下的所有子节点的菜单类型，因为会保存菜单与产品的关联关系，为了保证某菜单下如果没有接口也能返回给前端
            List<String> permissionTypeList = groupByParentIdMap.get(parentId).stream().map(Permission::getPermissionType).collect(Collectors.toList());
            //父id的下级还是菜单的话直接返回其id，如果是菜单类型给前端的数据组成没有子节点的list
            if (permissionTypeList.contains(Constant.TWO)) {
                groupByParentIdMap.get(parentId).forEach(permission -> {
                    //为了去重，某个大菜单的会有小菜单，会与map中的key重复
                    if (!groupByParentIdMap.keySet().contains(permission.getPermissionId())) {
                        PermissionLevelDto permissionLevelDto = PermissionLevelDto.builder().permissionId(permission.getPermissionId())
                                .childIds(new ArrayList<>())
                                .build();
                        permissionInfo.add(permissionLevelDto);
                    }
                });
            } else {
                //组装permissionId及其子id
                PermissionLevelDto permissionLevelDto = PermissionLevelDto.builder().permissionId(parentId)
                        .childIds(groupByParentIdMap.get(parentId).stream().map(Permission::getPermissionId).collect(Collectors.toList()))
                        .build();
                permissionInfo.add(permissionLevelDto);
            }
        });
        productSimpleDto.setPermissionInfo(permissionInfo);
        return productSimpleDto;
    }


    /***
      * @Description:    根据具体ID，获取可编辑的简单服务信息
      * @Author:         LiangJB
      * @param :         productId
      * @CreateDate:     2020/8/31 15:48
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/8/31 15:48
      * @return :        ProductDetailDto
     */
    @Override
    public ProductDetailDto seviceStatusDetail(String productId) {
        ProductDetailDto result = productQueryMapper.getProductDetailByProductId(productId);
        List<ProductGradeDto> gradeLst = productQueryMapper.selectGradeByProductId(productId);
        result.setProductGradeLst(gradeLst);
        return result;
    }

    /**
     * @author: wjc1
     * 功能描述: <br>
     * //根据具体ID，获取服务详细信息
     * 根据tenantId 来查询是否有购买记录，如有则没有试用权限
     * @date: 2021/12/09 13:21
     * @param: tenantId
     * @param: productId
     * @return: ProductDetailExDto
     */
    @Override
    public ProductDetailExDto seviceTypeDetail(String productId, String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            throw new BusinessException(Errors.TENANTIDNULL.getMessage(),
                    Errors.TENANTIDNULL.getCode());
        }
        ProductDetailExDto result = productQueryMapper.getProductDetailExByProductId(productId);
        //20211117 WJC ADD 如果为空 抛异常
        Errors.PRODUCTNOTEXSIT.assertNotNull(result);
        //给赋值单位
        result.setProductGradeUnit("");
        Map<String, String> finalMap = convertChargeType();
        if (StringUtils.isNotBlank(finalMap.get(result.getChargeTypeName()))) {
            result.setProductGradeUnit(finalMap.get(result.getChargeTypeName()));
        }
        //判断是否购买服务
        List<ProductCountDto> countDto = productQueryMapper.getProductCountByPIdAndTId(productId, tenantId);
        if (!CollectionUtils.isEmpty(countDto)) {
            result.setHasPurchaseProduct(true);
        }
        List<ProductGradeDto> gradeLst = productQueryMapper.selectGradeByProductId(productId);
        result.setProductGradeLst(gradeLst);
        return result;
    }

    /***
     * @Description: 删除产品服务
     * @Author: wangsong
     * @param :         productId
     * @CreateDate: 2021/11/17 13:32
     * @UpdateDate: 2021/11/17 13:32
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void delProductService(String productId) throws BusinessException {
        ProductServices productServices = productServicesMapper.selectByPrimaryKey(productId);
        if (!productServices.getProductStatus().equals(Constant.ZERO)) {
            throw new BusinessException(Errors.CANNOTDEL.getMessage(), Errors.CANNOTDEL.getCode(), null);
        }
        //删除产品服务
        productServicesMapper.delProductService(productId);
        //删除关联功能
        productPermissionMapper.deleteByProductId(productId);
    }
    /**
     * @author: liangJB
     * 功能描述: <br>
     * 查询线下的离线数据
     * @date: 2020/8/12 13:26
     * @param: productId
     * @return: ProductDetailExDto
     */
    @Override
    public String getOfflineDetail() {
        return productQueryMapper.getOfflineDetail();
    }
}
