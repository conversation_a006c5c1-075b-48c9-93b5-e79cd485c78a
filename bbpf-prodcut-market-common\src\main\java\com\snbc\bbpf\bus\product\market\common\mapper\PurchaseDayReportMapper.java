package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.PurchaseDayReport;
import org.apache.ibatis.annotations.Mapper;
/**
 * @author: LiangJB
 * 功能描述: <br>
 * 日报表表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface PurchaseDayReportMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 删除数据
     * @date: 2020/8/12 13:21
     */
    int deleteByPrimaryKey(String dayReportId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(PurchaseDayReport purchaseDayReport);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(PurchaseDayReport purchaseDayReport);

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 查询日报表数据
     * @date: 2020/8/12 13:21
     */
    PurchaseDayReport selectByPrimaryKey(String dayReportId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 修改日报表数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKeySelective(PurchaseDayReport purchaseDayReport);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 修改日报表数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKey(PurchaseDayReport purchaseDayReport);
}
