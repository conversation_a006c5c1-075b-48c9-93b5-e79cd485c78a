/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.mapper;


import com.snbc.bbpf.bus.product.market.common.dto.product.ProductPermissionDto;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: ProductPermissionMapper
 * 产品服务跟功能关系类
 * 含查询，插入，批量删除产品ID，批量删除功能ID关联
 * @module: bbpf-product-manager
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Mapper
public interface ProductPermissionMapper {

    /**
     * 插入权限关系
     * @param ppList
     */
    void insertPPermissionList(List<ProductPermissionDto> ppList);
    /**
     *
     * @param publicIds
     * @return
     */
    int deleteByProductIds(String[] publicIds);
    /**
     *
     * @param publicId
     * @return
     */
    int deleteByProductId(@Param("publicId")String publicId);
    /**
     *
     * @param permissionIds
     * @return
     */
    int deleteBypermissionIds(String[] permissionIds);

    List<Permission> selelctPermissionByProductId(String productId);

}
