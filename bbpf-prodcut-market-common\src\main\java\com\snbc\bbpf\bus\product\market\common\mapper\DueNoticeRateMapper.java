package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.DueNoticeRate;
import org.apache.ibatis.annotations.Mapper;
/**
 * @author: LiangJB
 * 功能描述: <br>
 * 到期通知频率表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface DueNoticeRateMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 删除数据
     * @date: 2020/8/12 13:21
     * @param: noticeRateId
     * @return: interesting
     */
    int deleteByPrimaryKey(String noticeRateId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     * @param: DueNoticeRate
     * @return: 编号
     */
    int insert(DueNoticeRate dueNoticeRate);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     * @param: DueNoticeRate
     * @return: 编号
     */
    int insertSelective(DueNoticeRate dueNoticeRate);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 获取数据
     * @date: 2020/8/12 13:21
     * @param: noticeRateId
     * @return: DueNoticeRate
     */
    DueNoticeRate selectByPrimaryKey(String noticeRateId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     * @param: record
     * @return: 编号
     */
    int updateByPrimaryKeySelective(DueNoticeRate dueNoticeRate);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     * @param: record
     * @return: 编号
     */
    int updateByPrimaryKey(DueNoticeRate dueNoticeRate);
}
