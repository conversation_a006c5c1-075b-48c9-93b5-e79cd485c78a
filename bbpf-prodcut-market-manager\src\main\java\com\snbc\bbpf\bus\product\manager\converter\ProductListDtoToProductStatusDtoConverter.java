package com.snbc.bbpf.bus.product.manager.converter;

import com.snbc.bbpf.bus.product.market.common.dto.product.ProductListDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductStatusDto;
import com.snbc.bbpf.commons.objs.IConvert;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * ProductListDto 到 ProductStatusDto 的转换器
 */
@Mapper
public interface ProductListDtoToProductStatusDtoConverter extends IConvert<ProductListDto, ProductStatusDto> {
    ProductListDtoToProductStatusDtoConverter INSTANCE = Mappers.getMapper(ProductListDtoToProductStatusDtoConverter.class);
}