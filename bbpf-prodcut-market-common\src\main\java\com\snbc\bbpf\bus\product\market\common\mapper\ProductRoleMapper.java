package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.ProductRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: LiangJB
 * 功能描述: <br>
 * 产品角色数据表操作
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface ProductRoleMapper {

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 根据订单号获取产品与角色对应关系
     * @date: 2020/8/12 13:21
     */
    ProductRole selectByPurchaseNo(@Param("purchaseNo") String purchaseNo);

}
