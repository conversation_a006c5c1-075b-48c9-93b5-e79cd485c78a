package com.snbc.bbpf.bus.product.manager;

import com.snbc.bbpf.component.gray.config.CustomLoadBalancerConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 服启动入口
 */
@SpringBootApplication
@EnableCaching
@EnableFeignClients(basePackages = { "com.snbc.component.*", "com.snbc.bbpf.*" })
@ComponentScan(basePackages = { "com.snbc.component.filestorage.*", "com.snbc.bbpf.bus.product.manager",
		"com.snbc.component.gray" })
@MapperScan(basePackages = "com.snbc.bbpf.bus.product.market.common.*")
@LoadBalancerClients(defaultConfiguration = CustomLoadBalancerConfiguration.class)
public class App {
	/**
	 * wjc
	 * main方法
	 * 
	 * @param args 参数
	 */
	public static void main(String[] args) {
		SpringApplication.run(App.class, args);
	}
}
