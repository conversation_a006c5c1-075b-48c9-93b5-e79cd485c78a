package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/*
* @author: liangJB
     * 功能描述: <br>
     * 基础订单列表
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
vemsId	String	是	售货机Id
vemsName	String	是	售货机名称

gradeDiscount	int	是	规格（天/台）

price	Decimal	是	单价
purchaseAmount	Decimal	是	总金额(元)
discountAmount	Decimal	是	优惠金额(元)
paymentAmount	Decimal	是	实付金额(元)

dueTime	Date	是	到期时间
* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductLOrderDto {

    private String vemsId;


    private String vemsName;



    private Integer gradeDiscount;

    private BigDecimal price;


    private BigDecimal purchaseAmount;

    private BigDecimal discountAmount;

    private BigDecimal paymentAmount;

    private Integer quantity;



    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueTime	;
}
