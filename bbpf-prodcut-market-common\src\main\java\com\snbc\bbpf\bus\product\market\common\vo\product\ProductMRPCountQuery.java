/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.market.common.vo.product;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR> LiangJb
 * @ClassName : ProductCountQuery
 * @Description : 服务统计月报查询入参
shopName	查询条件	String	商户名称	否	1983
startTime	查询条件	string	统计开始月份	否	2020-01
endTime	查询条件	string	统计结束月份	否	2020-02


 * @Date: 2020-08-12 15:51
 */



@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T06:49:02.983Z")
@Data
public class ProductMRPCountQuery {
    
    private String shopName;

    
    @JsonFormat(pattern = "yyyy-MM",timezone = "GTM+8")
    private String startTime;
    
    @JsonFormat(pattern = "yyyy-MM",timezone = "GTM+8")
    private String endTime;
}
