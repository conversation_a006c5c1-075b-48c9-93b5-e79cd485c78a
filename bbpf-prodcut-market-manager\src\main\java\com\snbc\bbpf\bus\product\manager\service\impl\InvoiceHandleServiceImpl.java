package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.InvoiceHandleService;
import com.snbc.bbpf.bus.product.manager.converter.ApplyInvoiceParamToInvoiceApplyConverter;
import com.snbc.bbpf.bus.product.manager.converter.ApplyInvoiceParamToInvoiceTitleConverter;
import com.snbc.bbpf.bus.product.market.common.entity.InvoiceApply;
import com.snbc.bbpf.bus.product.market.common.entity.InvoiceDelivery;
import com.snbc.bbpf.bus.product.market.common.entity.InvoiceTitle;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceApplyMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceDeliveryMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceTitleMapper;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ApplyInvoiceParam;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: InvoiceDeliveryServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/10/27 11:06
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/27 11:06
 */
@Service
public class InvoiceHandleServiceImpl implements InvoiceHandleService {

    @Autowired
    private InvoiceDeliveryMapper invoiceDeliveryMapper;
    @Autowired
    private InvoiceApplyMapper invoiceApplyMapper;
    @Autowired
    private InvoiceTitleMapper invoiceTitleMapper;


    private static final int THREEHUNDRED = 300;
    private static final int TEN = 10;

    @Override
    public String invoiceDeliveryProcess(ApplyInvoiceParam applyInvoiceParam) throws BusinessException {
        //纸质发票必填手机号、邮寄人、地址
        if (applyInvoiceParam.getInvoiceCarrier() == Constant.ONE_NUM
                && StringUtils.isBlank(applyInvoiceParam.getReceiverAddress())) {
            throw new BusinessException(Errors.DELIVERYNOTNULL.getMessage(),
                    Errors.DELIVERYNOTNULL.getCode(), null);
        }
        // 电子发票不需要邮寄信息，直接返回空字符串
        if (applyInvoiceParam.getInvoiceCarrier() != Constant.ONE_NUM) {
            return "";
        }
        InvoiceDelivery invoiceDelivery = new InvoiceDelivery();
        String invoiceDeliveryId = UUID.randomUUID().toString();
        invoiceDelivery.setDeliveryId(invoiceDeliveryId);
        invoiceDelivery.setReceiverName(applyInvoiceParam.getReceiverName());
        invoiceDelivery.setAddress(applyInvoiceParam.getReceiverAddress());
        invoiceDelivery.setTel(applyInvoiceParam.getReceiverTel());
        invoiceDelivery.setTenantId(applyInvoiceParam.getTenantId());
        invoiceDeliveryMapper.insertSelective(invoiceDelivery);
        return invoiceDeliveryId;
    }

    @Override
    public String addInvoice(ApplyInvoiceParam applyInvoiceParam, String invoiceDeliveryId,
                             List<BigDecimal> payAmountList) throws BusinessException {
        //页面参数转换为数据库交互的发票申请bean
        InvoiceApply invoiceApply = ApplyInvoiceParamToInvoiceApplyConverter.INSTANCE.to(applyInvoiceParam);
		invoiceApply.setBankName(applyInvoiceParam.getBankName());
		invoiceApply.setBankNo(applyInvoiceParam.getBankNo());
		invoiceApply.setTaxRegisterNo(applyInvoiceParam.getTaxRegisterNo());
		invoiceApply.setRegisterAddress(applyInvoiceParam.getRegisterAddress());
		invoiceApply.setRegisterPhonenum(applyInvoiceParam.getRegisterPhonenum());
        createInvoiceApply(applyInvoiceParam, invoiceApply, invoiceDeliveryId, payAmountList);
        //新增发票申请记录
        invoiceApplyMapper.insertInvoiceRecord(invoiceApply);
        return invoiceApply.getInvoiceApplyId();
    }

    /***
      * @Description:    生成发票对象
      * @Author:         wangsong
      * @param :         applyInvoiceParam
      * @param :         invoiceApply
      * @param :         invoiceDeliveryId
      * @param :         payAmountList
      * @CreateDate:     2020/11/4 13:29
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/11/4 13:29
      * @return :        InvoiceApply
     */
    private void createInvoiceApply(ApplyInvoiceParam applyInvoiceParam,InvoiceApply invoiceApply,String invoiceDeliveryId,List<BigDecimal> payAmountList)
            throws BusinessException {
        //生成发票id
        String invoiceId = UUID.randomUUID().toString();
        invoiceApply.setInvoiceApplyId(invoiceId);
        //申请时间
        invoiceApply.setApplyTime(LocalDateTime.now());
        //发票状态为已申请
        invoiceApply.setInvoiceStatus(Constant.ONE_NUM);
        //快递id
        invoiceApply.setInvoiceDeliveryId(invoiceDeliveryId);
        //计算开票金额
        BigDecimal invoiceAmount = BigDecimal.valueOf(0.00);
        for (BigDecimal amount : payAmountList) {
            invoiceAmount = invoiceAmount.add(amount);
        }
        //纸质发票金额不能小于300
        if (applyInvoiceParam.getInvoiceCarrier().equals(Constant.ONE_NUM) &&
                BigDecimal.valueOf(THREEHUNDRED).compareTo(invoiceAmount) == Constant.ONE_NUM) {
            throw new BusinessException(Errors.AMOUNTERROR.getMessage(), Errors.AMOUNTERROR.getCode(), null);
        }
        invoiceApply.setInvoiceAmount(invoiceAmount);
        //生成十位随机数
        String random = RandomStringUtils.random(TEN, false, true);
        //发票编号
        invoiceApply.setInvoiceCode("KP"+random);
    }
    /***
     * @Description: 发票抬头处理
     * @Author: wangsong
     * @param :         applyInvoiceParam
     * @CreateDate: 2020/10/12 15:32
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/10/12 15:32
     * @return :        void
     */
    @Override
    public void invoiceTitleProcess(ApplyInvoiceParam applyInvoiceParam) {
        //根据税务证号查询发票抬头是否存在
        int invoiceTitleCount = invoiceTitleMapper.selectInvoiceByTenantId(applyInvoiceParam.getTenantId());
        //发票抬头不存在，新增发票抬头信息，方便商户下次开票
        InvoiceTitle invoiceTitle = ApplyInvoiceParamToInvoiceTitleConverter.INSTANCE.to(applyInvoiceParam);
        if (invoiceTitleCount == 0) {
            //页面参数转换为数据库交互的发票抬头bean
            invoiceTitle.setInvoiceTitleId(UUID.randomUUID().toString());
            //新增发票抬头
            invoiceTitleMapper.insertInvoiceTitle(invoiceTitle);
        }
        //是否更新发票抬头信息
        if (null != applyInvoiceParam.getIsUpdateInvoiceTit() &&
                applyInvoiceParam.getIsUpdateInvoiceTit()) {
            invoiceTitleMapper.updateInvoiceTit(invoiceTitle);
        }
    }
}
