package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: PermissionLevelDto
 * @Description: 产品服务权限id
 * @module: SI-bbpf-product-manage
 * @Author: wangsong
 * @date: 2021/11/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PermissionLevelDto {
    private String permissionId;
    private List<String> childIds;
}
