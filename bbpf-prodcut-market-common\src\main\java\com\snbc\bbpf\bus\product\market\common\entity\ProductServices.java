package com.snbc.bbpf.bus.product.market.common.entity;

import java.time.LocalDateTime;

/**
 * 发布服务
 */
public class ProductServices {
    /**
     * 编号
     */
    private String productId;
    /**
     * 服务编号
     */
    private String productCode;
    /**
     * 服务名称
     */
    private String productName;
    /**
     * 服务类目
     */
    private Integer productCategory;
    /**
     * 服务类型
     */
    private Integer productType;
    /**
     * 服务状态
     */
    private Integer productStatus;
    /**
     * 收费类型
     */
    private Integer chargeType;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
    /**
     * 项目简介
     */
    private String productBrief;
    /**
     * 项目详情
     */
    private String productDetail;
    /**
     * 项目图片
     */
    private String productImage;
    /**
     * 服务条款
     */
    private String serviceRule;
    /**
     * 售后条款
     */
    private String aftersaleRule;
    /**
     * 退款规则
     */
    private String refundRule;
    /**
     * 使用手册
     */
    private String tutorial;
    /**
     * 服务入口
     */
    private String productEntrance;
    /**
     * 优惠金额
     */
    private Double priceDiscount;
    /**
     * 试用期
     */
    private Integer probation;

    public Double getPriceDiscount() {
        return priceDiscount;
    }

    public void setPriceDiscount(Double priceDiscount) {
        this.priceDiscount = priceDiscount;
    }

    public Integer getProbation() {
        return probation;
    }

    public void setProbation(Integer probation) {
        this.probation = probation;
    }

    public String getProductId() {
        return productId;
    }
    /**
     * 设置productId
     * @param productId
     */
    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode == null ? null : productCode.trim();
    }

    public String getProductName() {
        return productName;
    }
    /**
     * 设置productName
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public Integer getProductCategory() {
        return productCategory;
    }
    /**
     * 设置productCategory
     * @param productCategory
     */
    public void setProductCategory(Integer productCategory) {
        this.productCategory = productCategory;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public Integer getProductStatus() {
        return productStatus;
    }
    /**
     * 设置productStatus
     * @param productStatus
     */
    public void setProductStatus(Integer productStatus) {
        this.productStatus = productStatus;
    }

    public Integer getChargeType() {
        return chargeType;
    }

    public void setChargeType(Integer chargeType) {
        this.chargeType = chargeType;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    /**
     * 设置updateTime
     * @param updateTime
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getPublishTime() {
        return publishTime;
    }
    /**
     * 设置publishTime
     * @param publishTime
     */
    public void setPublishTime(LocalDateTime publishTime) {
        this.publishTime = publishTime;
    }

    public String getProductBrief() {
        return productBrief;
    }
    /**
     * 设置productBrief
     * @param productBrief
     */
    public void setProductBrief(String productBrief) {
        this.productBrief = productBrief == null ? null : productBrief.trim();
    }

    public String getProductDetail() {
        return productDetail;
    }
    /**
     * 设置productDetail
     * @param productDetail
     */
    public void setProductDetail(String productDetail) {
        this.productDetail = productDetail == null ? null : productDetail.trim();
    }

    public String getProductImage() {
        return productImage;
    }
    /**
     * 设置productImage
     * @param productImage
     */
    public void setProductImage(String productImage) {
        this.productImage = productImage == null ? null : productImage.trim();
    }

    public String getServiceRule() {
        return serviceRule;
    }
    /**
     * 设置serviceRule
     * @param serviceRule
     */
    public void setServiceRule(String serviceRule) {
        this.serviceRule = serviceRule == null ? null : serviceRule.trim();
    }

    public String getAftersaleRule() {
        return aftersaleRule;
    }
    /**
     * 设置aftersaleRule
     * @param aftersaleRule
     */
    public void setAftersaleRule(String aftersaleRule) {
        this.aftersaleRule = aftersaleRule == null ? null : aftersaleRule.trim();
    }

    public String getRefundRule() {
        return refundRule;
    }
    /**
     * 设置refundRule
     * @param refundRule
     */
    public void setRefundRule(String refundRule) {
        this.refundRule = refundRule == null ? null : refundRule.trim();
    }

    public String getTutorial() {
        return tutorial;
    }
    /**
     * 设置tutorial
     * @param tutorial
     */
    public void setTutorial(String tutorial) {
        this.tutorial = tutorial == null ? null : tutorial.trim();
    }

    public String getProductEntrance() {
        return productEntrance;
    }
    /**
     * 设置productEntrance
     * @param productEntrance
     */
    public void setProductEntrance(String productEntrance) {
        this.productEntrance = productEntrance == null ? null : productEntrance.trim();
    }
}
