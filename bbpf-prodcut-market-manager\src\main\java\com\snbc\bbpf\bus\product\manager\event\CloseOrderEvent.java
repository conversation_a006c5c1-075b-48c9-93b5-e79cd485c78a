/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.event;

import org.springframework.context.ApplicationEvent;

/**
 * @ClassName: CloseOrderEvent
 * 异步关闭订单
 * @module: si-bbpf-product-market
 * @Author: wjc1
 * @date: 2023/6/8 15:23
 */
public class CloseOrderEvent extends ApplicationEvent {
    // 流水号
    private final String payTrackNo;
    private final String payType;
    public CloseOrderEvent(Object source,String payTrackNo,String payType) {
        super(source);
        this.payTrackNo=payTrackNo;
        this.payType=payType;
    }

    public String getPayTrackNo() {
        return payTrackNo;
    }

    public String getPayType() {
        return payType;
    }
}
