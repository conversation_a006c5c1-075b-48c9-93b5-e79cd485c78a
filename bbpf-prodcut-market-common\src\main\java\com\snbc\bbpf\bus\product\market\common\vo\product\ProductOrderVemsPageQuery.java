/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.market.common.vo.product;

import com.snbc.bbpf.bus.product.market.common.Constant;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR> LiangJb
 * @ClassName : ProductOrderPageQuery
 * @Description : 服务订单查询入参
purchaseNo	查询条件	String	订单号	否	1111


 * pageNum	查询条件	string	页数	是	1
 * pageSize	查询条件	string	每页行数	是	10
 * @Date: 2020-08-12 15:51
 */



@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T06:49:02.983Z")
@Data
public class ProductOrderVemsPageQuery {

    private Integer pageSize= Constant.TEN;

    private Integer pageNum=Constant.ONE;


    private String purchaseNo;
}
