package com.snbc.bbpf.bus.product.market.common.dto.dict;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: DictProductDto
 * @Description: 产品服务列表所需字典信息
 * @module: SI-bbpf-product-manage
 * @Author: wangsong
 * @date: 2021/11/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DictProductDto {
    private List<DictValueDto> productCategorys;
    private List<DictValueDto> chargeTypes;
    private List<DictValueDto> productStatus;
}
