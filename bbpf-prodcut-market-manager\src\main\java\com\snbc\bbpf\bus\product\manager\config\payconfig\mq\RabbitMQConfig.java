/**
 * 版权所有 2009-2012山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.product.manager.config.payconfig.mq;

import com.snbc.bbpf.bus.product.manager.config.Constant;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: RabbitMQConfig
 * @Description: MQ 配置
 * <AUTHOR> newbeiyang.com
 * @date 2019年12月16日 下午2:24:46
 * @version V1.0
 */
@Configuration
public class RabbitMQConfig {
	public static final int INITIAL_CAPACITY = 2;

	/**
	 * 声明死信队列交换
	 * @return DirectExchange
	 */
	@Bean
	public DirectExchange dlxExchange() {
		return new DirectExchange(Constant.PRODUCT_PAY_DEAD_EXCHANGE);
	}

	/**
	 * 声明死信队列
	 * @return Queue
	 */
	@Bean
	public Queue dlxQueue() {
		return new Queue(Constant.PRODUCT_PAY_DEAD);
	}

	/**
	 * 绑定死信队列到死信交换机
	 * @return Binding
	 */
	@Bean
	public Binding binding() {
		return BindingBuilder.bind(dlxQueue())
				.to(dlxExchange())
				.with(Constant.DEAD_LETTER_PAY_KEY);
	}

	/**
	 * 声明订单业务交换机
	 * @return DirectExchange
	 */
	@Bean
	public DirectExchange usageExchange() {
		return new DirectExchange(Constant.PRODUCT_PAY_EXCHANGE);
	}


	@Bean
	public Queue queue() {
		Map<String,Object> arguments = new HashMap<>(INITIAL_CAPACITY);
		// 绑定该队列到私信交换机
		arguments.put("x-dead-letter-exchange",Constant.PRODUCT_PAY_DEAD_EXCHANGE);
		arguments.put("x-dead-letter-routing-key",Constant.DEAD_LETTER_PAY_KEY);
		return new Queue(Constant.PRODUCT_PAY,true,false,false,arguments);
	}
	/**
	 * 绑定订单队列到订单交换机
	 * @return Binding
	 */
	@Bean
	public Binding orderBinding() {
		return BindingBuilder.bind(queue())
				.to(usageExchange())
				.with(Constant.PAY_ROUTING_KEY);

	}

}
