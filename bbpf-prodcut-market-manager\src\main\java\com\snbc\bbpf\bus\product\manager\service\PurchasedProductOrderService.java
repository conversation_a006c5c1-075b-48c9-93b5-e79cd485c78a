package com.snbc.bbpf.bus.product.manager.service;

import com.github.pagehelper.PageInfo;

/**
 * @ProjectName: product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: PurchasedProductsOrder
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 已订购产品服务
 * @Author: wangsong
 * @CreateDate: 2021/2/25 18:24
 * @UpdateUser: wangsong
 * @UpdateDate: 2021/2/25 18:24
 */
public interface PurchasedProductOrderService {
    PageInfo<Object> purchasedOrderList(String tenantId, String productId, Integer pageNum, Integer pageSize);

    Object productRemainingStatis(String tenantId, String productId);
}
