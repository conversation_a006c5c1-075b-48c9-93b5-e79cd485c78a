package com.snbc.bbpf.bus.product.manager.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailExDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductSimpleDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ServicePDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ServicePPDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductPageQuery;
/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: ProductOrderExService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 负责产品查询先关的接口
 * @Author: LiangJb
 * @CreateDate: 2020/9/28 15:59
 */
public interface ProductService {

     /**
     * @author: LiangJB
     * 功能描述: <br>
     * 按日期、名字、类型、支付方式负责对现有列表的综合查询
     * @date: 2020/8/12 13:21
     * @param: productPageQuery
     * @return: PageInfo<ProductDto>
     */
    PageInfo<ProductDto> seviceListWithPage(ProductPageQuery productPageQuery);

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * //按日期、名字、类型、支付方式负责对现有列表的综合查询
     * @date: 2020/8/12 13:21
     * @param: productPageQuery
     * @return: PageInfo<ServicePDto>
     */
    PageInfo<ServicePDto> sevicesListWithPage(ProductPageQuery productPageQuery);

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * //按日期、名字、类型、支付方式负责对现有列表的综合查询
     * @date: 2020/8/12 13:21
     * @param: productPageQuery
     * @return: PageInfo<ServicePPDto>
     */
    PageInfo<ServicePPDto> sevicesOrderListWithPage(ProductPageQuery productPageQuery);

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * //根据具体ID，获取可编辑的简单服务信息
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: ProductSimpleDto
     */
    ProductSimpleDto seviceDetail(String productId) throws BusinessException;

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * //根据具体ID，获取可编辑的简单服务信息
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: ProductDetailDto
     */
    ProductDetailDto seviceStatusDetail(String productId);

    /**
     * @author: wjc1
     * 功能描述: <br>
     * //根据具体ID，获取服务详细信息
     * 根据tenantId 来查询是否有购买记录，如有则没有试用权限
     * @date: 2021/12/09 13:21
     * @param: tenantId
     * @param: productId
     * @return: ProductDetailExDto
     */
    ProductDetailExDto seviceTypeDetail(String productId,String tenantId)throws BusinessException;

    void delProductService(String productId) throws BusinessException;
    /**
     * @author: liangJB
     * 功能描述: <br>
     * 查询线下的离线数据
     * @date: 2020/8/12 13:26
     * @param: productId
     * @return:  ProductDetailExDto
     */
    String getOfflineDetail();
}
