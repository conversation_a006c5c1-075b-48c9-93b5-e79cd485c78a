package com.snbc.bbpf.bus.product.manager.service;


import com.snbc.bbpf.bus.product.market.common.entity.DictType;

import java.util.List;
import java.util.Map;


public interface DictTypeService {
    /**
     * 添加系统字典类型
     *
     * @param dictType
     * @return
     * @throws Exception
     */
    int insertDictType(DictType dictType);

    /**
     * 修改系统字典类型信息
     *
     * @param dictType
     * @return
     * @throws Exception
     */
    int updateDictType(DictType dictType);

    /**
     * 删除系统字典类型信息
     *
     * @param dictTypeCode
     * @return
     * @throws Exception
     */
    int deleteDictType(String dictTypeCode);

    /**
     * 根据字典类型ID查询基本信息
     *
     * @param dictTypeCode
     * @return
     * @throws Exception
     */
    DictType selectByPrimary(String dictTypeCode);

    /***
     * 获取所有类型信息的type_code值
     * @return
     */
    List<DictType> quertAllDictTypeCode();

    /**
     * 根据查询条件获取dictType列表
     *
     * @param map
     * @return
     */
    List<DictType> getDictTypeByMap(Map<String, Object> map);

}
