package com.snbc.bbpf.bus.product.manager.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.web.client.RestTemplate;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020-10-14 9:51
 */
@Configuration
public class RedisTemplateConfig {

    /**
     * 链接工厂
     */
    @Autowired
    private RedisConnectionFactory factory;

    /**
     * 初始化RedisTemplate
     * 设置序列化方式
     * @return
     */
    @Bean
    public RedisTemplate redisTemplate() {
        RedisTemplate redisTemplate = new RedisTemplate();
        //key 的序列化方式StringRedisSerializer
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        //value 的序列化方式StringRedisSerializer
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Object.class));
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new StringRedisSerializer());
        //设置链接工厂
        redisTemplate.setConnectionFactory(factory);
        return redisTemplate;
    }
    @Bean
    RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        return container;
    }
    @Bean({"sdkRestTemplate"})
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
