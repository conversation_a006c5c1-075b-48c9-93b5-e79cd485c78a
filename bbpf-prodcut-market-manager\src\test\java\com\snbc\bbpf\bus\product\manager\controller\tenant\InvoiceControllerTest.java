package com.snbc.bbpf.bus.product.manager.controller.tenant;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.manager.resp.invoice.InvoceExpressDeliResp;
import com.snbc.bbpf.bus.product.manager.resp.invoice.InvoiceCommonResp;
import com.snbc.bbpf.bus.product.manager.resp.invoice.InvoiceDetailResp;
import com.snbc.bbpf.bus.product.manager.resp.invoice.InvoicePurchasePageResp;
import com.snbc.bbpf.bus.product.manager.resp.invoice.RRQInvoiceListPage;
import com.snbc.bbpf.bus.product.manager.service.InvoiceDeliveryService;
import com.snbc.bbpf.bus.product.manager.service.InvoiceService;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoceExpressDeliDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceListRRQDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoicePurchaseDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceTitleDto;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ApplyInvoiceParam;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.InvoiceParamQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InvoiceController单元测试类
 */
@DisplayName("发票控制器测试")
class InvoiceControllerTest {

    @Mock
    private InvoiceService invoiceService;

    @Mock
    private InvoiceDeliveryService invoiceDeliveryService;

    @InjectMocks
    private InvoiceController invoiceController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(invoiceController).build();
    }

    @Test
    @DisplayName("查询发票列表 - 成功")
    void testInvoiceListWithPage_Success() throws IOException {
        // 准备测试数据
        InvoiceParamQuery invoiceParamQuery = new InvoiceParamQuery();
        invoiceParamQuery.setPageNum(1);
        invoiceParamQuery.setPageSize(10);
        invoiceParamQuery.setInvoiceStatus("APPROVED");
        String tenantId = "TENANT_001";
        
        List<InvoiceListRRQDto> invoiceList = new ArrayList<>();
        InvoiceListRRQDto invoice1 = new InvoiceListRRQDto();
        invoice1.setInvoiceApplyId("INV_001");
        invoice1.setInvoiceAmount("1000.00");
        invoice1.setInvoiceStatus("APPROVED");
        invoice1.setApplyTime(LocalDateTime.now());
        invoiceList.add(invoice1);
        
        InvoiceListRRQDto invoice2 = new InvoiceListRRQDto();
        invoice2.setInvoiceApplyId("INV_002");
        invoice2.setInvoiceAmount("2000.00");
        invoice2.setInvoiceStatus("PENDING");
        invoice2.setApplyTime(LocalDateTime.now());
        invoiceList.add(invoice2);
        
        PageInfo<InvoiceListRRQDto> pageInfo = new PageInfo<>(invoiceList);
        pageInfo.setTotal(2);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
        
        // Mock服务方法
        when(invoiceService.invoiceListWithPageForRRQ(any(InvoiceParamQuery.class))).thenReturn(pageInfo);

        // 执行测试
        RRQInvoiceListPage result = invoiceController.invoiceListWithPage(invoiceParamQuery, tenantId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        PageInfo<InvoiceListRRQDto> resultBody = result.getBody();
        assertEquals(2, resultBody.getTotal());
        assertEquals(2, resultBody.getList().size());
        assertEquals("INV_001", resultBody.getList().get(0).getInvoiceApplyId());
        assertEquals("1000.00", resultBody.getList().get(0).getInvoiceAmount());
        assertEquals("APPROVED", resultBody.getList().get(0).getInvoiceStatus());
        
        // 验证租户ID被正确设置
        assertEquals(tenantId, invoiceParamQuery.getTenantId());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).invoiceListWithPageForRRQ(any(InvoiceParamQuery.class));
    }

    @Test
    @DisplayName("查询发票列表 - 空结果")
    void testInvoiceListWithPage_EmptyResult() throws IOException {
        // 准备测试数据
        InvoiceParamQuery invoiceParamQuery = new InvoiceParamQuery();
        invoiceParamQuery.setPageNum(1);
        invoiceParamQuery.setPageSize(10);
        String tenantId = "TENANT_002";
        
        PageInfo<InvoiceListRRQDto> emptyPageInfo = new PageInfo<>(new ArrayList<>());
        emptyPageInfo.setTotal(0);
        
        // Mock服务方法
        when(invoiceService.invoiceListWithPageForRRQ(any(InvoiceParamQuery.class))).thenReturn(emptyPageInfo);

        // 执行测试
        RRQInvoiceListPage result = invoiceController.invoiceListWithPage(invoiceParamQuery, tenantId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        PageInfo<InvoiceListRRQDto> resultBody = result.getBody();
        assertEquals(0, resultBody.getTotal());
        assertTrue(resultBody.getList().isEmpty());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).invoiceListWithPageForRRQ(any(InvoiceParamQuery.class));
    }

    @Test
    @DisplayName("发票详情查询 - 成功")
    void testInvoiceDetail_Success() throws Exception {
        // 准备测试数据
        String invoiceApplyId = "INV_DETAIL_001";
        
        InvoiceDetailDto invoiceDetail = new InvoiceDetailDto();
        // invoiceDetail.setInvoiceApplyId(invoiceApplyId); // InvoiceDetailDto没有invoiceApplyId字段
        invoiceDetail.setInvoiceAmount("1500.00");
        invoiceDetail.setInvoiceType("ELECTRONIC");
        invoiceDetail.setInvoiceTitle("测试公司");
        invoiceDetail.setTaxRegisterNo("91110000********9X");
        invoiceDetail.setInvoiceStatus("ISSUED");
        invoiceDetail.setApplyTime(new Date());
        invoiceDetail.setInvoiceTime(new Date());
        
        // Mock服务方法
        when(invoiceService.invoiceDetail(invoiceApplyId)).thenReturn(invoiceDetail);

        // 执行测试
        InvoiceDetailResp result = invoiceController.invoiceDetail(invoiceApplyId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        InvoiceDetailDto resultBody = result.getBody();
        // assertEquals(invoiceApplyId, resultBody.getInvoiceApplyId()); // InvoiceDetailDto没有invoiceApplyId字段
        assertEquals("1500.00", resultBody.getInvoiceAmount());
        assertEquals("ELECTRONIC", resultBody.getInvoiceType());
        assertEquals("测试公司", resultBody.getInvoiceTitle());
        assertEquals("91110000********9X", resultBody.getTaxRegisterNo());
        assertEquals("ISSUED", resultBody.getInvoiceStatus());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).invoiceDetail(invoiceApplyId);
    }

    @Test
    @DisplayName("发票详情查询 - 发票不存在")
    void testInvoiceDetail_NotFound() throws Exception {
        // 准备测试数据
        String invoiceApplyId = "NON_EXIST_INV";
        
        // Mock服务方法返回null
        when(invoiceService.invoiceDetail(invoiceApplyId)).thenReturn(null);

        // 执行测试
        InvoiceDetailResp result = invoiceController.invoiceDetail(invoiceApplyId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertEquals("000000", result.getHead().getCode());
        assertNull(result.getBody());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).invoiceDetail(invoiceApplyId);
    }

    @Test
    @DisplayName("发票订单信息查询 - 成功")
    void testInvoicePurchaseListWithPage_Success() {
        // 准备测试数据
        String invoiceApplyId = "INV_001";
        String tenantId = "TENANT_001";
        String userId = "USER_001";
        String sortField = "createTime";
        String sequence = "DESC";
        Integer pageSize = 10;
        Integer pageNum = 1;
        
        List<InvoicePurchaseDto> purchaseList = new ArrayList<>();
        InvoicePurchaseDto purchase1 = new InvoicePurchaseDto();
        purchase1.setProductPurchaseId("ORDER_001");
        purchase1.setProductName("测试产品1");
        purchase1.setPurchaseAmount(500.00);
        purchase1.setPurchaseTime(LocalDateTime.now());
        purchaseList.add(purchase1);
        
        InvoicePurchaseDto purchase2 = new InvoicePurchaseDto();
        purchase2.setProductPurchaseId("ORDER_002");
        purchase2.setProductName("测试产品2");
        purchase2.setPurchaseAmount(1000.00);
        purchase2.setPurchaseTime(LocalDateTime.now());
        purchaseList.add(purchase2);
        
        PageInfo<InvoicePurchaseDto> pageInfo = new PageInfo<>(purchaseList);
        pageInfo.setTotal(2);
        
        // Mock服务方法
        when(invoiceService.invoicePurchaseListWithPage(invoiceApplyId, pageSize, pageNum, tenantId, userId, sortField, sequence))
                .thenReturn(pageInfo);

        // 执行测试
        InvoicePurchasePageResp result = invoiceController.invoicePurchaseListWithPage(
                invoiceApplyId, tenantId, sortField, sequence, userId, pageSize, pageNum);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        PageInfo<InvoicePurchaseDto> resultBody = result.getBody();
        assertEquals(2, resultBody.getTotal());
        assertEquals(2, resultBody.getList().size());
        assertEquals("ORDER_001", resultBody.getList().get(0).getProductPurchaseId());
        assertEquals("测试产品1", resultBody.getList().get(0).getProductName());
        assertEquals(500.00, resultBody.getList().get(0).getPurchaseAmount());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).invoicePurchaseListWithPage(
                invoiceApplyId, pageSize, pageNum, tenantId, userId, sortField, sequence);
    }

    @Test
    @DisplayName("发票申请 - 成功")
    void testApplyInvoice_Success() throws Exception {
        // 准备测试数据
        ApplyInvoiceParam applyInvoiceParam = new ApplyInvoiceParam();
        applyInvoiceParam.setTenantId("TENANT_001");
        // applyInvoiceParam.setUserId("USER_001"); // ApplyInvoiceParam没有userId字段
        applyInvoiceParam.setInvoiceAmount(new BigDecimal("2000.00"));
        applyInvoiceParam.setInvoiceType(1); // 1：增值税普通发票
        applyInvoiceParam.setInvoiceTitle("申请测试公司");
        applyInvoiceParam.setTaxRegisterNo("91110000987654321Y");
        applyInvoiceParam.setReceiverTel("13800138000");
        applyInvoiceParam.setReceiverAddress("北京市朝阳区测试街道123号");
        
        // Mock服务方法 - void方法不需要返回值
        doNothing().when(invoiceService).applyInvoice(applyInvoiceParam);

        // 执行测试
        InvoiceCommonResp result = invoiceController.applyInvoice(applyInvoiceParam);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertEquals("000000", result.getHead().getCode());
        assertEquals("操作成功", result.getHead().getMessage());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).applyInvoice(applyInvoiceParam);
    }

    @Test
    @DisplayName("查询发票抬头 - 成功")
    void testQueryInvoiceTitle_Success() throws IOException {
        // 准备测试数据
        String tenantId = "TENANT_001";
        
        InvoiceTitleDto invoiceTitle = new InvoiceTitleDto();
        invoiceTitle.setTenantId(tenantId);
        invoiceTitle.setCompanyName("查询测试公司");
        invoiceTitle.setTaxRegisterNo("91110000111111111A");
        invoiceTitle.setRegisterAddress("北京市海淀区中关村大街1号");
        invoiceTitle.setRegisterPhonenum("010-********");
        invoiceTitle.setBankName("中国银行");
        invoiceTitle.setBankNo("********90********9");
        
        // Mock服务方法
        when(invoiceService.getInvoiceTitle(tenantId)).thenReturn(invoiceTitle);

        // 执行测试
        CommonResp<InvoiceTitleDto> result = invoiceController.queryinvoiceTitle(tenantId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        InvoiceTitleDto resultBody = result.getBody();
        assertEquals(tenantId, resultBody.getTenantId());
        assertEquals("查询测试公司", resultBody.getCompanyName());
        assertEquals("91110000111111111A", resultBody.getTaxRegisterNo());
        assertEquals("北京市海淀区中关村大街1号", resultBody.getRegisterAddress());
        assertEquals("010-********", resultBody.getRegisterPhonenum());
        assertEquals("中国银行", resultBody.getBankName());
        assertEquals("********90********9", resultBody.getBankNo());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).getInvoiceTitle(tenantId);
    }

    @Test
    @DisplayName("查询发票抬头 - 抬头不存在")
    void testQueryInvoiceTitle_NotFound() throws IOException {
        // 准备测试数据
        String tenantId = "NON_EXIST_TENANT";
        
        // Mock服务方法返回null
        when(invoiceService.getInvoiceTitle(tenantId)).thenReturn(null);

        // 执行测试
        CommonResp<InvoiceTitleDto> result = invoiceController.queryinvoiceTitle(tenantId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertEquals("000000", result.getHead().getCode());
        assertNull(result.getBody());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).getInvoiceTitle(tenantId);
    }

    @Test
    @DisplayName("查看发票物流信息 - 成功")
    void testLogisticsDetail_Success() throws IOException {
        // 准备测试数据
        String invoiceApplyId = "INV_LOGISTICS_001";
        
        InvoceExpressDeliDto logisticsInfo = new InvoceExpressDeliDto();
        logisticsInfo.setExpressCompany("顺丰速运");
        logisticsInfo.setExpressNo("SF********90");
        
        // Mock服务方法
        when(invoiceDeliveryService.logisticsDetail(invoiceApplyId)).thenReturn(logisticsInfo);

        // 执行测试
        InvoceExpressDeliResp result = invoiceController.logisticsDetail(invoiceApplyId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        InvoceExpressDeliDto resultBody = result.getBody();
        assertEquals("顺丰速运", resultBody.getExpressCompany());
        assertEquals("SF********90", resultBody.getExpressNo());
        
        // 验证Mock调用
        verify(invoiceDeliveryService, times(1)).logisticsDetail(invoiceApplyId);
    }

    @Test
    @DisplayName("查看发票物流信息 - 物流信息不存在")
    void testLogisticsDetail_NotFound() throws IOException {
        // 准备测试数据
        String invoiceApplyId = "NON_EXIST_LOGISTICS";
        
        // Mock服务方法返回null
        when(invoiceDeliveryService.logisticsDetail(invoiceApplyId)).thenReturn(null);

        // 执行测试
        InvoceExpressDeliResp result = invoiceController.logisticsDetail(invoiceApplyId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertEquals("000000", result.getHead().getCode());
        assertNull(result.getBody());
        
        // 验证Mock调用
        verify(invoiceDeliveryService, times(1)).logisticsDetail(invoiceApplyId);
    }

    @Test
    @DisplayName("发票状态过滤测试")
    void testInvoiceStatusFilter() throws IOException {
        // 准备测试数据 - 测试不同状态的发票
        InvoiceParamQuery invoiceParamQuery = new InvoiceParamQuery();
        invoiceParamQuery.setPageNum(1);
        invoiceParamQuery.setPageSize(10);
        invoiceParamQuery.setInvoiceStatus("PENDING");
        String tenantId = "TENANT_001";
        
        List<InvoiceListRRQDto> pendingInvoices = new ArrayList<>();
        InvoiceListRRQDto pendingInvoice = new InvoiceListRRQDto();
        pendingInvoice.setInvoiceApplyId("PENDING_INV_001");
        pendingInvoice.setInvoiceStatus("PENDING");
        pendingInvoice.setInvoiceAmount("800.00");
        pendingInvoices.add(pendingInvoice);
        
        PageInfo<InvoiceListRRQDto> pageInfo = new PageInfo<>(pendingInvoices);
        
        // Mock服务方法
        when(invoiceService.invoiceListWithPageForRRQ(any(InvoiceParamQuery.class))).thenReturn(pageInfo);

        // 执行测试
        RRQInvoiceListPage result = invoiceController.invoiceListWithPage(invoiceParamQuery, tenantId);
        
        // 验证结果
        assertNotNull(result);
        PageInfo<InvoiceListRRQDto> resultBody = result.getBody();
        assertEquals(1, resultBody.getList().size());
        assertEquals("PENDING", resultBody.getList().get(0).getInvoiceStatus());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).invoiceListWithPageForRRQ(any(InvoiceParamQuery.class));
    }

    @Test
    @DisplayName("分页参数边界值测试")
    void testPaginationBoundaryValues() {
        // 准备测试数据 - 测试边界值
        String invoiceApplyId = "INV_001";
        String tenantId = "TENANT_001";
        String userId = "USER_001";
        Integer pageSize = 1; // 最小页面大小
        Integer pageNum = 1000; // 大页码
        
        PageInfo<InvoicePurchaseDto> pageInfo = new PageInfo<>(new ArrayList<>());
        
        // Mock服务方法
        when(invoiceService.invoicePurchaseListWithPage(anyString(), anyInt(), anyInt(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(pageInfo);

        // 执行测试
        InvoicePurchasePageResp result = invoiceController.invoicePurchaseListWithPage(
                invoiceApplyId, tenantId, null, null, userId, pageSize, pageNum);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("000000", result.getHead().getCode());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).invoicePurchaseListWithPage(
                invoiceApplyId, pageSize, pageNum, tenantId, userId, null, null);
    }

    @Test
    @DisplayName("租户隔离验证测试")
    void testTenantIsolation() throws IOException {
        // 准备测试数据 - 不同租户
        String tenantId1 = "TENANT_001";
        String tenantId2 = "TENANT_002";
        
        InvoiceTitleDto tenant1Title = new InvoiceTitleDto();
        tenant1Title.setTenantId(tenantId1);
        tenant1Title.setCompanyName("租户1公司");
        
        InvoiceTitleDto tenant2Title = new InvoiceTitleDto();
        tenant2Title.setTenantId(tenantId2);
        tenant2Title.setCompanyName("租户2公司");
        
        // Mock服务方法 - 根据租户返回不同结果
        when(invoiceService.getInvoiceTitle(tenantId1)).thenReturn(tenant1Title);
        when(invoiceService.getInvoiceTitle(tenantId2)).thenReturn(tenant2Title);

        // 执行测试 - 租户1
        CommonResp<InvoiceTitleDto> result1 = invoiceController.queryinvoiceTitle(tenantId1);
        InvoiceTitleDto resultBody1 = result1.getBody();
        assertEquals(tenantId1, resultBody1.getTenantId());
        assertEquals("租户1公司", resultBody1.getCompanyName());
        
        // 执行测试 - 租户2
        CommonResp<InvoiceTitleDto> result2 = invoiceController.queryinvoiceTitle(tenantId2);
        InvoiceTitleDto resultBody2 = result2.getBody();
        assertEquals(tenantId2, resultBody2.getTenantId());
        assertEquals("租户2公司", resultBody2.getCompanyName());
        
        // 验证Mock调用
        verify(invoiceService, times(1)).getInvoiceTitle(tenantId1);
        verify(invoiceService, times(1)).getInvoiceTitle(tenantId2);
    }
}