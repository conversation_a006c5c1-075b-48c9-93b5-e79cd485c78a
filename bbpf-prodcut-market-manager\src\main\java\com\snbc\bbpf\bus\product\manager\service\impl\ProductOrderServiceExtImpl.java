/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.market.common.dto.dict.DictValueDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderListDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceBuyPDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceShopOrderDto;
import com.snbc.bbpf.bus.product.market.common.mapper.DictValueMapper;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductShopVemsPageQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName: ProductOrderServiceExtImpl
 * @module: si-bbpf-product-market
 * @Author: wjc1
 * @date: 2023/6/29 13:24
 */
@Service
public class ProductOrderServiceExtImpl {
    private static final int ZERO = 0;
    private static final int ONE = 1;
    private static final double ONEF = 1.0;
    private static final double ONEB = 100.0;
    private static final int TWO = 2;
    @Autowired
    private DictValueMapper dictValueMapper;

    /**
     * 字典值转换
     * @param productList
     * @return
     */
    public List<ServiceShopOrderDto> convertRrqServiceOrderListResult(Collection<ProductOrderListDto> productList){
        Map<String, String> dictValueMap = new HashMap<>();
        if (!productList.isEmpty()) {
            List<DictValueDto> dictValueDtoList = dictValueMapper.getMultipleDictValues(
                    Arrays.asList("purchase_status", "product_type", "charge_type", "pay_type", "grade_unit"));
            dictValueMap = dictValueDtoList.stream().collect(Collectors.toMap(dictValue ->
                    dictValue.getTypeCode() + dictValue.getValueCode(), DictValueDto::getValueName));
        }
        List<ServiceShopOrderDto> productVoList = new ArrayList<>();
        for (ProductOrderListDto productOrder : productList) {
            productVoList.add(ServiceShopOrderDto.builder().purchaseNo(productOrder.getPurchaseNo())
                    .productGrade(productOrder.getProductGrade()).productName(productOrder.getProductName())
                    .productImage(productOrder.getProductImage()).paymentAmount(productOrder.getPaymentAmount())
                    .productQuantity(productOrder.getProductQuantity()).purchaseStatus(productOrder.getPurchaseStatus())
                    .purchaseAmount(productOrder.getPurchaseAmount()).discountAmount(productOrder.getDiscountAmount())
                    .payTypeName(dictValueMap.get("pay_type" + productOrder.getPayType()))
                    .productgradeUnit(dictValueMap.get("grade_unit" + productOrder.getProductGrade()))
                    .purchaseStatusName(dictValueMap.get("purchase_status" + productOrder.getPurchaseStatus()))
                    .chargeTypeName(dictValueMap.get("charge_type" + productOrder.getChargeTypeName()))
                    .purchaseTime(productOrder.getPurchaseTime())
                    .payTime(productOrder.getPayTime())
                    .productTypeName(dictValueMap.get("product_type" + productOrder.getProductTypeName())).build());
        }
        return productVoList;
    }
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * //转换具体值的百分比
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: VemsProductOrderDto
     */
    public void convertServiceBuyPDto(ServiceBuyPDto item, LocalDateTime date) {
        //服务状态赋值
        if (item.getServiceStatus() == null || item.getServiceStatus() < ZERO) {
            item.setServiceStatus(ZERO);
        }
        if(item.getDueTime()==null){
            item.setServicePercent(ZERO);
            return;
        }
        //计算时间
        if (date.isBefore(item.getDueTime())) {
            Duration duration = Duration.between(item.getActivationTime(), item.getDueTime());
            double allDays = duration.toDays() + ONEF;
            duration = Duration.between(item.getActivationTime(), date);
            double pasDays = duration.toDays() + ONEF;
            double servicePercent = Math.round(pasDays / allDays * ONEB);
            item.setServicePercent(servicePercent);
            //再重新对结果赋值
            item.setServiceStatus(item.getServiceStatus() + ONE);
        } else {
            item.setServicePercent(ONEB);
        }

    }

    /**
     * @Description: 时间处理
     * @Author: LiangJB
     * @param :         productOrderVemsPageQuery
     * @param :         df
     * @param :         date
     * @CreateDate: 2020/10/13 13:35
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/10/13 13:35
     */
    public void convertTime(ProductShopVemsPageQuery vemsPageQuery, DateTimeFormatter df, LocalDateTime date) {
        //日期为2的时候表示过期时间，只要服务过期日期小于当前时间
        if (vemsPageQuery.getDateType() == TWO) {
            vemsPageQuery.setStartTime("");
            vemsPageQuery.setEndTime(df.format(date));
        } else {
            vemsPageQuery.setStartTime(df.format(date));
            vemsPageQuery.setEndTime(df.format(date.plusMonths(1)));
        }
    }

}
