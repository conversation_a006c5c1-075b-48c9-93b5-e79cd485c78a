package com.snbc.bbpf.bus.product.market.common.dto.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/*
* @author: liangJB
     * 功能描述: <br>
     * 月报统计
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
tenantName	String	是	商户名称
reportMonth	string	是	月份
purchaseTimes	int	是	购买次数
totalFee	Decimal	是	总金额(元)
discountFee	Decimal	是	优惠金额(元)
actualFee	Decimal	是	实付金额(元)
* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopMRPCountDto {

    private String tenantName;


    private String reportMonth;


    private Integer purchaseTimes;


    private BigDecimal totalFee;

    private BigDecimal discountFee;

    private BigDecimal actualFee;


}
