package com.snbc.bbpf.bus.product.manager.controller.pay;

import com.alibaba.fastjson.JSONObject;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.config.payconfig.request.AliQRCodeRequest;
import com.snbc.bbpf.bus.product.manager.config.payconfig.request.RefundRequest;
import com.snbc.bbpf.bus.product.manager.config.payconfig.request.WechatQRCodeRequest;
import com.snbc.bbpf.bus.product.manager.config.payconfig.resp.QRCodeResp;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.ProductPayService;
import com.snbc.bbpf.bus.product.manager.service.ProductPurchaseService;
import com.snbc.bbpf.bus.product.manager.service.PurchasePayTrackService;
import com.snbc.bbpf.bus.product.manager.utils.QRCodeTools;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;
import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import com.snbc.pay.entity.response.TradeCloseResponse;
import com.snbc.pay.entity.response.TradeRefundResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 支付统一rest接口
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/pay")
public class PayController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PayController.class);

    @Autowired
    private ProductPayService productPayService;
    @Autowired
    private PurchasePayTrackService purchasePayTrackService;
    @Autowired
    private ProductPurchaseService productPurchaseService;
    @Autowired
    private RedisTemplate redisTemplate;
    public static final int ZERO = 0;
    /**
     * 功能描述: <br>
     * 〈获取二维码〉
     *
     * @throws
     * @param: null
     * @return:
     * @author: gs
     * @date: 2019-08-07 16:54
     */
    
    @RequestMapping(value = "/getAliPayQRCode", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, method = RequestMethod.POST)
    public QRCodeResp getAliPayQRCode(@RequestBody AliQRCodeRequest aliQRCodeRequest) throws BusinessException{
        LOGGER.debug("Obtain alipay qr code interface input parameters：{}", JSONObject.toJSONString(aliQRCodeRequest));
        QRCodeResp qrCodeResp = new QRCodeResp();
        CallResponse result = new CallResponse();
        String covertQRCode = "";
        try {
            //先查看订单状态是否为已支付 再查流水是否在缓存中存在 不存在
            ProductPurchase pp = checkPurchase(aliQRCodeRequest.getOutTradeNo());
            //不用前台传过来的应付金额
            BigDecimal payAmount = pp.getProductPrice().multiply(BigDecimal.valueOf(pp.getProductQuantity())).subtract(pp.getDiscountAmount());
            LOGGER.info("getAliPayQRCode payAmount :{}",payAmount);
            aliQRCodeRequest.setTotalAmount(String.valueOf(payAmount));
            List<PurchasePayTrack> trackList = getTrackList(aliQRCodeRequest.getOutTradeNo(),Constant.ALIPAY);
            if(!CollectionUtils.isEmpty(trackList)){

                PurchasePayTrack oldTrack = trackList.get(ZERO);
                String redisKey = Constant.ALIPAY + "_" + oldTrack.getPayTrackNo();
                String qrCode = String.valueOf(redisTemplate.opsForValue().get(redisKey));
                if (qrCode!=null && !"".equals(qrCode) && !"null".equals(qrCode)) {
                    covertQRCode = qrCode;
                } else {
                    //作废旧流水 生成新流水方缓存
                    // 调支付宝/微信 作废接口 根据支付渠道分别创建流水号
                    String newTrack = purchasePayTrackService.cancelOldAndNew(oldTrack,Constant.ALIPAY);
                    aliQRCodeRequest.setOutTradeNo(newTrack);
                    covertQRCode = getCovertQRCode(aliQRCodeRequest);
                }
            }else{
                String newTrack = purchasePayTrackService.createNewTrack(aliQRCodeRequest.getOutTradeNo(),Constant.ALIPAY);
                aliQRCodeRequest.setOutTradeNo(newTrack);
                covertQRCode = getCovertQRCode(aliQRCodeRequest);
            }
            if(StringUtils.isNotBlank(covertQRCode)){
                result.setCode(Errors.SUCCESS.getCode());
                result.setMessage(Errors.SUCCESS.getMessage());
            }else{
                throw  new BusinessException(Errors.PARAMERROR.getMessage(),Errors.PARAMERROR.getCode());
            }
            qrCodeResp.setQrCodeUrl(covertQRCode);
        }catch (BusinessException e ){
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            LOGGER.error("Failed to obtain alipay QR code,parameters：{}", JSONObject.toJSONString(aliQRCodeRequest), e);
        }catch (Exception e) {
            result.setCode(Errors.PARAMERROR.getCode());
            result.setMessage(Errors.PARAMERROR.getMessage());
            LOGGER.error("Failed to obtain alipay QR code,parameters：{}", JSONObject.toJSONString(aliQRCodeRequest), e);
        }
        qrCodeResp.setHead(result);
        LOGGER.info("Obtain alipay qr code interface input：{}", JSONObject.toJSONString(qrCodeResp));
        return qrCodeResp;
    }
    private List<PurchasePayTrack> getTrackList(String outTradeNo,String payType){
        PurchasePayTrack payTrack = new PurchasePayTrack();
        payTrack.setPurchaseNo(outTradeNo);
        payTrack.setPayStatus(Constant.PAY_TRACK_WAIT);
        payTrack.setPayType(payType);
        return purchasePayTrackService.selectPayTracks(payTrack);
    }

    /***
      * @Description:    校验订单
      * @Author:         wangsong
      * @param :         outTradeNo
      * @CreateDate:     2022/2/9 10:30
      * @UpdateDate:     2022/2/9 10:30
      * @return :        com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private ProductPurchase checkPurchase(String outTradeNo)throws Exception {
        ProductPurchase purchase = productPurchaseService.selectByPurchaseNo(outTradeNo);
        if(null == purchase){
            throw  new BusinessException(Errors.NOPURCHASEDATA.getMessage(),Errors.NOPURCHASEDATA.getCode());
        }else if(Constant.PURCHASE_STATUS_WAITPAY != purchase.getPurchaseStatus()){
            throw  new BusinessException(Errors.PURCHASESTATUSERROR.getMessage(),Errors.PURCHASESTATUSERROR.getCode());
        }else{
            return purchase;
        }
    }
    private String getCovertQRCode(AliQRCodeRequest aliQRCodeRequest)throws Exception{
        String qrCodeUrl = productPayService.getAlipayQRCode(aliQRCodeRequest);
        String covertQRCode = QRCodeTools.creatRrCode(qrCodeUrl, Constant.QRCODE_WIDTH, Constant.QRCODE_HEIGHT);
        long expireTime = Long.parseLong(aliQRCodeRequest.getTimeExpress());
        redisTemplate.opsForValue().set(Constant.ALIPAY + "_" + aliQRCodeRequest.getOutTradeNo(), covertQRCode, expireTime, TimeUnit.MINUTES);
        return covertQRCode;
    }

    
    @RequestMapping(value = "/refreshAliPayQRCode", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, method = RequestMethod.POST)
    public QRCodeResp refreshAliPayQRCode(@RequestBody AliQRCodeRequest aliQRCodeRequest) throws BusinessException {
        LOGGER.info("Refresh the entry parameters of alipay qr code interface：{}", JSONObject.toJSONString(aliQRCodeRequest));
        QRCodeResp qrCodeResp = new QRCodeResp();
        CallResponse result = new CallResponse();
        String covertQRCode = "";
        try {

            ProductPurchase pp = checkPurchase(aliQRCodeRequest.getOutTradeNo());
            BigDecimal payAmount = pp.getProductPrice().multiply(BigDecimal.valueOf(pp.getProductQuantity())).subtract(pp.getDiscountAmount());
            LOGGER.debug("refreshAliPayQRCode payAmount :{}",payAmount);
            aliQRCodeRequest.setTotalAmount(String.valueOf(payAmount));
            List<PurchasePayTrack> trackList = getTrackList(aliQRCodeRequest.getOutTradeNo(),Constant.ALIPAY);
            if(!CollectionUtils.isEmpty(trackList)) {
                PurchasePayTrack oldTrack = trackList.get(0);
                redisTemplate.delete(Constant.ALIPAY + "_" + oldTrack.getPayTrackNo());
                //作废旧流水 生成新流水方缓存
                String newTrack = purchasePayTrackService.cancelOldAndNew(oldTrack,Constant.ALIPAY);
                aliQRCodeRequest.setOutTradeNo(newTrack);
                covertQRCode = getCovertQRCode(aliQRCodeRequest);
            }else{
                throw  new BusinessException(Errors.NOPAYTRACKDATA.getMessage(),Errors.NOPAYTRACKDATA.getCode());
            }
            if(StringUtils.isNotBlank(covertQRCode)){
                result.setCode(Errors.SUCCESS.getCode());
                result.setMessage(Errors.SUCCESS.getMessage());
            }else{
                throw  new BusinessException(Errors.PARAMERROR.getMessage(),Errors.PARAMERROR.getCode());
            }
            qrCodeResp.setQrCodeUrl(covertQRCode);
        }catch (BusinessException e ){
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            LOGGER.error("Failed to refresh the qr code interface of Alipay. Procedure,parameter:{}",
                    JSONObject.toJSONString(aliQRCodeRequest), e);
        } catch (Exception e) {
            result.setCode(Errors.PARAMERROR.getCode());
            result.setMessage(Errors.PARAMERROR.getMessage());
            LOGGER.error("Failed to refresh the qr code interface of Alipay. Procedure,parameter:{}",
                    JSONObject.toJSONString(aliQRCodeRequest), e);
        }
        qrCodeResp.setHead(result);
        LOGGER.debug("Refresh alipay qr code interface outgoing parameters：{}", JSONObject.toJSONString(qrCodeResp));
        return qrCodeResp;
    }
        /**
         * 功能描述: <br>
         * 〈获取二维码〉
         *
         * @throws
         * @param: null
         * @return:
         * @author: gs
         * @date: 2019-08-07 16:54
         */
    
    @RequestMapping(value = "/getWechatQrcodeUrl", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, method = RequestMethod.POST)
    public QRCodeResp getWechatQrcodeUrl(@RequestBody WechatQRCodeRequest wechatQRCodeRequest) throws NoSuchAlgorithmException, BusinessException {
        LOGGER.info("Obtain wechat QR code interface input parameters：{}",
                JSONObject.toJSONString(wechatQRCodeRequest));
        QRCodeResp qrCodeResp = new QRCodeResp();
        CallResponse result = new CallResponse();
        String covertQRCode = "";
        try {
            //先查看订单状态是否为已支付 再查流水是否在缓存中存在 不存在
            ProductPurchase pp = checkPurchase(wechatQRCodeRequest.getOutTradeNo());
            BigDecimal payAmount = pp.getProductPrice().multiply(BigDecimal.valueOf(pp.getProductQuantity())).subtract(pp.getDiscountAmount());
            LOGGER.info("getWechatQrcodeUrl payAmount :{}",payAmount);
            wechatQRCodeRequest.setAmount(payAmount);
            List<PurchasePayTrack> trackList = getTrackList(wechatQRCodeRequest.getOutTradeNo(),Constant.WECHATPAY);
            if(!CollectionUtils.isEmpty(trackList)){
                PurchasePayTrack oldTrack = trackList.get(0);
                String redisKey = Constant.WECHATPAY + "_" + oldTrack.getPayTrackNo();
                String qrCode = String.valueOf(redisTemplate.opsForValue().get(redisKey));
                if (qrCode!=null && !"".equals(qrCode) && !"null".equals(qrCode)) {
                    covertQRCode = qrCode;
                } else {
                    //作废旧流水 生成新流水方缓存
                    String newTrack = purchasePayTrackService.cancelOldAndNew(oldTrack,Constant.WECHATPAY);
                    wechatQRCodeRequest.setOutTradeNo(newTrack);
                    covertQRCode = getWeChatCovertQRCode(wechatQRCodeRequest);
                }
            }else{
                String newTrack = purchasePayTrackService.createNewTrack(wechatQRCodeRequest.getOutTradeNo(),Constant.WECHATPAY);
                wechatQRCodeRequest.setOutTradeNo(newTrack);
                covertQRCode = getWeChatCovertQRCode(wechatQRCodeRequest);
            }
            if(StringUtils.isNotBlank(covertQRCode)){
                result.setCode(Errors.SUCCESS.getCode());
                result.setMessage(Errors.SUCCESS.getMessage());
            }else{
                throw  new BusinessException(Errors.PARAMERROR.getMessage(),Errors.PARAMERROR.getCode());
            }
            qrCodeResp.setQrCodeUrl(covertQRCode);
        } catch (Exception e) {
            LOGGER.error("Error obtaining wechat QR code,parameter:{}"
                    ,JSONObject.toJSONString(wechatQRCodeRequest), e);
            result.setMessage(Errors.PARAMERROR.getMessage());
            result.setCode(Errors.PARAMERROR.getCode());
        }
        qrCodeResp.setHead(result);
        LOGGER.info("Obtain wechat QR code interface outgoing parameters：{}", JSONObject.toJSONString(qrCodeResp));
        return qrCodeResp;
    }

    
    @RequestMapping(value = "/refreshWechatQrcodeUrl", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, method = RequestMethod.POST)
    public QRCodeResp refreshWechatQrcodeUrl(@RequestBody WechatQRCodeRequest wechatQRCodeRequest) throws NoSuchAlgorithmException, BusinessException {
        LOGGER.info("Refresh the entry parameters of wechat QR code interface：{}"
                , JSONObject.toJSONString(wechatQRCodeRequest));
        QRCodeResp qrCodeResp = new QRCodeResp();
        CallResponse result = new CallResponse();
        String covertQRCode = "";
        try {
            ProductPurchase pp = checkPurchase(wechatQRCodeRequest.getOutTradeNo());
            BigDecimal payAmount = pp.getProductPrice().multiply(BigDecimal.valueOf(pp.getProductQuantity())).subtract(pp.getDiscountAmount());
            LOGGER.info("refreshWechatQrcodeUrl payAmount :{}",payAmount);
            wechatQRCodeRequest.setAmount(payAmount);
            List<PurchasePayTrack> trackList = getTrackList(wechatQRCodeRequest.getOutTradeNo(),Constant.WECHATPAY);
            if(!CollectionUtils.isEmpty(trackList)) {
                PurchasePayTrack oldTrack = trackList.get(0);
                //删除旧redis
                redisTemplate.delete(Constant.WECHATPAY + "_" + oldTrack.getPayTrackNo());
                //作废旧流水 生成新流水方缓存
                String newTrack = purchasePayTrackService.cancelOldAndNew(oldTrack,Constant.WECHATPAY);
                wechatQRCodeRequest.setOutTradeNo(newTrack);
                covertQRCode = getWeChatCovertQRCode(wechatQRCodeRequest);
            }else{
                throw  new BusinessException(Errors.NOPAYTRACKDATA.getMessage(),Errors.NOPAYTRACKDATA.getCode());
            }
            if(StringUtils.isNotBlank(covertQRCode)){
                result.setCode(Errors.SUCCESS.getCode());
                result.setMessage(Errors.SUCCESS.getMessage());
            }else{
                throw  new BusinessException(Errors.PARAMERROR.getMessage(),Errors.PARAMERROR.getCode());
            }
            qrCodeResp.setQrCodeUrl(covertQRCode);
        }catch (Exception e) {
            result.setCode(Errors.PARAMERROR.getCode());
            result.setMessage(Errors.PARAMERROR.getMessage());
            LOGGER.error("Failed to refresh the wechat QR code interface. Procedure,parameter:{}",
                    JSONObject.toJSONString(wechatQRCodeRequest), e);
        }
        qrCodeResp.setHead(result);
        LOGGER.info("Refresh wechat QR code interface outgoing parameters：{}",
                JSONObject.toJSONString(qrCodeResp));
        return qrCodeResp;
    }

    private String getWeChatCovertQRCode(WechatQRCodeRequest wechatQRCodeRequest)throws Exception{
        String covertQRCode = "";
        long expireTime = Long.parseLong(wechatQRCodeRequest.getTimeoutExpress());
        String qrCodeUrl = productPayService.getWechatQRCode(wechatQRCodeRequest);
        covertQRCode = QRCodeTools.creatRrCode(qrCodeUrl, Constant.QRCODE_WIDTH, Constant.QRCODE_HEIGHT);
        redisTemplate.opsForValue().set(Constant.WECHATPAY + "_" + wechatQRCodeRequest.getOutTradeNo(), covertQRCode, expireTime, TimeUnit.MINUTES);

        return covertQRCode;
    }
    /**
     * 功能描述: <br>
     * 〈退款〉
     * @author: MR.LI
     * @date: 2019-08-07 16:54
     */
    
    @RequestMapping(value = "/refundWechatOrder", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, method = RequestMethod.POST)
    public TradeRefundResponse refundWechatOrder(@RequestBody RefundRequest tradeRefundRequest) throws Exception {
        LOGGER.info("The wechat refund module is entered:{}", JSONObject.toJSONString(tradeRefundRequest));
        return productPayService.refundWechatOrder(tradeRefundRequest);
    }

    /**
     * 功能描述: <br>
     * 〈关闭阿里订单〉
     *
     * @throws
     * @param: null
     * @return:
     * @author: MR.LI
     * @date: 2019-08-07 16:54
     */
    
    @RequestMapping(value = "/closeWechatOrder", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, method = RequestMethod.POST)
    public TradeCloseResponse closeWechatOrder(String orderNum) throws Exception {
        LOGGER.info("Wechat closes order entry：{}", JSONObject.toJSONString(orderNum));
        return productPayService.closeWechatOrder(orderNum);
    }
    /**
     * 功能描述: <br>
     * 〈退款〉支付宝订单退款
     *
     * @throws
     * @param: null
     * @return:
     * @author: MR.LI
     * @date: 2019-08-07 16:54
     */
    
    @RequestMapping(value = "/refundAliOrder", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, method = RequestMethod.POST)
    public TradeRefundResponse refundAliOrder(@RequestBody RefundRequest tradeRefundRequest) throws Exception {
        LOGGER.info("Alipay refund order entry：{}", JSONObject.toJSONString(tradeRefundRequest));
        return productPayService.refundAliOrder(tradeRefundRequest);
    }

    /**
     * 功能描述: <br>
     * 〈关闭阿里订单〉
     *
     * @throws
     * @param: orderNum
     * @return: TradeCloseResponse
     * @author: MR.LI
     * @date: 2019-08-07 16:54
     */
    
    @RequestMapping(value = "/closeAliOrder", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, method = RequestMethod.POST)
    public TradeCloseResponse closeAliOrder(String orderNum) throws Exception {
        LOGGER.info("Alipay closed order entry：{}", JSONObject.toJSONString(orderNum));
        return productPayService.closeAliOrder(orderNum);
    }
}
