package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.service.PurchaseTrackService;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseTrackMapper;
import com.snbc.bbpf.bus.product.market.common.vo.BaseOrderVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * @ProjectName: product-manager
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: PurchaseTrackServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2021/3/17 13:44
 * @UpdateUser: wangsong
 * @UpdateDate: 2021/3/17 13:44
 */
@Service
public class PurchaseTrackServiceImpl implements PurchaseTrackService {
    @Autowired
    private PurchaseTrackMapper purchaseTrackMapper;
    private static final int SUCCESS = 1;

    /**
     * 生成订单跟踪日志
     *
     * @param orderVo
     * @param purchaseNo
     * @param purchaseStatus
     * @return
     */
    @Override
    public boolean savePurchaseTrack(BaseOrderVo orderVo, String purchaseNo, Integer purchaseStatus) {
        PurchaseTrack purchaseTrack = PurchaseTrack.builder()
                .purchaseTrackId(UUID.randomUUID().toString())
                .createTime(LocalDateTime.now())
                .userId(orderVo.getUserId())
                .tenantId(orderVo.getTenantId())
                .purchaseNo(purchaseNo)
                .paymentAmount(orderVo.getPaymentAmount())
                .discountAmount(orderVo.getDiscountAmount())
                .purchaseAmount(orderVo.getPurchaseAmount())
                .purchaseStatus(purchaseStatus)
                .purchaseTime(LocalDateTime.now()).build();
        int result = purchaseTrackMapper.insertSelective(purchaseTrack);
        return result == SUCCESS;
    }
}
