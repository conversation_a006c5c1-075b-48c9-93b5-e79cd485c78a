package com.snbc.bbpf.bus.product.manager.controller.pay;

import com.snbc.bbpf.bus.product.manager.service.ProductPayService;
import com.snbc.bbpf.bus.product.manager.service.ProductPayCallbackService;
import com.snbc.bbpf.bus.product.manager.service.ProductPurchaseService;
import com.snbc.bbpf.bus.product.manager.service.PurchasePayTrackService;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * NotifyController单元测试类
 */
@DisplayName("支付通知控制器测试")
class NotifyControllerTest {

    @Mock
    private ProductPayService productPayService;

    @Mock
    private ProductPayCallbackService productPayCallbackService;

    @Mock
    private ProductPurchaseService productPurchaseService;

    @Mock
    private PurchasePayTrackService purchasePayTrackService;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private ServletInputStream servletInputStream;

    @InjectMocks
    private NotifyController notifyController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(notifyController).build();
    }

    @Test
    @DisplayName("支付宝支付通知 - 成功")
    void testAliPayNotify_Success() throws Exception {
        // 准备测试数据
        String notifyData = "out_trade_no=TEST_ORDER_001&trade_status=TRADE_SUCCESS&total_amount=100.00";
        Map<String, String> params = new HashMap<>();
        params.put("out_trade_no", "TEST_ORDER_001");
        params.put("trade_status", "TRADE_SUCCESS");
        params.put("total_amount", "100.00");
        
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setProductPurchaseId("1");
        productPurchase.setPurchaseNo("TEST_ORDER_001");
        productPurchase.setPurchaseStatus(0); // 未支付状态
        productPurchase.setPurchaseAmount(new BigDecimal("100.00"));
        
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);

        // Mock HTTP请求和响应
        when(request.getInputStream()).thenReturn(servletInputStream);
        when(request.getParameterMap()).thenReturn(convertToParameterMap(params));
        when(response.getWriter()).thenReturn(printWriter);
        when(productPurchaseService.selectByPurchaseNo("TEST_ORDER_001")).thenReturn(productPurchase);
        // Mock ProductPayCallbackService methods
        doNothing().when(productPayCallbackService).aliNotify(any());

        // 模拟输入流
        ByteArrayInputStream inputStream = new ByteArrayInputStream(notifyData.getBytes());
        when(servletInputStream.read(any(byte[].class))).thenAnswer(invocation -> {
            byte[] buffer = invocation.getArgument(0);
            return inputStream.read(buffer);
        });

        // 验证服务层调用
        ProductPurchase result = productPurchaseService.selectByPurchaseNo("TEST_ORDER_001");
        assertNotNull(result);
        assertEquals("TEST_ORDER_001", result.getPurchaseNo());
        
        // Call aliNotify method
        productPayCallbackService.aliNotify(params);
    }

    @Test
    @DisplayName("支付宝支付通知 - 验签失败")
    void testAliPayNotify_VerifyFailed() throws Exception {
        // 准备测试数据
        Map<String, String> params = new HashMap<>();
        params.put("out_trade_no", "TEST_ORDER_002");
        params.put("trade_status", "TRADE_SUCCESS");
        params.put("total_amount", "100.00");
        
        // Mock failed verification
        doThrow(new RuntimeException("Verification failed")).when(productPayCallbackService).aliNotify(any());

        // Expect exception for failed verification
        assertThrows(RuntimeException.class, () -> {
            productPayCallbackService.aliNotify(params);
        });
        
        // 验证Mock调用
        verify(productPayCallbackService, times(1)).aliNotify(params);
    }

    @Test
    @DisplayName("微信支付通知 - 成功")
    void testWechatPayNotify_Success() throws Exception {
        // 准备测试数据
        String xmlData = "<xml><out_trade_no>WECHAT_ORDER_001</out_trade_no><result_code>SUCCESS</result_code><total_fee>10000</total_fee></xml>";
        
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setProductPurchaseId("1");
        productPurchase.setPurchaseNo("WECHAT_ORDER_001");
        productPurchase.setPurchaseStatus(0); // 未支付状态
        productPurchase.setPurchaseAmount(new BigDecimal("100.00"));
        
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);

        // Mock HTTP请求和响应
        when(request.getInputStream()).thenReturn(servletInputStream);
        when(response.getWriter()).thenReturn(printWriter);
        when(productPurchaseService.selectByPurchaseNo("WECHAT_ORDER_001")).thenReturn(productPurchase);
        // Mock ProductPayCallbackService for WeChat
        doNothing().when(productPayCallbackService).wxNotify(any());

        // 模拟输入流
        ByteArrayInputStream inputStream = new ByteArrayInputStream(xmlData.getBytes());
        when(servletInputStream.read(any(byte[].class))).thenAnswer(invocation -> {
            byte[] buffer = invocation.getArgument(0);
            return inputStream.read(buffer);
        });

        // 验证服务层调用
        ProductPurchase result = productPurchaseService.selectByPurchaseNo("WECHAT_ORDER_001");
        assertNotNull(result);
        assertEquals("WECHAT_ORDER_001", result.getPurchaseNo());
        
        // Call wxNotify method
        Map<String, String> wechatParams = new HashMap<>();
        wechatParams.put("out_trade_no", "WECHAT_ORDER_001");
        productPayCallbackService.wxNotify(wechatParams);
    }

    @Test
    @DisplayName("微信支付通知 - 订单不存在")
    void testWechatPayNotify_OrderNotFound() {
        // 准备测试数据
        String outTradeNo = "NON_EXIST_WECHAT_ORDER";
        
        // Mock服务方法返回null
        when(productPurchaseService.selectByPurchaseNo(outTradeNo)).thenReturn(null);

        // 执行测试
        ProductPurchase result = productPurchaseService.selectByPurchaseNo(outTradeNo);
        
        // 验证结果
        assertNull(result);
        
        // 验证Mock调用
        verify(productPurchaseService, times(1)).selectByPurchaseNo(outTradeNo);
    }

    @Test
    @DisplayName("支付通知 - 重复通知处理")
    void testPayNotify_DuplicateNotification() {
        // 准备测试数据
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setProductPurchaseId("1");
        productPurchase.setPurchaseNo("DUPLICATE_ORDER_001");
        productPurchase.setPurchaseStatus(1); // 已支付状态
        
        // Mock服务方法
        when(productPurchaseService.selectByPurchaseNo("DUPLICATE_ORDER_001")).thenReturn(productPurchase);

        // 执行测试
        ProductPurchase result = productPurchaseService.selectByPurchaseNo("DUPLICATE_ORDER_001");
        
        // 验证结果 - 订单已支付，应该直接返回成功
        assertNotNull(result);
        assertEquals(1, result.getPurchaseStatus());
        
        // 验证Mock调用
        verify(productPurchaseService, times(1)).selectByPurchaseNo("DUPLICATE_ORDER_001");
    }

    @Test
    @DisplayName("支付流水记录创建测试")
    void testCreatePaymentTrack() {
        // 准备测试数据
        PurchasePayTrack payTrack = new PurchasePayTrack();
        payTrack.setPayTrackNo("TRACK_ORDER_001");
        // payTrack.setPayAmount(new BigDecimal("100.00")); // PurchasePayTrack没有payAmount字段
        payTrack.setPayStatus(1);
        payTrack.setPayType("ALIPAY");
        payTrack.setPayTrackNo("2024061712345678");
        
        // Mock服务方法
        when(purchasePayTrackService.insert(any(PurchasePayTrack.class))).thenReturn(1);

        // 执行测试
        int result = purchasePayTrackService.insert(payTrack);
        
        // 验证结果
        assertEquals(1, result);
        
        // 验证Mock调用
        verify(purchasePayTrackService, times(1)).insert(any(PurchasePayTrack.class));
    }

    @Test
    @DisplayName("异常通知数据处理测试")
    void testInvalidNotificationData() throws IOException {
        // 准备异常数据
        String invalidData = "invalid_notification_data";
        
        // Mock输入流
        ByteArrayInputStream inputStream = new ByteArrayInputStream(invalidData.getBytes());
        when(request.getInputStream()).thenReturn(servletInputStream);
        when(servletInputStream.read(any(byte[].class))).thenAnswer(invocation -> {
            byte[] buffer = invocation.getArgument(0);
            return inputStream.read(buffer);
        });

        // 验证异常数据处理
        assertNotNull(invalidData);
        assertFalse(invalidData.contains("out_trade_no"));
    }

    @Test
    @DisplayName("HTTP响应写入测试")
    void testHttpResponseWriting() throws IOException {
        // 准备测试数据
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        
        // Mock响应
        when(response.getWriter()).thenReturn(printWriter);
        
        // 模拟写入响应
        PrintWriter writer = response.getWriter();
        writer.print("success");
        writer.flush();
        
        // 验证响应内容
        assertEquals("success", stringWriter.toString());
        
        // 验证Mock调用
        verify(response, times(1)).getWriter();
    }

    @Test
    @DisplayName("支付金额验证测试")
    void testPaymentAmountValidation() {
        // 准备测试数据
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseAmount(new BigDecimal("100.00"));
        productPurchase.setProductQuantity(1);
        productPurchase.setDiscountAmount(new BigDecimal("0.00"));
        // 设置产品单价
        productPurchase.setProductPrice(new BigDecimal("100.00"));
        
        // 计算预期金额
        BigDecimal expectedAmount = productPurchase.getProductPrice()
            .multiply(BigDecimal.valueOf(productPurchase.getProductQuantity()))
            .subtract(productPurchase.getDiscountAmount());
        
        // 通知中的金额
        BigDecimal notifyAmount = new BigDecimal("100.00");
        
        // 验证金额匹配
        assertEquals(0, expectedAmount.compareTo(notifyAmount));
    }

    @Test
    @DisplayName("并发通知处理测试")
    void testConcurrentNotificationHandling() throws Exception {
        // 准备测试数据
        String outTradeNo = "CONCURRENT_ORDER_001";
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setProductPurchaseId("1");
        productPurchase.setPurchaseNo(outTradeNo);
        productPurchase.setPurchaseStatus(0);
        
        // Mock服务方法
        when(productPurchaseService.selectByPurchaseNo(outTradeNo)).thenReturn(productPurchase);
        // Mock ProductPayCallbackService for concurrent test
        doNothing().when(productPayCallbackService).aliNotify(any());
        // Note: aliNotify throws Exception, but doNothing() handles it in mock

        // 模拟并发访问
        for (int i = 0; i < 5; i++) {
            ProductPurchase result = productPurchaseService.selectByPurchaseNo(outTradeNo);
            assertNotNull(result);
            assertEquals(outTradeNo, result.getPurchaseNo());
        }

        // 验证Mock调用次数
        verify(productPurchaseService, times(5)).selectByPurchaseNo(outTradeNo);
    }

    /**
     * 辅助方法：将Map转换为HttpServletRequest的ParameterMap格式
     */
    private Map<String, String[]> convertToParameterMap(Map<String, String> params) {
        Map<String, String[]> parameterMap = new HashMap<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            parameterMap.put(entry.getKey(), new String[]{entry.getValue()});
        }
        return parameterMap;
    }
}