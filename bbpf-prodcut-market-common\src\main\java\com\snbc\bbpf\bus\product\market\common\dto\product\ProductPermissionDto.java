/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: ProductPermissionDto
 * 权限信息,用于权限展示
 * @module: bbpf-bus-system
 * @Author:Liangjb
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductPermissionDto {
    
    private String permissionId;
    
    private String productId;
    
    private String id;
}
