package com.snbc.bbpf.bus.product.manager.converter;

import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import com.snbc.bbpf.bus.product.market.common.vo.PermissionVo;
import com.snbc.bbpf.commons.objs.IConvert;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * Permission 到 PermissionVo 的转换器
 */
@Mapper
public interface PermissionToPermissionVoConverter extends IConvert<Permission, PermissionVo> {
    PermissionToPermissionVoConverter INSTANCE = Mappers.getMapper(PermissionToPermissionVoConverter.class);
    
    @Mappings({
        @Mapping(source = "permissionDesc", target = "remarks"),
        @Mapping(source = "permissionImage", target = "permissionIcon"),
        @Mapping(source = "permissionId", target = "permissionId"),
        @Mapping(source = "permissionType", target = "permissionType"),
        @Mapping(source = "permissionName", target = "permissionName"),
        @Mapping(source = "permissionCode", target = "permissionCode"),
        @Mapping(source = "parentName", target = "parentName"),
        @Mapping(source = "parentId", target = "parentId"),
        @Mapping(source = "orderBy", target = "orderBy"),
        @Mapping(source = "permissionLevel", target = "permissionLevel"),
        @Mapping(source = "sysType", target = "sysType"),
        @Mapping(source = "hasEnable", target = "hasEnable"),
        @Mapping(source = "routingUrl", target = "routingUrl")
    })
    @Override
    PermissionVo to(Permission source);
    
    @Mappings({
        @Mapping(source = "remarks", target = "permissionDesc"),
        @Mapping(source = "permissionIcon", target = "permissionImage"),
        @Mapping(source = "permissionId", target = "permissionId"),
        @Mapping(source = "permissionType", target = "permissionType"),
        @Mapping(source = "permissionName", target = "permissionName"),
        @Mapping(source = "permissionCode", target = "permissionCode"),
        @Mapping(source = "parentName", target = "parentName"),
        @Mapping(source = "parentId", target = "parentId"),
        @Mapping(source = "orderBy", target = "orderBy"),
        @Mapping(source = "permissionLevel", target = "permissionLevel"),
        @Mapping(source = "sysType", target = "sysType"),
        @Mapping(source = "hasEnable", target = "hasEnable"),
        @Mapping(source = "routingUrl", target = "routingUrl")
    })
    @Override
    Permission from(PermissionVo source);
}