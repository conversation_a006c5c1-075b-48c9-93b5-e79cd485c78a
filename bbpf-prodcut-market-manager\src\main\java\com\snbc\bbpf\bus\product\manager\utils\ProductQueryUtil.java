package com.snbc.bbpf.bus.product.manager.utils;

import com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductShopVemsPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductCountPageQueryEx;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductCountQueryEx;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductOrderPageQueryEx;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductPageQueryEx;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductShopVemsPageQueryEx;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.utils
 * @ClassName: ProductQueryUtil
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 转换前端入参参数，主要将必要的字符串转成整型
 * @Author: LiangJb
 * @CreateDate: 2020/9/28 15:59
 */
public class ProductQueryUtil {
    public static final int ZERO = 0;
    public static final int ONE = 1;
    public static final int NINETYEIGHT = 98;
    public static final int TEN = 10;
    public static final int AONE = -1;
    public static final int TWENTY = 20;
    public static final int TWENTYFIVE= 25;
    public static final long NO_THIRTY= 30;
    public static final long NO_EIGHT_SIXM_FOURHK= 86_400_000;

    /**
     * 定义产品服务类型的单位
     */
    public static final String PRODUCT_CTYPE_VEMDAY = "按天/台收费";
    public static final String PRODUCT_CTYPE_VEMDAY_UNIT = "天/台";
    public static final String PRODUCT_CTYPE_DAY = "按天收费";
    public static final String PRODUCT_CTYPE_DAY_UNIT  = "Day";
    public static final String PRODUCT_CTYPE_NUMBER = "按次数收费";
    public static final String PRODUCT_CTYPE_NUMBER_UNIT = "次";
    public static final String PRODUCT_CTYPE_OTHER_UNIT = "M/月";
    public static final String PRODUCT_CTYPE_FIXED_CHARGES_UNIT = "按指定收费";
    /**
     * @author: LiangJB
     * 将字符串入参转换为标准入参
     *
     * @param query 原来参数
     * @return
     */
    public static ProductPageQuery convertProductPageQuery(ProductPageQueryEx query) {
        ProductPageQuery result = new ProductPageQuery();
        result.setPageNum(query.getPageNum());
        result.setPageSize(query.getPageSize());
        result.setTenantId(query.getTenantId());

        result.setEndTime(convertDateTime(query.getEndTime()));
        result.setStartTime(query.getStartTime());
        result.setProductName(query.getProductName());
        result.setProductOrder(query.getProductOrder());
        //判空转换
        result.setGradeTypeNo(convertStringInt(query.getGradeTypeNo(), ZERO));
        result.setProductType(convertStringInt(query.getProductType(), ZERO));
        return result;

    }
    /**
     * @author: LiangJB
     * 将字符串入参转换为标准入参
     *
     * @param strValue 原来参数
     * @return
     */
    public static String convertDateTime (String strValue) {
        String result=strValue;
        if(null!=strValue && !"".equals(strValue)){
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate time = LocalDate.parse(strValue,df);
            result = df.format(time.plusDays(ONE));
        }
        return result;
    }
    /**
     * @author: LiangJB
     * 给默认的参数转换值
     *
     * @param strValue 原来参数
     * @return
     */
    public static int convertStringInt(String strValue, int defaultValue) {
        int result = defaultValue;
        if (null != strValue && !"".equals(strValue)) {
            result = Integer.valueOf(strValue);
        }
        return result;
    }

    /**
     * @author: LiangJB
     * 将字符串入参转换为标准入参
     *
     * @param query 原来参数
     * @return
     */
    public static ProductCountQuery convertProductCountQuery(ProductCountQueryEx query) {
        ProductCountQuery result = new ProductCountQuery();
        result.setProductName(query.getProductName());
        //判空转换
        result.setProductCategory(convertStringInt(query.getProductCategory(), ZERO));
        result.setProductType(convertStringInt(query.getProductType(), ZERO));
        result.setChargeType(convertStringInt(query.getChargeType(), ZERO));
        return result;

    }

    /**
     * @author: LiangJB
     * 将字符串入参转换为标准入参
     *
     * @param query 原来参数
     * @return
     */
    public static ProductCountPageQuery convertProductCountPageQuery(ProductCountPageQueryEx query) {
        ProductCountPageQuery result = new ProductCountPageQuery();
        result.setProductName(query.getProductName());
        result.setPageNum(query.getPageNum());
        result.setPageSize(query.getPageSize());
        //判空转换
        result.setProductType(convertStringInt(query.getProductType(), ZERO));
        result.setProductCategory(convertStringInt(query.getProductCategory(), ZERO));
        result.setChargeType(convertStringInt(query.getChargeType(), ZERO));
        return result;

    }

    /**
     * @author: LiangJB
     * 将字符串入参转换为标准入参
     *
     * @param query 原来参数
     * @return
     */
    public static ProductOrderPageQuery convertProductOrderPageQuery(ProductOrderPageQueryEx query) {
        ProductOrderPageQuery result = new ProductOrderPageQuery();
        result.setProductName(query.getProductName());
        result.setPageNum(query.getPageNum());
        result.setPageSize(query.getPageSize());

        result.setProductId(query.getProductId());
        result.setShopName(query.getShopName());
        result.setTenantId(query.getTenantId());
        result.setPurchaseNo(query.getPurchaseNo());
        result.setStartTime(query.getStartTime());
        result.setEndTime(query.getEndTime());
        result.setStartOrderTime(query.getStartOrderTime());
        result.setEndOrderTime(query.getEndOrderTime());
        result.setSortRule(query.getSortRule());
        result.setSortField(query.getSortField());

        //判空转换
        result.setPurchaseStatus(convertStringInt(query.getPurchaseStatus(), AONE));
        result.setPayType(convertStringInt(query.getPayType(), AONE));
        result.setProductType(convertStringInt(query.getProductType(), ZERO));
        result.setChargeType(convertStringInt(query.getChargeType(), ZERO));
        return result;

    }

    /**
     * @author: LiangJB
     * 将字符串入参转换为标准入参
     *
     * @param query 原来参数
     * @return
     */
    public static ProductShopVemsPageQuery convertProductShopVemsPageQuery(ProductShopVemsPageQueryEx query) {
        ProductShopVemsPageQuery result = new ProductShopVemsPageQuery();
        result.setPageNum(query.getPageNum());
        result.setPageSize(query.getPageSize());

        result.setVemsName(query.getVemsName());
        result.setTenantId(query.getTenantId());
        result.setStartTime(query.getStartTime());
        result.setEndTime(convertDateTime(query.getEndTime()));
        result.setProductCode(query.getProductCode());
        //判空转换
        result.setDateType(convertStringInt(query.getDateType(), ONE));
        return result;
    }
    /**
     * @author: LiangJB
     * 根据收费方式，获取收费单位
     * wjc更新：除了 “按指定收费”返回空外， 其他类型都截图 按和收费之间的子。约定字典创建类型的时候需要遵循规则：按xx收费
     * @param strUnit 原来参数
     * @return
     */
    public static String getProductGradeUnit(String strUnit) {
        if (strUnit.equals(PRODUCT_CTYPE_FIXED_CHARGES_UNIT)) {
            return "";
        }
        if (strUnit.equals(PRODUCT_CTYPE_DAY)) {
            return PRODUCT_CTYPE_DAY_UNIT;
        }
        return strUnit.replace("按", "").replace("收费", "");
    }
}
