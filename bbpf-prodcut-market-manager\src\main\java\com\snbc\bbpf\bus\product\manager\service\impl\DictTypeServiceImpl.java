package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.service.DictTypeService;
import com.snbc.bbpf.bus.product.market.common.entity.DictType;
import com.snbc.bbpf.bus.product.market.common.mapper.DictTypeMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;


/**
 * 字典类型
 *
 * <AUTHOR>
 */
@Service
@Transactional
@CacheConfig(cacheNames = "dictType")
public class DictTypeServiceImpl implements DictTypeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DictTypeServiceImpl.class);

    @Autowired
    private DictTypeMapper dictTypeMapper;


    /**
     * 添加系统字典类型
     *
     * @param dictType
     * @return
     * @throws Exception
     */
    @Override
    @CacheEvict(allEntries = true)
    public int insertDictType(DictType dictType){
        int num = dictTypeMapper.insertSelective(dictType);
		LOGGER.info("Succeeded in adding the dictionary type. Procedure");
        return num;
    }

    /**
     * 修改系统字典类型信息
     *
     * @param dictType
     * @return
     * @throws Exception
     */
    @Override
    @CacheEvict(allEntries = true)
    public int updateDictType(DictType dictType){
        return dictTypeMapper.updateByPrimaryKeySelective(dictType);

    }

    /**
     * 删除系统字典类型信息
     *
     * @param dictTypeCode
     * @return
     * @throws Exception
     */
    @Override
    @CacheEvict(allEntries = true)
    public int deleteDictType(String dictTypeCode){
        return dictTypeMapper.deleteByPrimaryKey(dictTypeCode);
    }

    /**
     * 根据主键查询字典
     *
     * @param typeCode
     * @return
     * @throws Exception
     */
    @Override
    //@Cacheable(keyGenerator="cacheKeyGenerator")
    public DictType selectByPrimary(String typeCode){
        return dictTypeMapper.selectByPrimaryKey(typeCode);
    }

    /**
     * 查询所有字典类型编码
     *
     * @return
     * @throws Exception
     */
    @Override
    //@Cacheable(keyGenerator="cacheKeyGenerator")
    public List<DictType> quertAllDictTypeCode() {
        return dictTypeMapper.queryAllDictType();
    }

    /**
     * 根据条件查询字典类型
     *
     * @param map
     * @return
     */
    @Override
    public List<DictType> getDictTypeByMap(Map<String, Object> map) {
        return dictTypeMapper.queryDictTypeByMap(map);
    }


}
