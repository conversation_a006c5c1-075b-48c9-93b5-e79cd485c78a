package com.snbc.bbpf.bus.product.market.common.vo.invoice;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.vo.invoice
 * @ClassName: ApplyInvoiceParam
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 申请开票入参
 * @Author: wangsong
 * @CreateDate: 2020/8/26 19:18
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/26 19:18
 */
@Data
public class ApplyInvoiceParam {

    
    private String expressCompany;

    
    private String expressNo;

    
    private String receiverAddress;

    
    @NotBlank(message = "请填写收货人手机号")
    private String receiverTel;

    
    @NotBlank(message = "请填写收货人收货人姓名")
    private String receiverName;

    //租户Id
    
    @NotBlank(message = "商户id不能为空")
    private String tenantId;

    //开具类型 1:个人 2：企业
    
    private Integer applyType;



    @NotNull(message = "请选择开票订单")
    @Size(min = 1,message = "请选择开票订单")
    private List<String> productPurchaseId;


    
    @NotBlank(message = "发票抬头不能为空")
    private String invoiceTitle;

    
    private String mail;

    
    private String taxRegisterNo;

    
    private String bankNo;

    
    private String registerAddress;

    
    private String registerPhonenum;

    
    @NotEmpty(message = "商户名称不能为空")
    private String tenantName;

    
    private Boolean isUpdateInvoiceTit;


    
    //开票类型 1：增值税普通发票2：增值税专用发票
    @NotNull(message = "开票类型不能为空")
    private Integer invoiceType;

    //开票内容
    
    private String invoiceContent;

    //申请人
    
    private String applyUser;
    //开票介质 1：纸质发票 2：电子发票

    
    @NotNull(message = "开票介质不能为空")
    private Integer invoiceCarrier;

    //开票金额
    
    private BigDecimal invoiceAmount;

    
    private String bankName;

    
    private String invoiceTitleId;
}
