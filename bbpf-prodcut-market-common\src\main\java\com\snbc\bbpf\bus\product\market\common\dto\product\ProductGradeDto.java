package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/*
* @author: liangJB
     * 功能描述: <br>
     * 产品编辑类
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
字段名称	类型	是否必填	描述
grade	string	是	阶梯
gradeUnit	string	是	阶梯单位，入 天 兆 次数 交易额 数据量
gradeDiscount	decimal	否	阶梯折扣
price	decimal	是	价格
isDefault	Integer	是	是否默认，0：非默认 1：默认


* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductGradeDto  {
    private String productId;
    
    private String productGradeId;
    
    private String grade;
    
    private String gradeUnit;
    
    private BigDecimal gradeDiscount;
    
    private BigDecimal price;
    
    private Integer isDefault;
}
