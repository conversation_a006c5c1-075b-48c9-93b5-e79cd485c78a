package com.snbc.bbpf.bus.product.manager.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.Random;

/**
 * 请求Id生成器，用于生成上报服务使用情况的唯一编号
 */
public class RequestIdGenerator {
    private static final Logger LOGGER  = LoggerFactory.getLogger(RequestIdGenerator.class);
    public static final int RANDOM_CYCLE= 3;
    public static final int RANDOM_BOUND = 10;
    private  Random rand = SecureRandom.getInstanceStrong();

    /**
     * 构造器
     * @throws NoSuchAlgorithmException
     */
    public RequestIdGenerator() throws NoSuchAlgorithmException {
        LOGGER.debug("default construct method");
    }

    /**
     * 根据租户Id生成对应的请求编号
     * @return
     */
    public  String getOrderIdByTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String newDate = sdf.format(Date.from(Instant.now()));
        StringBuilder result= new StringBuilder();
        for(int i=0;i<RANDOM_CYCLE;i++){
            result.append(this.rand.nextInt(RANDOM_BOUND));
        }
        return newDate+result.toString();
    }
}
