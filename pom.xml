<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<!-- 指定 POM 文件的模型版本 -->
	<modelVersion>4.0.0</modelVersion>
	<!-- 父项目信息：定义当前项目的父项目及其版本 -->
	<parent>
		<groupId>com.snbc.bbpf</groupId>
		<artifactId>bbpf-parent</artifactId>
		<version>3.0.1</version>
		<relativePath/>
	</parent>
	<!-- 当前聚合模块的基本信息 -->
	<groupId>com.snbc.bbpf</groupId>
	<!-- 项目的唯一标识符 -->
	<artifactId>bbpf-product-market</artifactId>
	<!-- 项目的版本号 -->
	<version>3.0.1</version>
	<!-- 指定此 POM 为聚合模块，不生成实际的 JAR 或 WAR 文件 -->
	<packaging>pom</packaging>
	<!-- 项目的名称 -->
	<name>bbpf-product-market</name>
	<!-- 项目的简短描述 -->
	<description>用于 bbpf-product-market 项目的聚合模块</description>
	<!-- 子模块列表：定义子模块，这些子模块将被 Maven 统一构建和管理 -->
	<modules>
		<!-- 公共模块 -->
		<module>bbpf-prodcut-market-common</module>
		<!-- MySQL 数据库相关模块 -->
		<module>bbpf-prodcut-market-mysql</module>
		<!-- OpenGauss 数据库相关模块 -->
		<module>bbpf-prodcut-market-opengauss</module>
		<!-- 管理模块 -->
		<module>bbpf-prodcut-market-manager</module>
	</modules>
	<!-- 项目属性定义：定义项目中使用的属性 -->
	<properties>
		<!-- Java 版本 -->
		<java.version>21</java.version>
		<!-- 项目版本号 -->
		<project.version>3.0.1</project.version>
		<!-- BBPF组件版本号 -->
		<bbpf-component.version>3.0.1</bbpf-component.version>
		<!-- JaCoCo 跳过标志，默认为false -->
		<jacoco.skip>false</jacoco.skip>
	</properties>

</project>