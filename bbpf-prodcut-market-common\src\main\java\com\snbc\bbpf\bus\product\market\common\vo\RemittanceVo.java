package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;
@Data
public class RemittanceVo {
    //订单编号
    @NotEmpty(message = "订单编号不能为空")
    
    private String purchaseNo;
    //汇款编号
    @NotEmpty(message = "汇款编号不能为空")
    
    private String remitTradeNo;
    //汇款银行名称
    @NotEmpty(message = "汇款银行名称不能为空")
    
    private String remitBankName;
    //汇款账号
    @NotEmpty(message = "汇款账号不能为空")
    
    private String remitBankNo;
    //汇款时间
    @NotEmpty(message = "汇款时间不能为空")
    
    private String remitTime;
    //汇款凭证地址
    
    private String remitProof;
}
