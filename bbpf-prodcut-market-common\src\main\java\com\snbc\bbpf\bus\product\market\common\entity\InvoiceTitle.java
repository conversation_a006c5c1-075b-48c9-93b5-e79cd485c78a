package com.snbc.bbpf.bus.product.market.common.entity;

import lombok.Data;
/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.entity
 * @ClassName: InvoiceTitle
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票抬头表映射类
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:21
 */
@Data
public class InvoiceTitle {
    private String invoiceTitleId;

    private String tenantId;

    private String applyType;
    //发票抬头公司
    private String companyName;

    //纳税人识别号
    private String taxRegisterNo;

    private String bankName;

    private String bankNo;

    //注册地址
    private String registerAddress;

    //注册号码
    private String registerPhonenum;

    private String mail;

    //收货地址
    private String receiverAddress;

    //收货人手机号
    private String receiverTel;

    //收货人姓名
    private String receiverName;
}
