package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.dto.order.ProducedOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductByConutStatistics;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductLOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderListDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductRemainingDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceBuyPDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductDRPAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductDRPCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductOrderDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductShopVemsPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.VemsOrderDayQueryBase;
import com.snbc.bbpf.bus.product.market.common.vo.product.VemsOrderPageQueryBase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductOrderMapper {

    /**
     * @author: liangJB
     * 功能描述: <br>
     * <查询产品订单列表接口>
     * @date: 2020/8/12 13:26
     * @param: record
     * @return: com.snbc.vems.product.dto.product.ProductOrderListDto
     */
    List<ProductOrderListDto> selectProductOrderList(ProductOrderPageQuery productOrderPageQuery);

    /**
     * @author: liangJB
     * 功能描述: <br>
     * <查询产品订单详情具体子单接口>
     * @date: 2020/8/12 13:26
     * @param: record
     * @return:  com.snbc.vems.product.dto.product.ProductLOrderDto
     */
    List<ProductLOrderDto> getProductRecordLstByPurchaseNo(@Param("purchaseNo") String purchaseNo);
    /**
     * @author: liangJB
     * 功能描述: <br>
     * <查询产品订单详情接口>
     * @date: 2020/8/12 13:26
     * @param: purchaseNo
     * @return:  ProductDetailDto
     */
    ProductOrderDetailDto getProductOrderDetailByPurchaseNo(@Param("purchaseNo") String purchaseNo);

    /**
     * @author: liangJB
     * 功能描述: <br>
     * <查询已采购产品订单列表接口>
     * @date: 2020/8/12 13:26
     * @param: tenantId
     * @return:  com.snbc.vems.product.dto.product.ServiceBuyPDto
     */
    List<ServiceBuyPDto> getServiceBuyLstByTenantId(ProductShopVemsPageQuery tenantId);
    /**
     * @author: liangJB
     * 功能描述: <br>
     * 合作运营需要的订单数据对象
     *      * 以设备ID为查询条件，找出对应的购买服务记录以订单倒叙时间排序
     * @date: 2020/8/12 13:26
     * @param: tenantId
     * @return:  VemsProductOrderDto
     */
    List<VemsProductOrderDto> getOrderLstByVemsId(VemsOrderPageQueryBase vemsOrderPageQueryBase);

    /**
     * @author: liangJB
     * 功能描述: <br>
     * 合作运营需要的日报表订单数据对象
     *      * 以设备ID为查询条件，找出对应的购买服务记录以订单倒叙时间排序
     * @date: 2020/8/12 13:26
     * @param: tenantId
     * @return:  VemsProductOrderDto
     */
    List<VemsProductDRPCountDto> getOrderLstByPayType(VemsOrderDayQueryBase vemsOrderPageQueryBase);

    /**
     * @author: liangJB
     * 功能描述: <br>
     * 合作运营需要的日报表统计数据
     *      * 以设备ID为查询条件，找出对应的购买服务记录以订单倒叙时间排序
     * @date: 2020/8/12 13:26
     * @param: tenantId
     * @return:  VemsProductOrderDto
     */
    VemsProductDRPAllCount getOrderCountByPayType(VemsOrderDayQueryBase vemsOrderPageQueryBase);

    //按天收费服务已购订单列表
    List<ProducedOrderDto> productByDayOrder(@Param(value = "tenantId")String tenantId, @Param(value = "productId")String productId);

    //按次收费服务已购订单列表
    List<ProducedOrderDto> productByCountOrder(@Param(value = "tenantId")String tenantId, @Param(value = "productId")String productId);

    //按天收费服务到期时间
    ProductRemainingDto getProductRemaining(@Param(value = "tenantId") String tenantId, @Param(value = "productId") String productId);

    //按次收费服务购买总次数、剩余次数
    ProductByConutStatistics getProductCount(@Param(value = "tenantId") String tenantId, @Param(value = "productId") String productId);

    //按次收费服务最近到期时间、到期次数
    ProductByConutStatistics getProductExpireInfo(@Param(value = "tenantId")String tenantId, @Param(value = "productId")String productId);
}
