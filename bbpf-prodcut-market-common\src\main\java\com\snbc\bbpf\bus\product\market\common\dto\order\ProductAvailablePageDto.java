package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/*
* @author: liangJB
     * 功能描述: <br>
     * 商家服务基础订单列表
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
purchaseNo	String	是	订单编号
productName	String	是	服务名称
productTypeName	String	是	服务类型
payTypeName	String	是	支付方式

productQuantity	int	是	购买量/台
paymentAmount	Decimal	是	实付金额(元)

purchaseStatus	int	是	具体直参考订单状态0：待支付 1：已支付 2：购买成功 3:已取消
purchaseTime	Date	是	下单时间
payTime	Date	是	支付时间


* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductAvailablePageDto {

    private String availableId;
    /**
     * 租户Id
     */
    private String tenantId;
    /**
     * 服务Id
     */
    private String productId;
    private String productCode;
    /**
     * 服务状态 已开通 已到期
     */
    private String serviceStatus;
    /**
     * 剩余天数
     */
    private String residueDay;
    /**
     * 服务名称
     */
    private String productName;
    /**
     * 服务类型
     */
    private Integer productType;
    private String productTypeName;
    /**
     * 收费类型
     */
    private Integer chargeType;
    private String chargeTypeName;
    /**
     * 服务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime beginTime;
    /**
     * 服务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime dueTime;
    /**
     * 有效值
     */
    private Double availableValue;
    /**
     * 有效值单位
     */
    private String availableUnit;
    /**
     * 是否续费
     */
    private Integer isRenew;

    /**
     * 服务入口
     */
    private String productEntrance;

    private String productImage;

}
