/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * @ClassName: PermissionSortVo
 * 权限树拖拽排序Vo对象
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/20
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PermissionSortVo {

    @NotBlank(message = "当前节点不能为空")
    private String currentNodeId;
    @NotBlank(message = "目标节点不能为空")
    private String targetNodeId;
    @NotBlank(message = "目标节点父id不能为空")
    private String parentId;
    @NotBlank(message = "排序类型不能为空")
    private String type;

    @Override
    public String toString() {
        return "PermissionSortVo{" +
                "currentNodeId='" + currentNodeId + '\'' +
                ", targetNodeId='" + targetNodeId + '\'' +
                ", parentId='" + parentId + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}
