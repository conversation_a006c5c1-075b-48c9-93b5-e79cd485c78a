package com.snbc.bbpf.bus.product.market.common.mapper;


import com.snbc.bbpf.bus.product.market.common.dto.product.ProductCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailExDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductGradeDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductListDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductSimpleDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ServicePPDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductPageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductQueryMapper {

    /**
     * @author: liangJB
     * 功能描述: <br>
     * <查询产品列表接口>
     * @date: 2020/8/12 13:26
     * @param: record
     * @return: ProductListDto
     */
    List<ProductListDto> selectProductList(ProductPageQuery productPageQuery);
    /**
     * @author: wjc1
     * 功能描述: <br>
     * 租户服务市场，按发布时间倒叙<查询产品列表接口>
     * @date: 2021/11/29 10:26
     * @param: record
     * @return: ProductListDto
     */
    List<ProductListDto> selectProductListForTenant(ProductPageQuery productPageQuery);

    /**
     * @author: liangJB
     * 功能描述: <br>
     * <查询产品详情接口>
     * @date: 2020/8/12 13:26
     * @param: productId
     * @return:  ProductDetailDto
     */
    ProductDetailDto getProductDetailByProductId(@Param("productId") String productId);
    /**
     * @author: liangJB
     * 功能描述: <br>
     * <查询产品详情接口>
     * @date: 2020/8/12 13:26
     * @param: productId
     * @return:  ProductSimpleDto
     */
    ProductSimpleDto getProductSimpleByProductId(@Param("productId") String productId);
    /**
     * @author: liangJB
     * 功能描述: <br>
     * 服务阶梯阶梯
     * @date: 2020/8/12 13:26
     * @param: productId
     * @return:  ProductGradeDto
     */
    List<ProductGradeDto> selectGradeByProductId(@Param("productId") String productId);
    /**
     * @author: wjc
     * 功能描述: <br>
     * 查询默认的服务阶梯阶梯
     * @date: 2021/11/12 13:26
     * @param: productId
     * @return:  ProductGradeDto
     */
    ProductGradeDto selectDefaultGradeByProductId(@Param("productId") String productId);

    List<ProductGradeDto> selectDefaultGradeByProductIds(@Param("productIdList") List<String> productId);


    /**
     * @author: liangJB
     * 功能描述: <br>
     * <查询产品统计接口>
     * @date: 2020/8/12 13:26
     * @param: productId
     * @return:  ProductSimpleDto
     */
    List<ProductCountDto> getProductCountByPIdAndTId(@Param("productId") String productId, @Param("tenantId") String tenantId);
    /**
     * @author: liangJB
     * 功能描述: <br>
     * 人人取查询产品详情接口>
     * @date: 2020/8/12 13:26
     * @param: productId
     * @return:  ProductDetailExDto
     */
    ProductDetailExDto getProductDetailExByProductId(@Param("productId") String productId);
    /**
     * @author: liangJB
     * 功能描述: <br>
     * 已购产品服务
     * @date: 2020/8/12 13:26
     * @param: productId
     * @return:  ProductDetailExDto
     */
    List<ServicePPDto> getProductLstByTranid(ProductPageQuery productPageQuery) ;
    /**
     * @author: liangJB
     * 功能描述: <br>
     * 查询线下的离线数据
     * @date: 2020/8/12 13:26
     * @param: productId
     * @return:  ProductDetailExDto
     */
    String getOfflineDetail();
}
