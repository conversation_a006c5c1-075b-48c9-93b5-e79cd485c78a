<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductQueryMapper">
  <resultMap id="BaseLstResultMap" type="com.snbc.bbpf.bus.product.market.common.dto.product.ProductListDto">
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_categoryName" jdbcType="VARCHAR" property="productCategoryName" />
    <result column="product_typeName" jdbcType="VARCHAR" property="productTypeName" />
    <result column="product_status" jdbcType="INTEGER" property="productStatus" />
    <result column="charge_typeName" jdbcType="VARCHAR" property="chargeTypeName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="product_brief" jdbcType="VARCHAR" property="productBrief" />
    <result column="product_image" jdbcType="VARCHAR" property="productImage" />
  </resultMap>
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailDto">
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_brief" jdbcType="VARCHAR" property="productBrief" />
    <result column="product_detail" jdbcType="VARCHAR" property="productDetail" />
    <result column="product_image" jdbcType="VARCHAR" property="productImage" />
    <result column="service_rule" jdbcType="VARCHAR" property="serviceRule" />
    <result column="aftersale_rule" jdbcType="VARCHAR" property="aftersaleRule" />
    <result column="refund_rule" jdbcType="VARCHAR" property="refundRule" />
    <result column="tutorial" jdbcType="VARCHAR" property="tutorial" />
    <result column="price_discount" jdbcType="DECIMAL" property="priceDiscount" />
  </resultMap>
  <resultMap id="BaseSimpleResultMap" type="com.snbc.bbpf.bus.product.market.common.dto.product.ProductSimpleDto">
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_category" jdbcType="INTEGER" property="productCategory" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="charge_type" jdbcType="INTEGER" property="chargeType" />
    <result column="product_categoryName" jdbcType="VARCHAR" property="productCategoryName" />
    <result column="product_typeName" jdbcType="VARCHAR" property="productTypeName" />
    <result column="charge_typeName" jdbcType="VARCHAR" property="chargeTypeName" />
  </resultMap>

  <resultMap id="BaseGradeResultMap" type="com.snbc.bbpf.bus.product.market.common.dto.product.ProductGradeDto">
    <result column="product_grade_id" jdbcType="VARCHAR" property="productGradeId" />
    <result column="grade" jdbcType="VARCHAR" property="grade" />
    <result column="grade_unit" jdbcType="DECIMAL" property="gradeUnit" />
    <result column="grade_discount" jdbcType="DECIMAL" property="gradeDiscount" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="is_default" jdbcType="INTEGER" property="isDefault" />
  </resultMap>
  <sql id="Base_Column_List">
    ts.product_id, ts.product_code, ts.product_name, ts.product_status, ts.publish_time,
    ts.create_time,ts.product_brief, ts.product_image,  tpc.value_name product_categoryName,
    tpt.value_name  product_typeName,tct.value_name charge_typeName
  </sql>
  <select id="selectProductList" parameterType="com.snbc.bbpf.bus.product.market.common.vo.productex.ProductPageQueryEx" resultMap="BaseLstResultMap">
    select DISTINCT
    <include refid="Base_Column_List"/>
    from t_product_services ts
    left join (select value_code,value_name from t_dict_value  where type_code='product_category') tpc on ts.product_category=tpc.value_code
    left join (select value_code,value_name from t_dict_value  where type_code='product_type') tpt on ts.product_type=tpt.value_code
    left join (select value_code,value_name from t_dict_value  where type_code='charge_type') tct on ts.charge_type=tct.value_code
    where 1=1
    <if test = "productName != null and productName != ''">
      and product_name like CONCAT ('%',#{productName,jdbcType = VARCHAR},'%')
    </if>
    <if test = "productCode != null and productCode != ''">
      and product_code like CONCAT ('%',#{productCode,jdbcType = VARCHAR},'%')
    </if>
    <if test = "productType != 0">
      and ts.product_type = #{productType,jdbcType=INTEGER}
    </if>
    <if test = "productCategory != 0">
      and ts.product_category = #{productCategory,jdbcType=INTEGER}
    </if>
    <if test = "productStatus != -1">
      and ts.product_status = #{productStatus,jdbcType=INTEGER}
    </if>
    <if test = "gradeTypeNo != 0">
      and ts.charge_type = #{gradeTypeNo,jdbcType=INTEGER}
    </if>
    <if test="createStartTime != null and createStartTime !=''">
      <![CDATA[ and create_time >= #{createStartTime,jdbcType=TIMESTAMP} ]]>
    </if>
    <if test="createEndTime != null and createEndTime !=''">
      <![CDATA[ and create_time < #{createEndTime,jdbcType=TIMESTAMP} ]]>
    </if>
    <if test="publishStartTime != null and publishStartTime !=''">
      <![CDATA[ and publish_time >= #{publishStartTime,jdbcType=TIMESTAMP} ]]>
    </if>
    <if test="publishEndTime != null and publishEndTime !=''">
      <![CDATA[ and publish_time < #{publishEndTime,jdbcType=TIMESTAMP} ]]>
    </if>
      order by create_time desc
  </select>
  <select id="selectProductListForTenant" parameterType="com.snbc.bbpf.bus.product.market.common.vo.productex.ProductPageQueryEx" resultMap="BaseLstResultMap">
    select DISTINCT
    <include refid="Base_Column_List"/>
    from t_product_services ts
    left join (select value_code,value_name from t_dict_value  where type_code='product_category') tpc on ts.product_category=tpc.value_code
    left join (select value_code,value_name from t_dict_value  where type_code='product_type') tpt on ts.product_type=tpt.value_code
    left join (select value_code,value_name from t_dict_value  where type_code='charge_type') tct on ts.charge_type=tct.value_code
    where 1=1
    <if test = "productType != 0">
      and ts.product_type = #{productType,jdbcType=INTEGER}
    </if>
    <if test = "productStatus != -1">
      and ts.product_status = #{productStatus,jdbcType=INTEGER}
    </if>
    <if test = "gradeTypeNo != 0">
      and ts.charge_type = #{gradeTypeNo,jdbcType=INTEGER}
    </if>
    order by publish_time desc
  </select>
  <select id="getProductSimpleByProductId" parameterType="java.lang.String" resultMap="BaseSimpleResultMap">
      select
        ts.product_id, ts.product_code, ts.product_name,
        ts.product_category product_category,ts.product_type product_type,ts.charge_type charge_type,
        tpc.value_name product_categoryName,tpt.value_name product_typeName,tct.value_name charge_typeName,
      string_agg(tp.permission_id,',') permission_id
      from t_product_services ts
      LEFT JOIN t_product_permission tp on ts.product_id = tp.product_id
      left join (select value_code,value_name from t_dict_value where type_code='product_category') tpc on
      ts.product_category=tpc.value_code
      left join (select value_code,value_name from t_dict_value where type_code='product_type') tpt on
      ts.product_type=tpt.value_code
      left join (select value_code,value_name from t_dict_value where type_code='charge_type') tct on
      ts.charge_type=tct.value_code
      where 1=1
      <if test="productId != null and productId != ''">
        and ts.product_id = #{productId}
      </if>
      GROUP BY ts.product_id, ts.product_code, ts.product_name,
      product_categoryName, product_typeName, charge_typeName
  </select>

  <select id="getProductDetailByProductId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    product_id,product_code,       product_name,  product_brief,product_detail,
    product_image,  service_rule, aftersale_rule,refund_rule,tutorial,price_discount
    from t_product_services
    where 1=1
    <if test="productId != null and productId != ''">
      and product_id = #{productId}
    </if>
  </select>
  <select id="selectGradeByProductId" parameterType="java.lang.String" resultMap="BaseGradeResultMap">
    select product_grade_id,grade,    grade_unit,grade_discount,price,is_default
    from t_product_grade
    where 1=1
    <if test="productId != null and productId != ''">
      and product_id = #{productId}
    </if>
    order by grade+0
  </select>
  <select id="selectDefaultGradeByProductId" parameterType="java.lang.String" resultMap="BaseGradeResultMap">
    select product_grade_id,grade,grade_unit,grade_discount,price,is_default
    from t_product_grade
    where is_default=1
    <if test="productId != null and productId != ''">
      and product_id = #{productId}
    </if>
  </select>

  <select id="selectDefaultGradeByProductIds" resultType="com.snbc.bbpf.bus.product.market.common.dto.product.ProductGradeDto">
    select product_id as productId, product_grade_id as productGradeId,
    grade, grade_unit as gradeUnit,grade_discount as gradeDiscount,
    price,is_default as isDefault
    from t_product_grade
    where is_default=1
    <if test="productIdList != null and productIdList.size() != 0">
      and product_id in
      <foreach item="productId" collection="productIdList" separator="," open="(" close=")" index="">
        #{productId, jdbcType=NUMERIC}
      </foreach>
    </if>
  </select>

  <select id="getProductCountByPIdAndTId" parameterType="java.lang.String" resultType="com.snbc.bbpf.bus.product.market.common.dto.product.ProductCountDto">
    select product_id from t_product_purchase
    where (purchase_status=2 or purchase_status=1)
    and product_id = #{productId}
    and tenant_id = #{tenantId}
  </select>

  <select id="getProductDetailExByProductId" parameterType="java.lang.String" resultType="com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailExDto">
    select
    product_id productId,product_code productCode,product_name productName,  product_brief productBrief,product_detail productDetail,
    product_image productImage,  service_rule serviceRule, aftersale_rule aftersaleRule,refund_rule refundRule,tutorial,
    tpt.value_name     productTypeName,tct.value_name chargeTypeName,price_discount priceDiscount,probation as probation
    from t_product_services ts
    left join (select value_code,value_name from t_dict_value  where type_code='product_type') tpt on ts.product_type=tpt.value_code
    left join (select value_code,value_name from t_dict_value  where type_code='charge_type') tct on ts.charge_type=tct.value_code
    where 1=0
    <if test="productId != null and productId != ''">
      or product_id = #{productId}
    </if>
  </select>
  <select id="getProductLstByTranid" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductPageQuery" resultType="com.snbc.bbpf.bus.product.market.common.dto.product.ServicePPDto">
    Select
    tps.product_id productId, tps.product_code productCode,   product_brief productBrief,    product_name productName,  product_image productImage,
    tpt.value_name     productTypeName,tct.value_name chargeTypeName
    from t_product_services tps
    left join (select value_code,value_name from t_dict_value  where type_code='product_type') tpt on tps.product_type=tpt.value_code
    left join (select value_code,value_name from t_dict_value  where type_code='charge_type') tct on tps.charge_type=tct.value_code
    where  tps.product_id in (select DISTINCT product_id from t_product_purchase where purchase_status=2
    <if test="tenantId != null and tenantId != ''">
      and tenant_id = #{tenantId}
    </if>)
    <if test = "productType != 0">
      and tps.product_type = #{productType,jdbcType=INTEGER}
    </if>
    <if test = "gradeTypeNo != 0">
      and tps.charge_type = #{gradeTypeNo,jdbcType=INTEGER}
    </if>
  order by publish_time desc
  </select>
  <select id="getOfflineDetail"  resultType="java.lang.String">
    Select product_payconfig_content from t_product_payconfig where product_payconfig_id='offline'
  </select>
</mapper>
