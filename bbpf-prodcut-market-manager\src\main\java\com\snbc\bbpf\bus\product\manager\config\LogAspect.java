/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.config;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * @ClassName: LogAspect
 * 定义aop拦截请求参数打印
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/24 11:31
 */
@Aspect
@Component
@Slf4j
public class LogAspect {
    public static final int MAXTIME = 700;
    /**
     * 用来记录请求进入的时间，防止多线程时出错，这里用了ThreadLocal
     */
    private ThreadLocal<Long> startTime = new ThreadLocal<>();

    /**
     * 定义切入点，controller下面的所有类的所有公有方法
     */
    @Pointcut("execution(* com.snbc.bbpf.bus.product.manager.controller..*.*(..))")
    public void requestLog() {
        // 空方法
    }

    /**
     * 方法之前执行，日志打印请求信息
     *
     * @param joinPoint joinPoint
     */
    @Before("requestLog()")
    public void doBefore(JoinPoint joinPoint) {

        startTime.set(System.currentTimeMillis());
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes)
                RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();
        MDC.put("userId", request.getHeader("userId"));

        // 打印当前的请求路径
        // 打印请求参数，如果需要打印其他的信息可以到request中去拿
        log.debug("=========================== reqeuset begin ===========================");
        log.info("RequestMapping:{},\nRequestMethod:{},\nRequest Param:{}",
                request.getRequestURI(), request.getMethod(), Arrays.toString(joinPoint.getArgs()));
        log.debug("============================ request end ============================");
    }

    /**
     * 方法返回之前执行，打印才返回值以及方法消耗时间
     *
     * @param response 返回值
     */
    @AfterReturning(returning = "response", pointcut = "requestLog()")
    public void doAfterRunning(Object response) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();

        // 打印返回值信息
        log.debug("=========================== response begin ===========================");
        log.info("RequestMapping:{},\nResponse:[{}],\nRequest spend times : {}ms", request.getRequestURI(), response,
                System.currentTimeMillis() - startTime.get());
        log.debug("============================ response end ============================");
        startTime.remove();
    }

}
