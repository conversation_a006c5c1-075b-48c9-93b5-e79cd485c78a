package com.snbc.bbpf.bus.product.market.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 服务购买记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductPurchase {
    /**
     * id
     */
    private String productPurchaseId;
    /**
     * 服务Id
     */
    private String productId;
    /**
     * 订单号
     */
    private String purchaseNo;
    /**
     * 购买数量
     */
    private Integer productQuantity;
    /**
     * 产品阶梯
     */
    private String productGrade;
    /**
     * 下单时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchaseTime;
    /**
     * 支付时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;
    /**
     * 成功时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime successTime;
    /**
     * 取消时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelTime;

    /**
     * 取消原因
     */
    private String cancelReson;
    /**
     * 支付类型
     */
    private Integer payType;
    /**
     * 订单金额
     */
    private BigDecimal purchaseAmount;
    /**
     * 实付金额
     */
    private BigDecimal paymentAmount;
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;
    /**
     * 订单状态
     */
    private Integer purchaseStatus;
    /**
     * 发票状态
     */
    private Integer invoiceStatus;
    /**
     * 租户Id
     */
    private String tenantId;

    /**
     * 用户Id
     */
    private String userId;
    /**
     * 是否续费
     */
    private Integer isRenew;
    /**
     * 产品价格
     */
    private BigDecimal productPrice;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 赠送人
     */
    private String giveUserName;
    /**
     * 赠送原因
     */
    private String giveReason;
    /**
     * 优惠券金额
     */
    private BigDecimal discountcardAmount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 线下支付时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime brankstartTime;

}
