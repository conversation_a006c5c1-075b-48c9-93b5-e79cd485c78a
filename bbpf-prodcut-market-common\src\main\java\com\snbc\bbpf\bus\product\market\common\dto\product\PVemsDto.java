package com.snbc.bbpf.bus.product.market.common.dto.product;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.dto.product
 * @ClassName: PVemsDto
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 售货机返回值
 * @Author: wangsong
 * @CreateDate: 2020/9/23 11:30
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/9/23 11:30
 */
@Data
public class PVemsDto {
    
    @JsonProperty(value = "id")
    private String vemsId;

    
    @JsonProperty(value = "vemName")
    private String vemsName;

    
    private String areaName;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date serviceExpTime;

    
    private String lineName;
}
