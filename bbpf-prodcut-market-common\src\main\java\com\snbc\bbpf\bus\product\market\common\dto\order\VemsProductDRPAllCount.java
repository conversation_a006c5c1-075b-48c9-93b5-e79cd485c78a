package com.snbc.bbpf.bus.product.market.common.dto.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/*
* @author: liangJB
     * 功能描述: <br>
     * 产品设备赠送日报整体统计，只要当有数据的时候才提供，
     * @date: 2020/8/12 13:13 赠送总设备数：100台    总金额：10000元
字段名称	类型	是否必填	描述
serviceCountAmount	Decimal	是	原价总金额
serviceCountDisAmount	Decimal	是	优惠总金额
serviceCountPayAmount	Decimal	是	实付总金额
* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VemsProductDRPAllCount {


    private BigDecimal serviceCountAmount;

    private BigDecimal serviceCount;
}
