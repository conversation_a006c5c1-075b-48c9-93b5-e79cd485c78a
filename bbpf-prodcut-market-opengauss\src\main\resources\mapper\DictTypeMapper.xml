<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.DictTypeMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.DictType" >
    <id column="type_code" property="typeCode" jdbcType="VARCHAR" />
    <result column="type_name" property="typeName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    type_code, type_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from t_dict_type
    where type_code = #{typeCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from t_dict_type
    where type_code = #{typeCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.DictType" >
    insert into t_dict_type (type_code, type_name)
    values (#{typeCode,jdbcType=VARCHAR}, #{typeName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.DictType" >
    insert into t_dict_type
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="typeCode != null" >
        type_code,
      </if>
      <if test="typeName != null" >
        type_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="typeCode != null" >
        #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="typeName != null" >
        #{typeName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.DictType" >
    update t_dict_type
    <set >
      <if test="typeName != null" >
        type_name = #{typeName,jdbcType=VARCHAR},
      </if>
    </set>
    where type_code = #{typeCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.bus.product.market.common.entity.DictType" >
    update t_dict_type
    set type_name = #{typeName,jdbcType=VARCHAR}
    where type_code = #{typeCode,jdbcType=VARCHAR}
  </update>
   <select id="queryAllDictType" resultMap="BaseResultMap" >
  	SELECT 
    <include refid="Base_Column_List" />
    FROM t_dict_type 
  </select>
  <select id="queryDictTypeByMap" resultMap="BaseResultMap" parameterType="map">
  	 select 
    <include refid="Base_Column_List" />
    from t_dict_type
    where 1=1
   <if test="typeName != null and typeName !=''">
			AND  type_name like CONCAT('%',#{typeName,jdbcType=VARCHAR},'%')
		</if>
  </select>
</mapper>