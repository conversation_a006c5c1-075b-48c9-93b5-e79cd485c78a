package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.service.PurchasedProductOrderService;
import com.snbc.bbpf.bus.product.manager.utils.OrderServiceUtils;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProducedOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductRemainingDto;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ProjectName: product-manager
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: PurchasedOrderByDayImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 按天购买的服务订单信息
 * @Author: wangsong
 * @CreateDate: 2021/2/25 18:27
 * @UpdateUser: wangsong
 * @UpdateDate: 2021/2/25 18:27
 */
@Component(value = "purchasedProductOrderByDay")
public class PurchasedOrderByDayImpl implements PurchasedProductOrderService {
    @Autowired
    private ProductOrderMapper productOrderMapper;
    /***
      * @Description:    查询按天类型服务订单列表
      * @Author:         wangsong
      * @param :         tenantId
      * @param :         productId
      * @param :         pageNum
      * @param :         pageSize
      * @CreateDate:     2021/2/25 19:03
      * @UpdateUser:     wangsong
      * @UpdateDate:     2021/2/25 19:03
      * @return :        com.github.pagehelper.PageInfo<java.lang.Object>
     */
    @Override
    public PageInfo<Object> purchasedOrderList(String tenantId, String productId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum,pageSize);
        //查询已购买服务（按天）订单记录
        List<ProducedOrderDto> productByDayOrderDtos = productOrderMapper.productByDayOrder(tenantId, productId);
        //支付类型转文字
        productByDayOrderDtos.forEach(productByDayOrderDto ->
            productByDayOrderDto.setPayType(OrderServiceUtils.payTypeConverter(productByDayOrderDto.getPayType()))
        );
        return new PageInfo(productByDayOrderDtos);
    }

    /***
      * @Description:   查询已购买服务（按天）剩余天数、到期时间查询
      * @Author:         wangsong
      * @param :         tenantId
      * @param :         productId
      * @CreateDate:     2021/3/1 19:10
      * @UpdateUser:     wangsong
      * @UpdateDate:     2021/3/1 19:10
      * @return :        java.lang.Object
     */
    @Override
    public Object productRemainingStatis(String tenantId, String productId) {
        //查询总购买天数、到期时间
        ProductRemainingDto productRemaining = productOrderMapper.getProductRemaining(tenantId, productId);
        //计算剩余天数
        Duration duration = Duration.between(LocalDateTime.now(), productRemaining.getDueTime());
        //转化剩余天数
        int remainingDay = (int)duration.toDays();
        //小于零剩余天数置为0
        productRemaining.setAvailableValueRemaining(remainingDay > 0 ? remainingDay : 0);
        return productRemaining;
    }
}
