package com.snbc.bbpf.bus.product.market.common.dto.order;

import lombok.Data;

import java.util.List;
/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.controller
 * @ClassName: Authority2TenantConfigDto
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description:身份验证
 * @Author: ouyang
 * @CreateDate: 2020/8/24 14:55
 * @UpdateUser: ouyang
 * @UpdateDate: 2020/10/10 14:55
 */

@Data
public class Authority2TenantConfigDto {
    private String tenantId;
    private String purchaseNo;
    private String productCode;
    private List<Permission2Tenant> perList;
    private List<String> ids;
}
