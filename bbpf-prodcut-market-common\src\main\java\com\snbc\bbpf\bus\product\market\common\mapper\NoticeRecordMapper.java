package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.NoticeRecord;
import org.apache.ibatis.annotations.Mapper;
/**
 * @author: LiangJB
 * 功能描述: <br>
 * 消息通知记录表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface NoticeRecordMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 删除数据
     * @date: 2020/8/12 13:21
     * @param: noticeRecordId
     * @return: interesting
     */
    int deleteByPrimaryKey(String noticeRecordId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     * @param: record
     * @return: 编号
     */
    int insert(NoticeRecord noticeRecord);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     * @param: record
     * @return: 编号
     */
    int insertSelective(NoticeRecord noticeRecord);
    /**
     * @author: <PERSON><PERSON><PERSON>
     * 功能描述: <br>
     * 获取数据
     * @date: 2020/8/12 13:21
     * @param: noticeRecordId
     * @return: NoticeRecord
     */
    NoticeRecord selectByPrimaryKey(String noticeRecordId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     * @param: NoticeRecord
     * @return: 编号
     */
    int updateByPrimaryKeySelective(NoticeRecord noticeRecord);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     * @param: NoticeRecord
     * @return: 编号
     */
    int updateByPrimaryKey(NoticeRecord noticeRecord);
}
