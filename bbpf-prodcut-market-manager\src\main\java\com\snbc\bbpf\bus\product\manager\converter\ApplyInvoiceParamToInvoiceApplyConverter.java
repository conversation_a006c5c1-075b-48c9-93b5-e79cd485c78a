package com.snbc.bbpf.bus.product.manager.converter;

import com.snbc.bbpf.bus.product.market.common.entity.InvoiceApply;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ApplyInvoiceParam;
import com.snbc.bbpf.commons.objs.IConvert;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * ApplyInvoiceParam 到 InvoiceApply 的转换器
 */
@Mapper
public interface ApplyInvoiceParamToInvoiceApplyConverter extends IConvert<ApplyInvoiceParam, InvoiceApply> {
    ApplyInvoiceParamToInvoiceApplyConverter INSTANCE = Mappers.getMapper(ApplyInvoiceParamToInvoiceApplyConverter.class);

    @Override
    InvoiceApply to(ApplyInvoiceParam source);
}