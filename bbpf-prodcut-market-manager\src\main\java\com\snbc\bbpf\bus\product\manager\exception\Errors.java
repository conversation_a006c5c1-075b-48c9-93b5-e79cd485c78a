package com.snbc.bbpf.bus.product.manager.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: ccom.snbc.vems.product.exception
 * @ClassName: Errors
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 错误码定义
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:21
 */

@Getter
@AllArgsConstructor
public enum Errors implements BusinessExceptionAssert{


    //请求成功
    SUCCESS("000000", "操作成功"),
    //请求失败
    FAILED("111111", "操作失败"),

    /**
     * 通用的系统异常定义
     */
    REQUEST_TIME_OUT("994001", "请求超时"),
    DATASOURCE_CONNECT_FAILED("994002", "数据库连接失败"),
    SQL_EXECUTE_FAILED("994003", "sql执行失败"),
    SYS_NETWORK_EXCEPTION("994004", "网络异常"),
    SYSTEM_IO_ERROR("994005", "系统IO错误"),

    /**
     * 全系统
     */
    //参数错误
    PARAM_NOTNULL("990001", "缺少{0}关键参数"),
    //数据解析错误
    INPUT_PARAM_ANALYSIS_ERROR("993001", "数据解析错误"),
    INPUT_PARAM_PAST_ERROR("993002", "格式转换错误"),
    PARAM_ERROR("993003", "参数非法"),
    /**
     * 字典异常定义
     */
    DICT_VALUE_EXIST("991901", "字典值{}已存在"),
    PRODUCT_NOTICE_MQ_EXCHANGE_LOST("991902","请配置系统通知的MQ交换机"),

    /**
     * 外部调用
     */
    OTHER_SYSTEM_INVOK_FAILED("992001", "外部调用失败"),
    OTHER_SYSTEM_RESPONSE_NULL("992002", "返回内容为空"),
    OTHER_SYSTEM_ERROR("992003", "第三方系统错误"),
    OTHER_SYSTEM_CONNECT_TIMEOUT("992004", "连接超时"),

    //订单模块参数异常
    AMOUNT_ERROR("990101","实付金额不一致"),
    PRDOUCTCODE_EXIST("990102","产品编码已存在"),
    ORDER_CANCEL("990103","订单已取消"),
    ORDER_NOEXIST("990105","订单不存在"),
    REMITTANCE_EXIST("990104","汇款单已存在"),
    ORDER_ISPAYED("990109","订单已支付"),
    REMITTANCE_EXCEPTION("901111","保存汇款单异常"),


    //订单模块业务异常
    USERTIME_ERROR("991101","服务时长赠送失败"),
    USERTIME_BATCH_ERROR("991102","批量服务时长赠送失败"),
    ORDER_ERROR("991103","订单详情保存失败"),
    PUBLISH_EXCEPTION("901104","重复提交服务发布数据"),
    ORDER_UNPUBLISH("991105","产品服务未发布不能下单"),
    //wjc add start
    PRODUCTNOTHASGRAGE("991106","未配置价格梯度"),
    PRODUCTNOTEXSIT("991107","服务不存在"),
    TENANTIDNULL("991108","租户不能为空"),
    //wjc add end

    //支付参数错误
    OUT_SNBCPAY_PARAM_ERROR("993201", "调用snbcPay返回参数错误"),
    //支付业务异常
    QR_CODEERROR_ERROR("991202", "获取二维码异常"),
    VERIFY_ERROR("991203", "验签失败"),
    DB_ERROR("991204", "连接数据库异常"),
    CLOSE_ORDER_ERROR("991205", "关闭订单异常，orderid={0}"),

    //发票模块的参数错误
    INVOICEDPURCHASE("990301", "您选择的订单无法开票或不存在"),
    INVOICEISSUE("990302", "您选择的发票已开具或不存在"),
    AMOUNTERROR("990303", "纸质发票开票金额不能小于300元"),
    DELIVERYNOTNULL("990304", "申请纸质发票请填写收货人、手机号、详细地址"),
    //发票模块的业务逻辑错误
    WRITEEXCELERROR("991302", "写入Excel失败"),

    //发布服务模块业务异常定义
    SERVICEEXPIRED("991401", "您购买的服务已到期，请您及时续费"),
    SERVICEOFFLINE("991402", "您购买的服务目前已下线，具体情况请联系服务提供商"),
    SERVICEDISABLED("991403", "您购买的服务目前已禁用，具体情况请联系服务提供商"),
    SERVICENOTPURCHASED("991404", "您没有购买过该服务，请购买后使用"),
    CANNOTDEL("991405", "已经发布的服务无法删除"),
    SERVICEDOESNOTEXIST("991406", "该服务不存在"),
    PARAMERROR("990401", "请求参数非法"),
    LACKPARAM("990402", "请求缺少关键参数"),
    NOPURCHASEDATA("990403", "未查询到该订单记录"),
    PURCHASESTATUSERROR("990404", "订单状态异常"),
    NOPAYTRACKDATA("990405", "未查询到流水信息"),
    HASTRYDATA("990406", "该租户已购买过该产品，无法继续试用"),

    //产品功能定义模块异常定义
    PERMISSIONCODEEXIST("995401", "功能编码已存在");

    private final String code;
    private final String message;
}
