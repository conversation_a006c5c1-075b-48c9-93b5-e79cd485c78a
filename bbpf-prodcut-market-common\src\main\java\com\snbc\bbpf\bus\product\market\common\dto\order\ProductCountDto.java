package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/*
* @author: liangJB
     * 功能描述: <br>
     * 产品购买统计
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
productCode	String	是	服务编号
productName	String	是	服务名称
productCategoryName	String	是	服务类目名称
productTypeName	String	是	服务类型名称
chargeTypeName	String	是	收费方式名称
purchasesAmount	Decimal	是	总金额(元)
discountAmount	Decimal	是	优惠金额(元)
paymentAmount	Decimal	是	实付金额(元)


* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductCountDto extends ProductDto {

    
    private BigDecimal purchasesAmount;
    
    private BigDecimal discountAmount;
    
    private BigDecimal paymentAmount;
    
    private String productGradeUnit;

}
