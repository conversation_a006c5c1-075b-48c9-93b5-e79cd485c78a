package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.service.ProductPayConfigService;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPayConfig;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPayConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: ProductPayServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/10/13 13:03
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/13 13:03
 */
@Service
public class ProductPayConfigServiceImpl implements ProductPayConfigService {

    @Autowired
    private ProductPayConfigMapper productPayConfigMapper;

    /***
     * @Description: 支付宝支付配置查询并加入redis缓存
     * @Author: wangsong
     * @param :         payConfigId
     * @CreateDate: 2020/10/13 13:38
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/10/13 13:38
     * @return :        AlipayConfig
     */
    @Override
    @Cacheable(value = "payConfig", key = "#payConfigId")
    public String getAlipayPayConfig(String payConfigId) {
        ProductPayConfig productPayConfig = productPayConfigMapper.selectByPrimaryKey(payConfigId);
        return productPayConfig.getProductPayconfigContent();
    }

    /***
     * @Description: 微信支付配置查询并加入redis缓存
     * @Author: wangsong
     * @param :         payConfigId
     * @CreateDate: 2020/10/13 13:38
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/10/13 13:38
     * @return :        AlipayConfig
     */
    @Override
    @Cacheable(value = "payConfig", key = "#payConfigId")
    public String getWechatPayConfig(String payConfigId) {
        ProductPayConfig productPayConfig = productPayConfigMapper.selectByPrimaryKey(payConfigId);
        return productPayConfig.getProductPayconfigContent();
    }
}
