/**  
* <p>Title: TenantRequest.java</p>  
* <p>Description: </p>  
* <p>Copyright: Copyright (c) 2019</p>  
* <p>Company: newbeiyang</p>  
* <AUTHOR>
* @date 2019年12月4日  
* @version 2.0  
*/ 
package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.Builder;
import lombok.Data;

/**  
* <p>Title: TenantRequest</p>  
* <p>Description: </p>  
* <AUTHOR> 
* @date 2019年12月4日  
*/
@Data
@Builder
public class TenantRequest {

	/**
	* <p>Title: TenantRequest<／p>
	* <p>Description: <／p>
	* <p>Company: newbeiyang<／p> 
	* <AUTHOR>
	* @date 2019年12月4日
	*/
	private String isEnable;
}
