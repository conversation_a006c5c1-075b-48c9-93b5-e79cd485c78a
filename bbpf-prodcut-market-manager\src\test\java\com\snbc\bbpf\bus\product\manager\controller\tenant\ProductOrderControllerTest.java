package com.snbc.bbpf.bus.product.manager.controller.tenant;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.resp.product.AvailableDetailResp;
import com.snbc.bbpf.bus.product.manager.resp.product.OrderDetailResp;
import com.snbc.bbpf.bus.product.manager.resp.product.PeoductOrderDetailResp;
import com.snbc.bbpf.bus.product.manager.resp.product.ProductAvailablePageResp;
import com.snbc.bbpf.bus.product.manager.resp.product.PurchVemsPageResp;
import com.snbc.bbpf.bus.product.manager.resp.product.ServiceOrderPageResp;
import com.snbc.bbpf.bus.product.manager.service.ProductOrderService;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductAvailablePageDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductLOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceBuyPDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ServiceShopOrderDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductAvailablePageQueryBase;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductOrderVemsPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductOrderPageQueryEx;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductShopVemsPageQueryEx;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ProductOrderController 单元测试
 */
class ProductOrderControllerTest {

    @Mock
    private ProductOrderService productOrderService;

    @InjectMocks
    private ProductOrderController productOrderController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(productOrderController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    @DisplayName("设备标准列表 - 成功")
    void testSevicesShopListWithPage_Success() {
        // Given
        ProductShopVemsPageQueryEx queryEx = new ProductShopVemsPageQueryEx();
        queryEx.setTenantId("tenant123");
        queryEx.setPageNum(1);
        queryEx.setPageSize(10);
        
        ServiceBuyPDto dto = new ServiceBuyPDto();
        dto.setVemsId("VEMS123");
        
        List<ServiceBuyPDto> mockList = Arrays.asList(dto);
        PageInfo<ServiceBuyPDto> pageInfo = new PageInfo<>(mockList);
        
        when(productOrderService.sevicesShopListWithPage(any())).thenReturn(pageInfo);
        
        // When
        PurchVemsPageResp result = productOrderController.sevicesShopListWithPage(queryEx);
        
        // Then
        assertNotNull(result);
        assertEquals(Errors.SUCCESS.getCode(), result.getHead().getCode());
        assertEquals(Errors.SUCCESS.getMessage(), result.getHead().getMessage());
        assertNotNull(result.getBody());
        assertEquals(1, result.getBody().getList().size());
        verify(productOrderService, times(1)).sevicesShopListWithPage(any());
    }

    @Test
    @DisplayName("设备标准列表 - 租户ID为空")
    void testSevicesShopListWithPage_EmptyTenantId() {
        // Given
        ProductShopVemsPageQueryEx queryEx = new ProductShopVemsPageQueryEx();
        queryEx.setTenantId(""); // 空租户ID
        
        // When
        PurchVemsPageResp result = productOrderController.sevicesShopListWithPage(queryEx);
        
        // Then
        assertNotNull(result);
        assertEquals(Errors.FAILED.getCode(), result.getHead().getCode());
        assertEquals("租户ID不能为空。", result.getHead().getMessage());
        verify(productOrderService, never()).sevicesShopListWithPage(any());
    }

    @Test
    @DisplayName("设备标准列表 - 租户ID为null")
    void testSevicesShopListWithPage_NullTenantId() {
        // Given
        ProductShopVemsPageQueryEx queryEx = new ProductShopVemsPageQueryEx();
        queryEx.setTenantId(null); // null租户ID
        
        // When
        PurchVemsPageResp result = productOrderController.sevicesShopListWithPage(queryEx);
        
        // Then
        assertNotNull(result);
        assertEquals(Errors.FAILED.getCode(), result.getHead().getCode());
        assertEquals("租户ID不能为空。", result.getHead().getMessage());
        verify(productOrderService, never()).sevicesShopListWithPage(any());
    }

    @Test
    @DisplayName("人人取服务订单列表 - 成功")
    void testTenantSeviceOrderListWithPage_Success() {
        // Given
        ProductOrderPageQueryEx queryEx = new ProductOrderPageQueryEx();
        queryEx.setTenantId("tenant123");
        queryEx.setPageNum(1);
        queryEx.setPageSize(10);
        
        ServiceShopOrderDto dto = new ServiceShopOrderDto();
        dto.setPurchaseNo("PO123456");
        
        List<ServiceShopOrderDto> mockList = Arrays.asList(dto);
        PageInfo<ServiceShopOrderDto> pageInfo = new PageInfo<>(mockList);
        
        when(productOrderService.rrqserviceOrderListWithPage(any())).thenReturn(pageInfo);
        
        // When
        ServiceOrderPageResp result = productOrderController.tenantSeviceOrderListWithPage(queryEx);
        
        // Then
        assertNotNull(result);
        assertEquals(Errors.SUCCESS.getCode(), result.getHead().getCode());
        assertEquals(Errors.SUCCESS.getMessage(), result.getHead().getMessage());
        assertNotNull(result.getBody());
        assertEquals(1, result.getBody().getList().size());
        verify(productOrderService, times(1)).rrqserviceOrderListWithPage(any());
    }

    @Test
    @DisplayName("人人取服务订单列表 - 租户ID为空")
    void testTenantSeviceOrderListWithPage_EmptyTenantId() {
        // Given
        ProductOrderPageQueryEx queryEx = new ProductOrderPageQueryEx();
        queryEx.setTenantId(""); // 空租户ID
        
        // When
        ServiceOrderPageResp result = productOrderController.tenantSeviceOrderListWithPage(queryEx);
        
        // Then
        assertNotNull(result);
        assertEquals(Errors.FAILED.getCode(), result.getHead().getCode());
        assertEquals(Errors.FAILED.getMessage(), result.getHead().getMessage());
        verify(productOrderService, never()).rrqserviceOrderListWithPage(any());
    }

    @Test
    @DisplayName("订单详情 - 成功")
    void testOrderDetail_Success() {
        // Given
        String purchaseNo = "PO123456";
        
        ProductOrderDetailDto dto = new ProductOrderDetailDto();
        dto.setPurchaseNo(purchaseNo);
        dto.setProductName("测试产品");
        
        when(productOrderService.orderDetail(anyString())).thenReturn(dto);
        
        // When
        OrderDetailResp result = productOrderController.orderDetail(purchaseNo);
        
        // Then
        assertNotNull(result);
        assertEquals(Errors.SUCCESS.getCode(), result.getHead().getCode());
        assertEquals(Errors.SUCCESS.getMessage(), result.getHead().getMessage());
        assertNotNull(result.getBody());
        assertEquals(purchaseNo, result.getBody().getPurchaseNo());
        verify(productOrderService, times(1)).orderDetail(purchaseNo);
    }

    @Test
    @DisplayName("服务可用列表 - 成功")
    void testSeviceAvailableListWithPage_Success() {
        // Given
        ProductAvailablePageQueryBase query = new ProductAvailablePageQueryBase();
        query.setTenantId("tenant123");
        query.setPageNum(1);
        query.setPageSize(10);
        
        ProductAvailablePageDto dto = new ProductAvailablePageDto();
        dto.setAvailableId("AV123456");
        
        List<ProductAvailablePageDto> mockList = Arrays.asList(dto);
        PageInfo<ProductAvailablePageDto> pageInfo = new PageInfo<>(mockList);
        
        when(productOrderService.seviceAvailableListWithPage(any(ProductAvailablePageQueryBase.class))).thenReturn(pageInfo);
        
        // When
        ProductAvailablePageResp result = productOrderController.seviceAvailableListWithPage(query);
        
        // Then
        assertNotNull(result);
        assertEquals(Errors.SUCCESS.getCode(), result.getHead().getCode());
        assertEquals(Errors.SUCCESS.getMessage(), result.getHead().getMessage());
        assertNotNull(result.getBody());
        assertEquals(1, result.getBody().getList().size());
        verify(productOrderService, times(1)).seviceAvailableListWithPage(query);
    }

    @Test
    @DisplayName("可用详情 - 成功")
    void testAvailableDetail_Success() {
        // Given
        String availableId = "AV123456";
        String tenantId = "tenant123";
        
        ProductAvailablePageDto dto = new ProductAvailablePageDto();
        dto.setAvailableId(availableId);
        dto.setServiceStatus("已开通");
        
        when(productOrderService.availableDetail(anyString(), anyString())).thenReturn(dto);
        
        // When
        AvailableDetailResp result = productOrderController.availableDetail(availableId, tenantId);
        
        // Then
        assertNotNull(result);
        assertEquals(Errors.SUCCESS.getCode(), result.getHead().getCode());
        assertEquals(Errors.SUCCESS.getMessage(), result.getHead().getMessage());
        assertNotNull(result.getBody());
        assertEquals(availableId, result.getBody().getAvailableId());
        verify(productOrderService, times(1)).availableDetail(availableId, tenantId);
    }

    @Test
    @DisplayName("服务订单详细记录 - 成功")
    void testSeviceVemsOrderListWithPage_Success() {
        // Given
        ProductOrderVemsPageQuery query = new ProductOrderVemsPageQuery();
        query.setPurchaseNo("PO123456");
        query.setPageNum(1);
        query.setPageSize(10);
        
        ProductLOrderDto dto = new ProductLOrderDto();
        dto.setVemsId("PO123456");
        
        List<ProductLOrderDto> mockList = Arrays.asList(dto);
        PageInfo<ProductLOrderDto> pageInfo = new PageInfo<>(mockList);
        
        when(productOrderService.seviceVemsOrderListWithPage(any(ProductOrderVemsPageQuery.class))).thenReturn(pageInfo);
        
        // When
        PeoductOrderDetailResp result = productOrderController.seviceVemsOrderListWithPage(query);
        
        // Then
        assertNotNull(result);
        assertEquals(Errors.SUCCESS.getCode(), result.getHead().getCode());
        assertEquals(Errors.SUCCESS.getMessage(), result.getHead().getMessage());
        assertNotNull(result.getBody());
        assertEquals(1, result.getBody().getList().size());
        verify(productOrderService, times(1)).seviceVemsOrderListWithPage(query);
    }

    @Test
    @DisplayName("测试所有接口的响应结构")
    void testAllEndpointsResponseStructure() {
        // 验证所有接口都返回了正确的响应结构
        
        // 1. sevicesShopListWithPage
        ProductShopVemsPageQueryEx queryEx1 = new ProductShopVemsPageQueryEx();
        queryEx1.setTenantId("tenant123");
        when(productOrderService.sevicesShopListWithPage(any())).thenReturn(new PageInfo<>());
        
        PurchVemsPageResp resp1 = productOrderController.sevicesShopListWithPage(queryEx1);
        assertNotNull(resp1.getHead());
        
        // 2. tenantSeviceOrderListWithPage
        ProductOrderPageQueryEx queryEx2 = new ProductOrderPageQueryEx();
        queryEx2.setTenantId("tenant123");
        when(productOrderService.rrqserviceOrderListWithPage(any())).thenReturn(new PageInfo<>());
        
        ServiceOrderPageResp resp2 = productOrderController.tenantSeviceOrderListWithPage(queryEx2);
        assertNotNull(resp2.getHead());
        
        // 3. orderDetail
        when(productOrderService.orderDetail(anyString())).thenReturn(new ProductOrderDetailDto());
        
        OrderDetailResp resp3 = productOrderController.orderDetail("PO123");
        assertNotNull(resp3.getHead());
        
        // 4. seviceAvailableListWithPage
        when(productOrderService.seviceAvailableListWithPage(any())).thenReturn(new PageInfo<>());
        
        ProductAvailablePageResp resp4 = productOrderController.seviceAvailableListWithPage(new ProductAvailablePageQueryBase());
        assertNotNull(resp4.getHead());
        
        // 5. availableDetail
        when(productOrderService.availableDetail(anyString(), anyString())).thenReturn(new ProductAvailablePageDto());
        
        AvailableDetailResp resp5 = productOrderController.availableDetail("AV123", "tenant123");
        assertNotNull(resp5.getHead());
        
        // 6. seviceVemsOrderListWithPage
        when(productOrderService.seviceVemsOrderListWithPage(any())).thenReturn(new PageInfo<>());
        
        PeoductOrderDetailResp resp6 = productOrderController.seviceVemsOrderListWithPage(new ProductOrderVemsPageQuery());
        assertNotNull(resp6.getHead());
    }
}