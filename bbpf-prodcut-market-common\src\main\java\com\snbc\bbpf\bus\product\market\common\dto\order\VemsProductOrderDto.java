package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/*
* @author: liangJB
     * 功能描述: <br>
     * 合作运营需要的订单数据对象
     * 以设备ID为查询条件，找出对应的购买服务记录以订单倒叙时间排序
     * @date: 2020/8/12 13:13
     * 序号 订单编号 使用时长/天 商户名称 赠送人 赠送原因



字段名称	类型	是否必填	描述
purchaseNo	String	是	订单编号
shopName	String	是	商户名称
payTypeName	String	是	支付方式
productName	String	是	服务类目名称
  beginTime	Date	是	开始时间
dueTime	Date	是	结束时间
availableValue	int	是	使用时长 天为单位
* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VemsProductOrderDto  {
    
    private String purchaseNo;
    
    private String productName;
    
    private String shopName;
    
    private String payTypeName;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueTime;
    
    private int availableValue;
    
    private String giveUserName;
    
    private String giveReason;
}
