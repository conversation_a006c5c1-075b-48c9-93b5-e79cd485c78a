<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.PurchasePayTrackMapper">
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack">
    <id column="purchase_pay_track_id" jdbcType="VARCHAR" property="purchasePayTrackId" />
    <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo" />
    <result column="pay_track_time" jdbcType="TIMESTAMP" property="payTrackTime" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
    <result column="pay_track_no" jdbcType="VARCHAR" property="payTrackNo" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
  </resultMap>
  <sql id="Base_Column_List">
    purchase_pay_track_id, purchase_no, pay_track_time, pay_status, pay_track_no,update_time,pay_type
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_purchase_pay_track
        where purchase_pay_track_id = #{purchasePayTrackId,jdbcType=VARCHAR}
    </select>
    <select id="selectPayTracks" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_purchase_pay_track
        where 1=1
      <if test="purchaseNo != null and purchaseNo != ''">
        and purchase_no = #{purchaseNo,jdbcType=VARCHAR}
      </if>
      <if test="payStatus != null">
        and pay_status = #{payStatus,jdbcType=VARCHAR}
      </if>
      <if test="payTrackNo != null and payTrackNo != ''">
        and pay_track_no = #{payTrackNo,jdbcType=VARCHAR}
      </if>
      <if test="payType != null and payType != ''">
        and pay_type = #{payType,jdbcType=VARCHAR}
      </if>
      order by pay_track_time desc
    </select>
  <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack">
    insert into t_purchase_pay_track (purchase_pay_track_id, purchase_no, pay_track_time, pay_status, pay_track_no,update_time,pay_type)
    values (#{purchasePayTrackId,jdbcType=VARCHAR}, #{purchaseNo,jdbcType=VARCHAR}, #{payTrackTime,jdbcType=TIMESTAMP},
      #{payStatus,jdbcType=INTEGER}, #{payTrackNo,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},#{payType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack">
    insert into t_purchase_pay_track
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="purchasePayTrackId != null">
        purchase_pay_track_id,
      </if>
      <if test="purchaseNo != null">
        purchase_no,
      </if>
      <if test="payTrackTime != null">
        pay_track_time,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="payTrackNo != null">
        pay_track_no,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="purchasePayTrackId != null">
        #{purchasePayTrackId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseNo != null">
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="payTrackTime != null">
        #{payTrackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=INTEGER},
      </if>
      <if test="payTrackNo != null">
        #{payTrackNo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack">
    update t_purchase_pay_track
    <set>
      <if test="purchaseNo != null">
        purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="payTrackTime != null">
        pay_track_time = #{payTrackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=INTEGER},
      </if>
      <if test="payTrackNo != null">
        pay_track_no = #{payTrackNo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where purchase_pay_track_id = #{purchasePayTrackId,jdbcType=VARCHAR}
  </update>
  <update id="updateBatchSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack">
    <foreach collection="list" item="item" index="index">
      update t_purchase_pay_track
      <set>
        <if test="item.purchaseNo != null">
          purchase_no = #{item.purchaseNo,jdbcType=VARCHAR},
        </if>
        <if test="item.payTrackTime != null">
          pay_track_time = #{item.payTrackTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.payStatus != null">
          pay_status = #{item.payStatus,jdbcType=INTEGER},
        </if>
        <if test="item.payTrackNo != null">
          pay_track_no = #{item.payTrackNo,jdbcType=VARCHAR},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where purchase_pay_track_id = #{item.purchasePayTrackId,jdbcType=VARCHAR};
    </foreach>
    <if test="list != null and list.isEmpty() ">
      update t_purchase_pay_track set update_time ='' where 1=2
    </if>
  </update>
</mapper>