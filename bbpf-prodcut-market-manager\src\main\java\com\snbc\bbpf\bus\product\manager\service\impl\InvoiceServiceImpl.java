package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.InvoiceHandleService;
import com.snbc.bbpf.bus.product.manager.service.InvoiceService;
import com.snbc.bbpf.bus.product.manager.converter.InvoiceApplyToExportInvoiceDtoConverter;
import com.snbc.bbpf.bus.product.manager.utils.ExcelUtil;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.ExportInvoiceDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceListRRQDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoicePurchaseDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceTitleDto;
import com.snbc.bbpf.bus.product.market.common.entity.InvoiceApply;
import com.snbc.bbpf.bus.product.market.common.entity.InvoicePurchase;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceApplyMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ApplyInvoiceParam;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.InvoiceParamQuery;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.OpenerInvoice;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.NoSuchAlgorithmException;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: InvoiceServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票业务处理层
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InvoiceServiceImpl implements InvoiceService {

    /**
     * excel列长度
     */
    private static final Integer FIFTEEN = 15;
    private static final Integer EIGHTEEN = 18;
    private static final Integer TWENTY = 20;
    private static final Integer TWENTYFIVE = 25;
    //BigDecimal 两位小数
    private static final int TWO = 2;
    //发票已开票状态
    private static final int ZERO = 0;
    //分页默认页
    private static final int ONE = 1;
    //发票记录mapper
    @Autowired
    private InvoiceApplyMapper invoiceApplyMapper;
    @Autowired
    private InvoiceHandleService invoiceHandleService;
    @Autowired
    private ProductPurchaseMapper productPurchaseMapper;


    /***
     * @Description: 发票详情
     * @Author: wangsong
     * @param :         invoiceApplyId
     * @CreateDate: 2020/8/25 13:57
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/25 13:57
     * @return :        com.snbc.vems.product.vo.invoice.InvoiceDetailVo
     */
    @Override
    public InvoiceDetailDto invoiceDetail(String invoiceApplyId) {
        //查询发票详情
        InvoiceDetailDto invoiceDetailDto = invoiceApplyMapper.selectByPrimaryKey(invoiceApplyId);
        BigDecimal invoiceAmount = new BigDecimal(invoiceDetailDto.getInvoiceAmount());
        //发票金额两位小数处理
        invoiceDetailDto.setInvoiceAmount(invoiceAmount.setScale(TWO, RoundingMode.HALF_UP).toString());
        return invoiceDetailDto;
    }


    @Override
    public PageInfo<InvoicePurchaseDto> invoicePurchaseListWithPage(String invoiceApplyId,
            Integer pageSize, Integer pageNum, String tenantId, String userId, String sortField, String sequence) {
        pageNum = Optional.ofNullable(pageNum).orElse(ONE);
        pageSize = Optional.ofNullable(pageSize).orElse(TWENTY);
        PageHelper.startPage(pageNum, pageSize);
        List<InvoicePurchaseDto> invoicePurchaseDtoList;
        //带发票ID查询发票的订单
        if (StringUtils.isNotBlank(invoiceApplyId)) {
            invoicePurchaseDtoList = invoiceApplyMapper.invoiceDetailPurchaseList(invoiceApplyId, tenantId, userId,sortField,sequence);
        } else {
            //没有发票ID查询30天内的订单
            invoicePurchaseDtoList = invoiceApplyMapper.invoicePurchaseList(tenantId, userId,sortField,sequence);
        }
        return new PageInfo<>(invoicePurchaseDtoList);
    }

    @Override
    public void exportInvoice(HttpServletResponse response, InvoiceParamQuery invoiceParamQuery) throws BusinessException {
        //查询开票记录列表
        List<InvoiceApply> invoiceApplyList = invoiceApplyMapper.selectExportInvoice(invoiceParamQuery);
        //类型转汉字
        List<ExportInvoiceDto> exportInvoiceVoList = dataConversion(invoiceApplyList);
        //定义字段名称
        String[] titleColumn = {"invoiceCode", "tenantName", "invoiceCarrier", "invoiceType",
                "invoiceContent", "invoiceAmount", "invoiceStatus", "applyTime", "invoiceTitle",
                "taxRegisterNo", "registerAddress", "registerPhonenum", "bankName", "bankNo", "address"};
        //定义列表名称
        String[] titleName = {"开票编号", "商户名称", "发票介质", "发票类型", "发票内容", "开票金额",
                "开票状态", "申请时间", "公司名称", "纳税人识别码", "注册地址", "注册电话", "开户银行", "银行账号", "发送地址"};
        //定义列长度
        int[] titleSize = {EIGHTEEN, TWENTY, FIFTEEN, TWENTYFIVE, FIFTEEN, FIFTEEN, TWENTY, TWENTYFIVE, TWENTYFIVE, TWENTYFIVE,
                TWENTYFIVE, TWENTY, TWENTY, TWENTYFIVE, TWENTYFIVE};
        //生成excel
        new ExcelUtil().writeBigExcel(response, "发票申请记录", titleColumn, titleName, titleSize, exportInvoiceVoList, null);
    }

    /***
     * @Description: 根据查询出的发票记录转换为excel需要的数据
     * @Author: wangsong
     * @param :         invoiceApplyList
     * @CreateDate: 2020/8/26 14:10
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/26 14:10
     * @return :        java.util.List<com.snbc.vems.product.vo.invoice.ExportInvoiceVo>
     */
    private static List<ExportInvoiceDto> dataConversion(List<InvoiceApply> invoiceApplyList) {
        List<ExportInvoiceDto> exportInvoiceVoList = new ArrayList<>();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        invoiceApplyList.forEach(invoiceApply -> {
            //对象转换
            ExportInvoiceDto exportInvoiceDto = InvoiceApplyToExportInvoiceDtoConverter.INSTANCE.to(invoiceApply);
            exportInvoiceDto.setApplyTime(dtf.format(invoiceApply.getApplyTime()));
            exportInvoiceDto.setApplyType(applyTypeConver(invoiceApply.getApplyType()));
            exportInvoiceDto.setInvoiceType(invoiceTypeConver(invoiceApply.getInvoiceType()));
            exportInvoiceDto.setInvoiceAmount(invoiceApply.getInvoiceAmount().
                    setScale(TWO, RoundingMode.HALF_UP).toString());
            exportInvoiceDto.setInvoiceCarrier(invoiceCarrierConver(invoiceApply.getInvoiceCarrier()));
            exportInvoiceDto.setInvoiceStatus(invoiceStatusConver(invoiceApply.getInvoiceStatus()));
            if (StringUtils.isNotBlank(invoiceApply.getMail())) {
                exportInvoiceDto.setAddress(invoiceApply.getMail());
            } else {
                exportInvoiceDto.setAddress(invoiceApply.getReceiverAddress());
            }
            exportInvoiceVoList.add(exportInvoiceDto);
        });
        return exportInvoiceVoList;
    }

    /***
      * @Description:    云平台发票分页
      * @Author:         wangsong
      * @param :         invoiceParamQuery
      * @CreateDate:     2020/11/18 11:04
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/11/18 11:04
      * @return :        com.github.pagehelper.PageInfo<InvoiceListRRQDto>
     */
    @Override
    public PageInfo<InvoiceListRRQDto> invoiceListWithPageForRRQ(InvoiceParamQuery invoiceParamQuery) {
        invoiceParamQuery.setPageNum(Optional.ofNullable(invoiceParamQuery.getPageNum()).orElse(ONE));
        invoiceParamQuery.setPageSize(Optional.ofNullable(invoiceParamQuery.getPageSize()).orElse(TWENTY));
        //分页 页数，条数
        PageHelper.startPage(invoiceParamQuery.getPageNum(), invoiceParamQuery.getPageSize());
        //查询开票记录列表
        List<InvoiceListRRQDto> invoiceListRRQDtoList = invoiceApplyMapper.selectInvoiceList(invoiceParamQuery);
        invoiceListRRQDtoList.forEach(invoiceListRRQDto ->
                invoiceListRRQDto.setInvoiceAmount(
                        new BigDecimal(invoiceListRRQDto.getInvoiceAmount()).setScale(TWO,BigDecimal.ROUND_HALF_UP).toString()));
        return new PageInfo<>(invoiceListRRQDtoList);
    }

    /***
     * @Description: 新增发票申请
     * @Author: wangsong
     * @param :         applyInvoiceParam
     * @CreateDate: 2020/8/26 20:53
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/26 20:53
     * @return :        void
     */
    @Override
    public void applyInvoice(ApplyInvoiceParam applyInvoiceParam) throws BusinessException, NoSuchAlgorithmException {
        //判断本次开票的订单中是否存在已开票的订单
        List<BigDecimal> payAmountList = productPurchaseMapper.selectNotIssued(applyInvoiceParam.getProductPurchaseId(),ZERO);
        //判断是否存在开票订单
        if (payAmountList.size() == applyInvoiceParam.getProductPurchaseId().size()) {
            //处理发票的快递信息
            String invoiceDeliveryId = invoiceHandleService.invoiceDeliveryProcess(applyInvoiceParam);
            //新增发票
            String invoiceId = invoiceHandleService.addInvoice(applyInvoiceParam, invoiceDeliveryId, payAmountList);
            //发票及订单关联
            List<InvoicePurchase> invoicePurchaseList=new ArrayList<>();
            applyInvoiceParam.getProductPurchaseId().stream().forEach(purchaseId->{
                InvoicePurchase invoicePurchase=new InvoicePurchase();
                invoicePurchase.setInvoicePurchaseId(UUID.randomUUID().toString());
                invoicePurchase.setProductPurchaseId(purchaseId);
                invoicePurchase.setInvoiceApplyId(invoiceId);
                invoicePurchaseList.add(invoicePurchase);
            });
            invoiceApplyMapper.insertInvoicePurchase(invoicePurchaseList);
            //开票后修改订单的开票状态
            productPurchaseMapper.updatePurchaseInvoiceStatus(applyInvoiceParam.getProductPurchaseId(), Constant.ONE_NUM);
        } else {
            throw new BusinessException(Errors.INVOICEDPURCHASE.getMessage(), Errors.INVOICEDPURCHASE.getCode(),
                    new Exception());
        }
    }

    @Override
    public void updateInvoiceStatus(OpenerInvoice openerInvoice) throws BusinessException {
        int invoiceStatus = invoiceApplyMapper.invoiceStatusById(openerInvoice.getInvoiceApplyId());
        //是否开具
        if(invoiceStatus == TWO){
            throw new BusinessException(Errors.INVOICEISSUE.getMessage(),
                    Errors.INVOICEISSUE.getCode(),null);
        }
        //修改发票状态
        invoiceApplyMapper.updateinvoiceStatus(openerInvoice.getInvoiceApplyId());
        //修改订单的发票状态
        productPurchaseMapper.updatePurchaseByInvoiceId(openerInvoice.getInvoiceApplyId());
    }

    //发票抬头
    @Override
    public InvoiceTitleDto getInvoiceTitle(String tenantId) {
        return invoiceApplyMapper.getInvoiceTitleByTenantId(tenantId);
    }

    /***
     * @Description: 发票介质转换
     * @Author: wangsong
     * @param :         invoiceCarrier
     * @CreateDate: 2020/8/27 18:26
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 18:26
     * @return :        void
     */
    private static String invoiceCarrierConver(Integer invoiceCarrier) {
        switch (invoiceCarrier) {
            case Constant.ONE_NUM:
                return "纸质发票";
            case TWO:
                return "电子发票";
            default:
                return "";
        }
    }

    /***
     * @Description: 发票状态转换为汉字
     * @Author: wangsong
     * @param :         invoiceStatus
     * @CreateDate: 2020/8/27 18:27
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 18:27
     * @return :        void
     */
    private static String invoiceStatusConver(Integer invoiceStatus) {
        switch (invoiceStatus) {
            case Constant.ONE_NUM:
                return "已申请";
            case TWO:
                return "已开票";
            default:
                return "";
        }
    }

    /***
     * @Description: 发票类型转换为汉字
     * @Author: wangsong
     * @param :         invoiceType
     * @CreateDate: 2020/8/27 18:27
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 18:27
     * @return :        void
     */
    private static String invoiceTypeConver(Integer invoiceType) {
        switch (invoiceType) {
            case Constant.ONE_NUM:
                return "增值税普通发票";
            case TWO:
                return "增值税专用发票";
            default:
                return "";
        }
    }

    /***
     * @Description: 发票抬头转换
     * @Author: wangsong
     * @param :         applyType
     * @CreateDate: 2020/8/27 18:27
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 18:27
     * @return :        void
     */
    private static String applyTypeConver(Integer applyType) {
        switch (applyType) {
            case Constant.ONE_NUM:
                return "个人";
            case TWO:
                return "企业";
            default:
                return "";
        }
    }
}
