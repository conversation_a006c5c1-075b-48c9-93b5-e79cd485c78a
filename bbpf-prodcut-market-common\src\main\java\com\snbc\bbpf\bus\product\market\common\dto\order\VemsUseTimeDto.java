package com.snbc.bbpf.bus.product.market.common.dto.order;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.constraints.NotNull;

@Data
public class VemsUseTimeDto {
    /**
     * 售货机id
     */

    @NotEmpty(message = "售货机id不能为空")
    private String vemsId;
    /**
     * 售货机名称
     */

    @NotEmpty(message = "售货机名称不能为空")
    private String vemsName;
    /**
     * 租户id
     */

    @NotEmpty(message = "租户id不能为空")
    private String tenantId;
    /**
     * 租户名称
     */

    @NotEmpty(message = "租户名称不能为空")
    private String tenantName;
    /**
     * 用户id
     */

    @NotEmpty(message = "用户id不能为空")
    private String userId;
    /**
     * 服务时长
     */

    @NotNull(message = "服务时长不能为空")
    private Integer useTime;
    /**
     * 线路
     */

    @NotNull(message = "赠送人不能为空")
    private String giveUserName;
    /**
     * 区域
     */

    @NotNull(message = "服赠送事由不能为空")
    private String giveReason;
}
