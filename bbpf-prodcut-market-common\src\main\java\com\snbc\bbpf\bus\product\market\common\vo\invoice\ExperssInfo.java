package com.snbc.bbpf.bus.product.market.common.vo.invoice;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.vo.invoice
 * @ClassName: ExperssInfo
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/10/15 14:59
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/15 14:59
 */
@Data
public class ExperssInfo {
    
    @NotEmpty(message = "物流公司不能为空")
    private String expressCompany;

    
    @NotEmpty(message = "物流单号不能为空")
    private String expressNo;

    
    @NotEmpty(message = "发票编号不能为空")
    private String invoiceId;
}
