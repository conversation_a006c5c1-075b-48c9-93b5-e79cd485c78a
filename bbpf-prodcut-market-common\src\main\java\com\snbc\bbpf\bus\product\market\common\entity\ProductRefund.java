package com.snbc.bbpf.bus.product.market.common.entity;

import java.time.LocalDateTime;

/**
 * 退款记录类型实体类
 */
public class ProductRefund {
    /**
     * 编号
     */
    private String refundId;
    /**
     * 服务购买Id
     */
    private String productPurchaseId;
    /**
     * 退款内容
     */
    private String refundContent;
    /**
     * 退款类型
     */
    private Integer refundType;
    /**
     * 退款时间
     */
    private LocalDateTime refundTime;
    /**
     * 退款操作人
     */
    private String refundOperator;
    /**
     * 退款金额
     */
    private Long refundFee;

    public String getRefundId() {
        return refundId;
    }
    /**
     * 设置refundId
     * @param refundId
     */
    public void setRefundId(String refundId) {
        this.refundId = refundId == null ? null : refundId.trim();
    }

    public String getProductPurchaseId() {
        return productPurchaseId;
    }
    /**
     * 设置productPurchaseId
     * @param productPurchaseId
     */
    public void setProductPurchaseId(String productPurchaseId) {
        this.productPurchaseId = productPurchaseId == null ? null : productPurchaseId.trim();
    }

    public String getRefundContent() {
        return refundContent;
    }
    /**
     * refundContent
     * @param refundContent
     */
    public void setRefundContent(String refundContent) {
        this.refundContent = refundContent == null ? null : refundContent.trim();
    }

    public Integer getRefundType() {
        return refundType;
    }

    public void setRefundType(Integer refundType) {
        this.refundType = refundType;
    }

    public LocalDateTime getRefundTime() {
        return refundTime;
    }
    /**
     * 设置refundTime
     * @param refundTime
     */
    public void setRefundTime(LocalDateTime refundTime) {
        this.refundTime = refundTime;
    }

    public String getRefundOperator() {
        return refundOperator;
    }
    /**
     * 设置refundOperator
     * @param refundOperator
     */
    public void setRefundOperator(String refundOperator) {
        this.refundOperator = refundOperator == null ? null : refundOperator.trim();
    }

    public Long getRefundFee() {
        return refundFee;
    }
    /**
     * 设置refundFee
     * @param refundFee
     */
    public void setRefundFee(Long refundFee) {
        this.refundFee = refundFee;
    }
}
