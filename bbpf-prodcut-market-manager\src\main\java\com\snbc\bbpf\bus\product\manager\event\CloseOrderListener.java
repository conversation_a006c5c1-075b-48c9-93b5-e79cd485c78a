/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.event;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.config.payconfig.AlipayConfig;
import com.snbc.bbpf.bus.product.manager.config.payconfig.WechatConfig;
import com.snbc.bbpf.bus.product.manager.service.ProductPayConfigService;
import com.snbc.pay.Pay;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @ClassName: CloseOrderListener
 * 关闭订单监听
 * @module: si-bbpf-product-market
 * @Author: wjc1
 * @date: 2023/6/8 15:30
 */
@Component
@Slf4j
public class CloseOrderListener {
    @Autowired
    private ProductPayConfigService productPayConfigService;
    @Async
    @EventListener
    public void onApplicationEvent(@NotNull CloseOrderEvent event) {
        String payType = event.getPayType();
        String outTradeNum = event.getPayTrackNo();
        log.debug("close {} order,orderNum= {} ",payType,outTradeNum);
        try{
            if(Constant.ALIPAY.equals(payType)){
                AlipayConfig alipayConfig = JSON.parseObject(productPayConfigService.getAlipayPayConfig(Constant.ALIPAY_CONFIG_ID),AlipayConfig.class);
                Pay pay = new com.snbc.pay.alipay.PayImpl(alipayConfig.getAppId(), alipayConfig.getAppPrivateKey(),
                        alipayConfig.getCharset(), alipayConfig.getPublicKey(), alipayConfig.getPid(),
                        alipayConfig.getSignType(), null, null);
                log.info("alipay close order orderNum={},result={}",outTradeNum,pay.closeOrder(outTradeNum));
            }else {
                WechatConfig wechatConfig = JSON.parseObject(productPayConfigService.getWechatPayConfig(Constant.WECHAT_CONFIG_ID),WechatConfig.class);
                Pay wechatPay = new com.snbc.pay.weixin.PayImpl(wechatConfig.getAppId(), wechatConfig.getSubAppId(),
                        wechatConfig.getAppPrivateKey(), wechatConfig.getMchId(), wechatConfig.getSubMchId(),
                        wechatConfig.getCertLocalPath(), wechatConfig.getCertPassword(), null);
                long startTime = System.currentTimeMillis();
                log.info("alipay close order orderNum={},result={}",outTradeNum,wechatPay.closeOrder(outTradeNum));
                log.info("The time required to invoke the third party of wechat is:{} ms", System.currentTimeMillis() - startTime);
            }
        }catch (Exception e){
            log.error("{} close order error,orderNum={}",payType,outTradeNum,e);
        }
    }
}
