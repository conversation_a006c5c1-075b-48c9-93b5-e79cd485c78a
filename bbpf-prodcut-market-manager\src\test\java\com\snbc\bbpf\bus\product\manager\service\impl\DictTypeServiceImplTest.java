package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.market.common.entity.DictType;
import com.snbc.bbpf.bus.product.market.common.mapper.DictTypeMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * DictTypeServiceImpl 单元测试
 */
class DictTypeServiceImplTest {

    @Mock
    private DictTypeMapper dictTypeMapper;

    @InjectMocks
    private DictTypeServiceImpl dictTypeService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("添加字典类型 - 成功")
    void testInsertDictType_Success() {
        // Given
        DictType dictType = new DictType();
        dictType.setTypeCode("test_type");
        dictType.setTypeName("测试类型");
        
        when(dictTypeMapper.insertSelective(any(DictType.class))).thenReturn(1);
        
        // When
        int result = dictTypeService.insertDictType(dictType);
        
        // Then
        assertEquals(1, result);
        verify(dictTypeMapper, times(1)).insertSelective(dictType);
    }

    @Test
    @DisplayName("修改字典类型 - 成功")
    void testUpdateDictType_Success() {
        // Given
        DictType dictType = new DictType();
        dictType.setTypeCode("test_type");
        dictType.setTypeName("修改后的测试类型");
        
        when(dictTypeMapper.updateByPrimaryKeySelective(any(DictType.class))).thenReturn(1);
        
        // When
        int result = dictTypeService.updateDictType(dictType);
        
        // Then
        assertEquals(1, result);
        verify(dictTypeMapper, times(1)).updateByPrimaryKeySelective(dictType);
    }

    @Test
    @DisplayName("删除字典类型 - 成功")
    void testDeleteDictType_Success() {
        // Given
        String typeCode = "test_type";
        when(dictTypeMapper.deleteByPrimaryKey(anyString())).thenReturn(1);
        
        // When
        int result = dictTypeService.deleteDictType(typeCode);
        
        // Then
        assertEquals(1, result);
        verify(dictTypeMapper, times(1)).deleteByPrimaryKey(typeCode);
    }

    @Test
    @DisplayName("根据主键查询字典 - 成功")
    void testSelectByPrimary_Success() {
        // Given
        String typeCode = "test_type";
        DictType expectedDictType = new DictType();
        expectedDictType.setTypeCode(typeCode);
        expectedDictType.setTypeName("测试类型");
        
        when(dictTypeMapper.selectByPrimaryKey(anyString())).thenReturn(expectedDictType);
        
        // When
        DictType result = dictTypeService.selectByPrimary(typeCode);
        
        // Then
        assertNotNull(result);
        assertEquals(typeCode, result.getTypeCode());
        assertEquals("测试类型", result.getTypeName());
        verify(dictTypeMapper, times(1)).selectByPrimaryKey(typeCode);
    }

    @Test
    @DisplayName("根据主键查询字典 - 不存在")
    void testSelectByPrimary_NotFound() {
        // Given
        String typeCode = "non_existent_type";
        when(dictTypeMapper.selectByPrimaryKey(anyString())).thenReturn(null);
        
        // When
        DictType result = dictTypeService.selectByPrimary(typeCode);
        
        // Then
        assertNull(result);
        verify(dictTypeMapper, times(1)).selectByPrimaryKey(typeCode);
    }

    @Test
    @DisplayName("查询所有字典类型编码 - 成功")
    void testQuertAllDictTypeCode_Success() {
        // Given
        DictType dictType1 = new DictType();
        dictType1.setTypeCode("type1");
        dictType1.setTypeName("类型1");
        
        DictType dictType2 = new DictType();
        dictType2.setTypeCode("type2");
        dictType2.setTypeName("类型2");
        
        List<DictType> expectedList = Arrays.asList(dictType1, dictType2);
        when(dictTypeMapper.queryAllDictType()).thenReturn(expectedList);
        
        // When
        List<DictType> result = dictTypeService.quertAllDictTypeCode();
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("type1", result.get(0).getTypeCode());
        assertEquals("type2", result.get(1).getTypeCode());
        verify(dictTypeMapper, times(1)).queryAllDictType();
    }

    @Test
    @DisplayName("查询所有字典类型编码 - 空列表")
    void testQuertAllDictTypeCode_EmptyList() {
        // Given
        when(dictTypeMapper.queryAllDictType()).thenReturn(Arrays.asList());
        
        // When
        List<DictType> result = dictTypeService.quertAllDictTypeCode();
        
        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(dictTypeMapper, times(1)).queryAllDictType();
    }

    @Test
    @DisplayName("根据条件查询字典类型 - 成功")
    void testGetDictTypeByMap_Success() {
        // Given
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("typeName", "测试");
        
        DictType dictType = new DictType();
        dictType.setTypeCode("test_type");
        dictType.setTypeName("测试类型");
        
        List<DictType> expectedList = Arrays.asList(dictType);
        when(dictTypeMapper.queryDictTypeByMap(any(Map.class))).thenReturn(expectedList);
        
        // When
        List<DictType> result = dictTypeService.getDictTypeByMap(queryMap);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test_type", result.get(0).getTypeCode());
        verify(dictTypeMapper, times(1)).queryDictTypeByMap(queryMap);
    }

    @Test
    @DisplayName("根据条件查询字典类型 - 空条件")
    void testGetDictTypeByMap_EmptyCondition() {
        // Given
        Map<String, Object> emptyMap = new HashMap<>();
        when(dictTypeMapper.queryDictTypeByMap(any(Map.class))).thenReturn(Arrays.asList());
        
        // When
        List<DictType> result = dictTypeService.getDictTypeByMap(emptyMap);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(dictTypeMapper, times(1)).queryDictTypeByMap(emptyMap);
    }
}