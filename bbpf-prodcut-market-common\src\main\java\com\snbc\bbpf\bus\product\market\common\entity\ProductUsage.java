package com.snbc.bbpf.bus.product.market.common.entity;

import lombok.Data;

import java.util.Date;
/**
 * 服务使用情况
 */
@Data
public class ProductUsage extends ProductVemsBase  {
    /**
     * 编号
     */
    private String usageId;
    /**
     * 请求编号
     */
    private String usageRequestCode;

    /**
     * 使用单位
     */
    private String usageUnit;
    /**
     * 使用时间
     */
    private Date usageTime;
    /**
     * 使用Ip
     */
    private String usageIp;
    /**
     * 具体操作
     */
    private String usageOperation;
    /**
     * 具体操作目标
     */
    private String usageTarget;


}
