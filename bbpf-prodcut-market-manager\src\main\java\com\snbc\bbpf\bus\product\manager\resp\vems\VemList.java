package com.snbc.bbpf.bus.product.manager.resp.vems;

import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;

/**
 *  *  * <p>
 *  *  * 版权所有 山东新北洋信息技术股份有限公司
 *  *  * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 *  *  * @copyright Copyright: 2014-2020
 * 售货机集合
 */

@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2017-04-17T06:13:34.050Z")
@ToString
@EqualsAndHashCode
public class VemList extends ArrayList<VemDto> {

}

