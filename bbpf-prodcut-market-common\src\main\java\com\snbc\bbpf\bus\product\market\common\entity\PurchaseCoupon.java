package com.snbc.bbpf.bus.product.market.common.entity;
/**
 * 购买优惠表
 */
public class PurchaseCoupon {
    /**
     * id
     */
    private String purchaseCouponId;
    /**
     * 服务购买Id
     */
    private String productPurchaseId;
    /**
     * 优惠Id
     */
    private String couponId;

    public String getPurchaseCouponId() {
        return purchaseCouponId;
    }
    /**
     * 设置purchaseCouponId
     * @param purchaseCouponId
     */
    public void setPurchaseCouponId(String purchaseCouponId) {
        this.purchaseCouponId = purchaseCouponId == null ? null : purchaseCouponId.trim();
    }

    public String getProductPurchaseId() {
        return productPurchaseId;
    }
    /**
     * 设置productPurchaseId
     * @param productPurchaseId
     */
    public void setProductPurchaseId(String productPurchaseId) {
        this.productPurchaseId = productPurchaseId == null ? null : productPurchaseId.trim();
    }

    public String getCouponId() {
        return couponId;
    }
    /**
     * 设置couponId
     * @param couponId
     */
    public void setCouponId(String couponId) {
        this.couponId = couponId == null ? null : couponId.trim();
    }
}
