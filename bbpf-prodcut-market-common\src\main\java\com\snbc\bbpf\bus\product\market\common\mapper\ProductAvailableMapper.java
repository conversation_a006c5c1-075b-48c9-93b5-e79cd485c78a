package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.dto.order.ProductAvailablePageDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.PVemsDto;
import com.snbc.bbpf.bus.product.market.common.entity.ProductAvailable;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductAvailablePageQueryBase;
import com.snbc.bbpf.bus.product.market.common.vo.product.VemsQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: LiangJB
 * 功能描述: <br>
 * 消息通知记录表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface ProductAvailableMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(ProductAvailable productAvailable);

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(ProductAvailable productAvailable);

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 获取数据
     * @date: 2020/8/12 13:21
     */
    ProductAvailable selectByPrimaryKey(String availableId);

    /**
     * @param vemsIds
     * @return
     * @author: LiangJB
     * 功能描述: <br>
     * 获取数据
     */
    List<ProductAvailable> selectByVemsIds(@Param("vemsIds") String[] vemsIds, @Param("tenantId") String tenantId, @Param("productId") String productId);

    /**
     * * @author:
     * * 功能描述: <br>
     * * 获取数据
     * 根据条件获取产品服务可用能力
     *
     * @param tenantId
     * @param productId
     * @return
     */
    List<ProductAvailable> selectByTenant(@Param("tenantId") String tenantId, @Param("productId") String productId);

    /**
     * 根据条件获取产品服务可用能力
     *
     * @param tenantId
     * @param vemsId
     * @param productId
     * @return
     */
    ProductAvailable selectByTenantAndProductId(@Param("tenantId") String tenantId, @Param("vemsId") String vemsId, @Param("productId") String productId);

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKeySelective(ProductAvailable productAvailable);

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKey(ProductAvailable productAvailable);

    List<PVemsDto> vemsWithPage(VemsQuery vemsQuery);

    List<PVemsDto> selectDueTimeByVemIds(@Param("vemIds") List<String> vemIds,
                                         @Param("productId") String productId);

    /**
     * 根据产品id获取产品名称
     *
     * @param productId
     * @return
     */
    String getProductNameById(@Param("productId") String productId);

    //根据当前时间查询即将到期和已经到期的服务
    List<ProductAvailable> queryOutTimeProduct(@Param("days") Integer days, @Param("code") List<String> code);

    /**
     * 根据收费阶梯查询即将到期服务
     *
     * @param threshold 提醒阈值
     * @param grade     类型
     * @return
     */
    List<ProductAvailable> queryOutTimeProductByGrade(@Param("threshold") Integer threshold, @Param("grade") String grade);

    List<ProductAvailablePageDto> selectseviceAvailableList(ProductAvailablePageQueryBase productAvailablePageQueryBase);
    ProductAvailablePageDto selectseviceAvailable(@Param("availableId") String availableId, @Param("tenantId") String tenantId);
}
