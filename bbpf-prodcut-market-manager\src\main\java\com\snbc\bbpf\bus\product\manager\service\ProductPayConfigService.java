package com.snbc.bbpf.bus.product.manager.service;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service
 * @ClassName: ProductPayConfigService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 获取支付配置
 * @Author: MR.LI
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: MR.LI
 * @UpdateDate: 2020/8/24 15:21
 */
public interface ProductPayConfigService {
    /**
     * 获取支付宝配置
     * @return AlipayConfig
     */
    String getAlipayPayConfig(String payConfigId);

    /**
     * 获取微信支付配置
     * @return WechatConfig
     */
    String getWechatPayConfig(String payConfigId);

}
