package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.ProductDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: LiangJB
 * 功能描述: <br>
 * 消息通知记录表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface ProductDetailMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(ProductDetail productDetail);

    /**
     * 批量插入数据
     * @param record
     * @return
     */
    int insertBatch(List<ProductDetail> productDetail);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(ProductDetail productDetail);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 获取数据
     * @date: 2020/8/12 13:21
     */
    ProductDetail selectByPrimaryKey(String availableId);

    /**
     * 根据订单id查询订单详情
     * @param purchaseNo
     * @return
     */
    List<ProductDetail> selectByPurchaseNo(@Param("purchaseNo") String purchaseNo);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKeySelective(ProductDetail productDetail);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKey(ProductDetail productDetail);
}
