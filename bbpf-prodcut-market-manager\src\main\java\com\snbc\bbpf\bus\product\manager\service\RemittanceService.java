package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.market.common.dto.order.RemittanceDto;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.vo.RemittanceVo;

/**
 * 汇款单service
 */
public interface RemittanceService {

    /**
     * 获取汇款单详情
     * @param productPurchaseId 订单编号
     * @return
     */
    RemittanceDto getRemittance(String productPurchaseId);

    /**
     * 汇款单信息提交
     * @param remittanceVo
     * @return
     */
    ProductPurchase remittanceSubmit(RemittanceVo remittanceVo) throws BusinessException;
    /**
     * 更新订单状态
     * @param productId
     * @param purchaseNo
     * @return
     */
    void updateProductStatusOnRedis(String productId,String purchaseNo);
}
