package com.snbc.bbpf.bus.product.market.common.entity;

import org.codehaus.jackson.annotate.JsonIgnoreProperties;


/**
 * 字典值实体类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DictValue{
    
    /**
     * 主键ID
     */
    
    private Integer valueId;
    /**
     * 字典类型编码
     */
    
    private String typeCode;

    /**
     * 字典值名称
     */
    
    private String valueName;

    /**
     * 字典值编码
     */
    
    private String valueCode;

    /**
     * 父节点ID
     */
    
    private Integer parentId;

    /**
     * 字典值描述
     */
    
    private String valueDesc;

    /**
     * 获取字典值主键
     */
    public Integer getValueId() {
        return valueId;
    }

    /**
     * 设置valueId
     * @param valueId
     */
    public void setValueId(Integer valueId) {
        this.valueId = valueId;
    }

    /**
     * 获取字典类型编码
     */
    public String getTypeCode() {
        return typeCode;
    }

    /**
     * 设置typeCode
     * @param typeCode
     */
    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode == null ? null : typeCode.trim();
    }

    /**
     * 获取字典值名称
     */
    public String getValueName() {
        return valueName;
    }

    /**
     * 设置valueName
     * @param valueName
     */
    public void setValueName(String valueName) {
        this.valueName = valueName == null ? null : valueName.trim();
    }

    /**
     * 获取字典值编码
     */
    public String getValueCode() {
        return valueCode;
    }

    /**
     * 设置valueCode
     * @param valueCode
     */
    public void setValueCode(String valueCode) {
        this.valueCode = valueCode == null ? null : valueCode.trim();
    }

    /**
     * 获取父节点ID
     */
    public Integer getParentId() {
        return parentId;
    }

    /**
     * 设置parentId
     * @param parentId
     */
    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }
    /**
     * 获取字典值描述
     */
    public String getValueDesc() {
        return valueDesc;
    }

    /**
     * 设置valueDesc
     * @param valueDesc
     */
    public void setValueDesc(String valueDesc) {
        this.valueDesc = valueDesc == null ? null : valueDesc.trim();
    }
}
