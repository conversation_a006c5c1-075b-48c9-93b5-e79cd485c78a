<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductRoleMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.ProductRole">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    </resultMap>
    <sql id="Base_Column_List">
    id, product_code, role_id
  </sql>
    <select id="selectByPurchaseNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_product_role where product_code=(
        select product_code from t_product_services where product_id=(
        select product_id from t_product_purchase where purchase_no= #{purchaseNo,jdbcType=VARCHAR})
        )
    </select>
</mapper>