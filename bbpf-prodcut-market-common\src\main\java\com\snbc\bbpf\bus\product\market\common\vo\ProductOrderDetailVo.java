package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.constraints.NotNull;

@Data
public class ProductOrderDetailVo {


    /**
     * 开始时间
     */
    
    private String activeTime;
    /**
     * 开始时间
     */
    
    private String beginTime;
    /**
     * 到期时间
     */
    
    private String dueTime;

    /**
     * 售货机id
     */
    
    private String vemsId;
    /**
     * 产品id
     */
    
    @NotEmpty(message = "产品服务id不能为空")
    private String productId;
    /**
     * 售货机名称
     */
    
    private String vemsName;
    /**
     * 服务可用数值
     */
    
    @NotNull(message = "服务可用数值不能为空")
    private Integer availableValue;
    /**
     * 服务可用单位
     */
    
    @NotEmpty(message = "服务可用单位不能为空")
    private String availableUnit;
    /**
     * 租户id
     */
    
    @NotEmpty(message = "租户Id不能为空")
    private String tenantId;

    /**
     * 线路名称
     */
    
    private String lineName;

    /**
     * 区域名称
     */
    
    private String areaName;


}
