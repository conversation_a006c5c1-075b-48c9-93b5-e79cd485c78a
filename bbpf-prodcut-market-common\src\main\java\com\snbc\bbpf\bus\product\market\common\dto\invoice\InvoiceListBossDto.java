package com.snbc.bbpf.bus.product.market.common.dto.invoice;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.vo
 * @ClassName: InvoiceVo
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:23
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:23
 */
@Data
public class InvoiceListBossDto {
    
    private String invoiceApplyId;

    
    private String invoiceCode;

    
    private String tenantName;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime applyTime;

    
    private String invoiceAmount;

    
    private String invoiceContent;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime invoiceTime;

    
    private String invoiceCarrier;

    
    private String applyType;

    
    private String invoiceStatus;
}
