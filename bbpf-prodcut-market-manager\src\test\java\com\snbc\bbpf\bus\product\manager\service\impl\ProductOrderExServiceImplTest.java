package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.service.AuthorityService;
import com.snbc.bbpf.bus.product.manager.service.ProductOrderExService;
import com.snbc.bbpf.bus.product.manager.service.RemittanceService;
import com.snbc.bbpf.bus.product.manager.utils.OrderNoUtil;
import com.snbc.bbpf.bus.product.manager.utils.OrderServiceUtils;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsUseTimeDto;
import com.snbc.bbpf.bus.product.market.common.mapper.DictValueMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductAvailableMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductDetailMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 赠送时长测试
 */
public class ProductOrderExServiceImplTest  {

    @Mock
    private RemittanceService remittanceService;
    @Mock
    private OrderServiceUtils orderServiceUtils;
    @Mock
    private ProductDetailMapper productDetailMapper;
    @Mock
    private ProductPurchaseMapper productPurchaseMapper;
    @Mock
    private DictValueMapper dictValueMapper;
    @Mock
    private ProductAvailableMapper productAvailableMapper;
    @Mock
    private OrderNoUtil orderNoUtil;
    @Mock
    private AuthorityService authorityService;
    @InjectMocks
    private ProductOrderExServiceImpl productOrderExService;
    @Before
    public void setUp()  {
        MockitoAnnotations.initMocks(this);
        // 设置@Value注解的productPrice字段值
        ReflectionTestUtils.setField(productOrderExService, "productPrice", new BigDecimal("100.00"));
    }
    /**
     * 测试赠送时长正常流程
     * @throws Exception
     */
    @Test
    public void testAddServiceTime_normal() throws BusinessException {
        VemsUseTimeDto vemsUseTimeDto=new VemsUseTimeDto();
        vemsUseTimeDto.setGiveReason("合同赠送");
        vemsUseTimeDto.setGiveUserName("ouyang");
        vemsUseTimeDto.setTenantId("143");
        vemsUseTimeDto.setTenantName("test");
        vemsUseTimeDto.setVemsId("12");
        vemsUseTimeDto.setUseTime(30);
        Mockito.when(orderNoUtil.getOrderNo()).thenReturn("PD2020122900001");
        Mockito.when(productDetailMapper.insert(Mockito.any())).thenReturn(1);
        //Mockito.when(bossManagerFeignClient.getTenantAdminUser(Mockito.any())).thenReturn("{\"result\":\"admin\"}");
        Mockito.when(productPurchaseMapper.insert(Mockito.any())).thenReturn(1);
        Mockito.when(dictValueMapper.getDictValueByTypeCodeAndValueCode(Mockito.any())).thenReturn("***********");
        Mockito.when(orderServiceUtils.updateProductAvailable(Mockito.anyString())).thenReturn(true);
        Mockito.when(authorityService.authority(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(remittanceService).updateProductStatusOnRedis(Mockito.anyString(),Mockito.anyString());
        String result=productOrderExService.addServiceTime(vemsUseTimeDto);
        Assert.assertEquals("PD2020122900001",result);

    }
    /**
     * 测试赠送时长异常流程
     * @throws Exception
     */
    @Test(expected = BusinessException.class)
    public void testAddServiceTime_exception() throws BusinessException {
        VemsUseTimeDto vemsUseTimeDto=new VemsUseTimeDto();
        vemsUseTimeDto.setGiveReason("合同赠送");
        vemsUseTimeDto.setGiveUserName("ouyang");
        vemsUseTimeDto.setTenantId("143");
        vemsUseTimeDto.setTenantName("test");
        vemsUseTimeDto.setVemsId("12");
        vemsUseTimeDto.setUseTime(30);
        Mockito.when(orderNoUtil.getOrderNo()).thenReturn("PD2020122900001");
        Mockito.when(productDetailMapper.insert(Mockito.any())).thenReturn(1);
        Mockito.when(productPurchaseMapper.insert(Mockito.any())).thenThrow(new BusinessException("测试异常", "TEST_ERROR"));
        Mockito.when(orderServiceUtils.updateProductAvailable(Mockito.anyString())).thenReturn(true);
        Mockito.when(authorityService.authority(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(remittanceService).updateProductStatusOnRedis(Mockito.anyString(),Mockito.anyString());
        String result=productOrderExService.addServiceTime(vemsUseTimeDto);
        Assert.assertEquals("PD2020122900001",result);

    }
    /**
     * 测试批量赠送时长正常流程
     * @throws Exception
     */
    @Test
    public void testAddServiceTimeBatch() throws BusinessException {
        List<VemsUseTimeDto> list=new ArrayList<>();
        VemsUseTimeDto vemsUseTimeDto=new VemsUseTimeDto();
        vemsUseTimeDto.setGiveReason("合同赠送");
        vemsUseTimeDto.setGiveUserName("ouyang");
        vemsUseTimeDto.setTenantId("143");
        vemsUseTimeDto.setTenantName("test");
        vemsUseTimeDto.setVemsId("12");
        vemsUseTimeDto.setUseTime(30);
        list.add(vemsUseTimeDto);
        Mockito.when(orderNoUtil.getOrderNo()).thenReturn("PD2020122900001");
        Mockito.when(productDetailMapper.insert(Mockito.any())).thenReturn(1);
        Mockito.when(productPurchaseMapper.insert(Mockito.any())).thenReturn(1);
        Mockito.when(orderServiceUtils.updateProductAvailable(Mockito.anyString())).thenReturn(true);
        Mockito.when(authorityService.authority(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(remittanceService).updateProductStatusOnRedis(Mockito.anyString(),Mockito.anyString());
        List<String>  result=productOrderExService.addServiceTimeBatch(list);
        List<String> expected=new ArrayList<>();
        expected.add("PD2020122900001");
        Assert.assertEquals(expected,result);
    }
    /**
     * 测试批量赠送时长异常流程
     * @throws Exception
     */
    @Test(expected = BusinessException.class)
    public void testAddServiceTimeBatch_exception() throws BusinessException {
        List<VemsUseTimeDto> list=new ArrayList<>();
        VemsUseTimeDto vemsUseTimeDto=new VemsUseTimeDto();
        vemsUseTimeDto.setGiveReason("合同赠送");
        vemsUseTimeDto.setGiveUserName("ouyang");
        vemsUseTimeDto.setTenantId("143");
        vemsUseTimeDto.setTenantName("test");
        vemsUseTimeDto.setVemsId("12");
        vemsUseTimeDto.setUseTime(30);
        list.add(vemsUseTimeDto);
        Mockito.when(orderNoUtil.getOrderNo()).thenReturn("PD2020122900001");
        Mockito.when(productDetailMapper.insert(Mockito.any())).thenThrow(new BusinessException("测试异常", "TEST_ERROR"));
        Mockito.when(productPurchaseMapper.insert(Mockito.any())).thenReturn(1);
        Mockito.when(orderServiceUtils.updateProductAvailable(Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(remittanceService).updateProductStatusOnRedis(Mockito.anyString(),Mockito.anyString());
        List<String>  result=productOrderExService.addServiceTimeBatch(list);
        List<String> expected=new ArrayList<>();
        expected.add("PD2020122900001");
        Assert.assertEquals(expected,result);
    }
}