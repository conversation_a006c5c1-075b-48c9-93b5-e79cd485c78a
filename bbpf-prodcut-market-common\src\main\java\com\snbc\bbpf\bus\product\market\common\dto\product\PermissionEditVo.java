/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * @ClassName: PermissionVo
 * PermissionVo对象
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionEditVo{
    /**
     * 权限id
     */
    @NotBlank(message = "功能ID不能为空")
    private String permissionId;

    private String permissionType;

    private String permissionName;

    private String permissionCode;

    private String parentId;

    private Integer orderBy;

    private String permissionIcon;

    private Integer hasEnable;

    private String routingUrl;

    private String parentName;

    private String remarks;

    private Integer permissionLevel;

    private Integer sysType;


    @Override
    public String toString() {
        return "PermissionEditVo{" +
                "permissionId='" + permissionId + '\'' +
                ", permissionType='" + permissionType + '\'' +
                ", permissionName='" + permissionName + '\'' +
                ", permissionCode='" + permissionCode + '\'' +
                ", parentId='" + parentId + '\'' +
                ", orderBy=" + orderBy +
                ", permissionIcon='" + permissionIcon + '\'' +
                ", hasEnable=" + hasEnable +
                ", routingUrl='" + routingUrl + '\'' +
                ", parentName='" + parentName + '\'' +
                ", remarks='" + remarks + '\'' +
                ", permissionLevel=" + permissionLevel +
                ", sysType=" + sysType +
                '}';
    }
}
