package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountIdDto;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductCountMapper;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCount;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCountPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.RPOrderQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import com.github.pagehelper.PageHelper;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ProductCountServiceImpl 单元测试
 */
class ProductCountServiceImplTest {

	@Mock
	private ProductCountMapper productCountMapper;

	@InjectMocks
	private ProductCountServiceImpl productCountService;

	@BeforeEach
	void setUp() {
		MockitoAnnotations.openMocks(this);
		reset(productCountMapper);
	}

	@Test
	@DisplayName("查询产品服务统计 - 成功")
	void testSeviceCountWithPage_Success() {
		// Given
		ProductCountQuery query = new ProductCountQuery();
		query.setProductName("product123");

		ProductAllCount expectedCount = new ProductAllCount();
		expectedCount.setServiceCount(100);
		expectedCount.setServiceCountAmount(new BigDecimal("95.00"));
		expectedCount.setServiceCountDisAmount(new BigDecimal("5.00"));
		expectedCount.setServiceCountPayAmount(new BigDecimal("90.00"));

		when(productCountMapper.getSeviceCountByType(any(ProductCountQuery.class))).thenReturn(expectedCount);

		// When
		ProductAllCount result = productCountService.seviceCountWithPage(query);

		// Then
		assertNotNull(result);
		assertEquals(100, result.getServiceCount());
		assertEquals(new BigDecimal("95.00"), result.getServiceCountAmount());
		assertEquals(new BigDecimal("5.00"), result.getServiceCountDisAmount());
		assertEquals(new BigDecimal("90.00"), result.getServiceCountPayAmount());
		verify(productCountMapper, times(1)).getSeviceCountByType(query);
	}

	@Test
	@DisplayName("查询产品服务统计列表 - 成功")
	void testSeviceCountListWithPage_Success() {
		// Given
		ProductCountPageQuery pageQuery = new ProductCountPageQuery();
		pageQuery.setPageNum(1);
		pageQuery.setPageSize(10);

		ProductCountDto dto1 = new ProductCountDto();
		dto1.setProductId("product1");
		dto1.setChargeTypeName("按次计费");

		ProductCountDto dto2 = new ProductCountDto();
		dto2.setProductId("product2");
		dto2.setChargeTypeName("按月计费");

		List<ProductCountDto> mockList = Arrays.asList(dto1, dto2);
		when(productCountMapper.getSeviceCountList(any(ProductCountPageQuery.class))).thenReturn(mockList);

		// When
		try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class)) {
			PageInfo<ProductCountDto> result = productCountService.seviceCountListWithPage(pageQuery);

			// Then
			assertNotNull(result);
			assertEquals(2, result.getList().size());
		}
	}

	@Test
	@DisplayName("查询产品服务月报统计 - 带分页")
	void testSeviceMRPCountListWithPage_Success() {
		// Given
		ProductMRPCountPageQuery pageQuery = new ProductMRPCountPageQuery();
		pageQuery.setPageNum(1);
		pageQuery.setPageSize(10);

		ShopMRPCountDto dto = new ShopMRPCountDto();
		dto.setTenantName("tenant123");
		dto.setTotalFee(new BigDecimal("1000.00"));

		List<ShopMRPCountDto> mockList = Arrays.asList(dto);
		when(productCountMapper.getSeviceMRPCountReport(any(ProductMRPCountPageQuery.class))).thenReturn(mockList);

		// When
		try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class)) {
			PageInfo<ShopMRPCountDto> result = productCountService.seviceMRPCountListWithPage(pageQuery);

			// Then
			assertNotNull(result);
			assertEquals(1, result.getList().size());
			assertEquals("tenant123", result.getList().get(0).getTenantName());
		}
	}

	@Test
	@DisplayName("查询产品服务月报统计 - 不带分页")
	void testGetSeviceMRPCountList_Success() {
		// Given
		ProductMRPCount productCount = new ProductMRPCount();
		productCount.setShopName("tenant123");

		ShopMRPCountDto dto = new ShopMRPCountDto();
		dto.setTenantName("tenant123");

		List<ShopMRPCountDto> expectedList = Arrays.asList(dto);
		when(productCountMapper.getSeviceMRPCountReport(any(ProductMRPCount.class))).thenReturn(expectedList);

		// When
		List<ShopMRPCountDto> result = productCountService.getSeviceMRPCountList(productCount);

		// Then
		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("tenant123", result.get(0).getTenantName());
		verify(productCountMapper, times(1)).getSeviceMRPCountReport(productCount);
	}

	@Test
	@DisplayName("查询产品服务月报统计标头 - 成功")
	void testGetSeviceMRPCountSum_Success() {
		// Given
		ProductMRPCount productCount = new ProductMRPCount();
		productCount.setShopName("tenant123");

		ShopMRPAllCount expectedSum = new ShopMRPAllCount();
		expectedSum.setServiceCountAmount(new BigDecimal("5000.00"));
		// ShopMRPAllCount does not have totalCount field, removed setTotalCount call

		when(productCountMapper.getSeviceMRPCountSum(any(ProductMRPCount.class))).thenReturn(expectedSum);

		// When
		ShopMRPAllCount result = productCountService.getSeviceMRPCountSum(productCount);

		// Then
		assertNotNull(result);
		assertEquals(new BigDecimal("5000.00"), result.getServiceCountAmount());
		// ShopMRPAllCount does not have totalCount field, removed getTotalCount
		// assertion
		verify(productCountMapper, times(1)).getSeviceMRPCountSum(productCount);
	}

	@Test
	@DisplayName("查询产品服务日报统计 - 成功")
	void testGetSeviceDRPCountList_Success() {
		// Given
		ProductMRPCount productCount = new ProductMRPCount();
		productCount.setShopName("tenant123");

		ShopMRPCountDto dto = new ShopMRPCountDto();
		dto.setTenantName("tenant123");

		List<ShopMRPCountDto> expectedList = Arrays.asList(dto);
		when(productCountMapper.getSeviceDRPCountReport(any(ProductMRPCount.class))).thenReturn(expectedList);

		// When
		List<ShopMRPCountDto> result = productCountService.getSeviceDRPCountList(productCount);

		// Then
		assertNotNull(result);
		assertEquals(1, result.size());
		verify(productCountMapper, times(1)).getSeviceDRPCountReport(productCount);
	}

	@Test
	@DisplayName("查询产品服务日报统计标头 - 成功")
	void testGetSeviceDRPCountSum_Success() {
		// Given
		ProductMRPCount productCount = new ProductMRPCount();

		ShopMRPAllCount expectedSum = new ShopMRPAllCount();
		expectedSum.setServiceCountAmount(new BigDecimal("1000.00"));

		when(productCountMapper.getSeviceDRPCountSum(any(ProductMRPCount.class))).thenReturn(expectedSum);

		// When
		ShopMRPAllCount result = productCountService.getSeviceDRPCountSum(productCount);

		// Then
		assertNotNull(result);
		assertEquals(new BigDecimal("1000.00"), result.getServiceCountAmount());
		verify(productCountMapper, times(1)).getSeviceDRPCountSum(productCount);
	}

	@Test
	@DisplayName("日期统计报表 - 成功")
	void testGetSeviceRPList_Success() {
		// Given
		RPOrderQuery query = new RPOrderQuery();
		query.setReportTime("2023-12-01");

		ShopMRPCountIdDto dto = new ShopMRPCountIdDto();
		dto.setTenantId("tenant123");

		List<ShopMRPCountIdDto> expectedList = Arrays.asList(dto);
		when(productCountMapper.getSeviceRPByTenant(any(RPOrderQuery.class))).thenReturn(expectedList);

		// When
		List<ShopMRPCountIdDto> result = productCountService.getSeviceRPList(query);

		// Then
		assertNotNull(result);
		assertEquals(1, result.size());
		verify(productCountMapper, times(1)).getSeviceRPByTenant(query);
	}

	@Test
	@DisplayName("插入日报表 - 成功")
	void testInsertSeviceRPByTenant_Success() {
		// Given
		RPOrderQuery query = new RPOrderQuery();
		query.setReportTime("2023-12-01");

		when(productCountMapper.deleteSeviceRPByTenant(anyString())).thenReturn(1);
		when(productCountMapper.insertSeviceRPByTenant(any(RPOrderQuery.class))).thenReturn(1);

		// When
		int result = productCountService.insertSeviceRPByTenant(query);

		// Then
		assertEquals(1, result);
		verify(productCountMapper, times(1)).deleteSeviceRPByTenant("2023-12-01");
		verify(productCountMapper, times(1)).insertSeviceRPByTenant(query);
	}

	@Test
	@DisplayName("插入月报表 - 成功")
	void testInsertSeviceRPByTenantM_Success() {
		// Given
		RPOrderQuery query = new RPOrderQuery();
		query.setReportTime("2023-12");

		when(productCountMapper.deleteSeviceRPByTenantM(anyString())).thenReturn(1);
		when(productCountMapper.insertSeviceRPByTenantM(any(RPOrderQuery.class))).thenReturn(1);

		// When
		int result = productCountService.insertSeviceRPByTenantM(query);

		// Then
		assertEquals(1, result);
		verify(productCountMapper, times(1)).deleteSeviceRPByTenantM("2023-12");
		verify(productCountMapper, times(1)).insertSeviceRPByTenantM(query);
	}

	@Test
	@DisplayName("删除日报表 - 成功")
	void testDeleteSeviceRPByTenant_Success() {
		// Given
		String reportDay = "2023-12-01";
		when(productCountMapper.deleteSeviceRPByTenant(anyString())).thenReturn(1);

		// When
		int result = productCountService.deleteSeviceRPByTenant(reportDay);

		// Then
		assertEquals(1, result);
		verify(productCountMapper, times(1)).deleteSeviceRPByTenant(reportDay);
	}

	@Test
	@DisplayName("删除月报表 - 成功")
	void testDeleteSeviceRPByTenantM_Success() {
		// Given
		String reportDay = "2023-12";
		when(productCountMapper.deleteSeviceRPByTenantM(anyString())).thenReturn(1);

		// When
		int result = productCountService.deleteSeviceRPByTenantM(reportDay);

		// Then
		assertEquals(1, result);
		verify(productCountMapper, times(1)).deleteSeviceRPByTenantM(reportDay);
	}

}