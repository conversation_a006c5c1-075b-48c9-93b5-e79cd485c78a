package com.snbc.bbpf.bus.product.market.common.entity;


import lombok.Data;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.entity
 * @ClassName: InvoicePurchase
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票与订单关系表映射类
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:21
 */
@Data
public class InvoicePurchase {
    private String invoicePurchaseId;

    private String invoiceApplyId;

    private String productPurchaseId;
}
