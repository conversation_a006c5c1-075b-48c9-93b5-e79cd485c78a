<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductServicesMapper">
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.ProductServices">
    <id column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_category" jdbcType="INTEGER" property="productCategory" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="product_status" jdbcType="INTEGER" property="productStatus" />
    <result column="charge_type" jdbcType="INTEGER" property="chargeType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="product_brief" jdbcType="LONGVARCHAR" property="productBrief" />
    <result column="product_detail" jdbcType="LONGVARCHAR" property="productDetail" />
    <result column="product_image" jdbcType="VARCHAR" property="productImage" />
    <result column="service_rule" jdbcType="LONGVARCHAR" property="serviceRule" />
    <result column="aftersale_rule" jdbcType="LONGVARCHAR" property="aftersaleRule" />
    <result column="refund_rule" jdbcType="LONGVARCHAR" property="refundRule" />
    <result column="tutorial" jdbcType="VARCHAR" property="tutorial" />
    <result column="product_entrance" jdbcType="VARCHAR" property="productEntrance" />
    <result column="probation" jdbcType="INTEGER" property="probation" />
  </resultMap>
  <delete id="delProductService" parameterType="java.lang.String">
    delete from t_product_services where product_id = #{productId}
  </delete>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    product_id,
  product_code,
  product_name,
  product_category,
  product_type,
  product_status,
  charge_type,
  create_time,
  update_time,
  publish_time,
  product_brief,
  product_detail,
  product_image,
  service_rule,
  aftersale_rule,
  refund_rule,
  tutorial,
  product_entrance,probation
    from t_product_services
    where product_id = #{productId,jdbcType=VARCHAR}
  </select>
</mapper>
