<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.PurchaseOnlineMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.PurchaseOnline" >
    <id column="online_id" property="onlineId" jdbcType="VARCHAR" />
    <result column="product_purchase_id" property="productPurchaseId" jdbcType="VARCHAR" />
    <result column="purchase_memeber_id" property="purchaseMemeberId" jdbcType="VARCHAR" />
    <result column="purchase_channel" property="purchaseChannel" jdbcType="INTEGER" />
    <result column="purchase_channel_no" property="purchaseChannelNo" jdbcType="VARCHAR" />
    <result column="purchase_status" property="purchaseStatus" jdbcType="VARCHAR" />
    <result column="purchase_time" property="purchaseTime" jdbcType="TIMESTAMP" />
    <result column="callback_content" property="callbackContent" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    online_id, product_purchase_id, purchase_memeber_id, purchase_channel, purchase_channel_no, 
    purchase_status, purchase_time, callback_content
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from t_purchase_online
    where online_id = #{onlineId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from t_purchase_online
    where online_id = #{onlineId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchaseOnline" >
    insert into t_purchase_online (online_id, product_purchase_id, purchase_memeber_id, 
      purchase_channel, purchase_channel_no, purchase_status, 
      purchase_time, callback_content)
    values (#{onlineId,jdbcType=VARCHAR}, #{productPurchaseId,jdbcType=VARCHAR}, #{purchaseMemeberId,jdbcType=VARCHAR}, 
      #{purchaseChannel,jdbcType=INTEGER}, #{purchaseChannelNo,jdbcType=VARCHAR}, #{purchaseStatus,jdbcType=VARCHAR}, 
      #{purchaseTime,jdbcType=TIMESTAMP}, #{callbackContent,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchaseOnline" >
    insert into t_purchase_online
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="onlineId != null" >
        online_id,
      </if>
      <if test="productPurchaseId != null" >
        product_purchase_id,
      </if>
      <if test="purchaseMemeberId != null" >
        purchase_memeber_id,
      </if>
      <if test="purchaseChannel != null" >
        purchase_channel,
      </if>
      <if test="purchaseChannelNo != null" >
        purchase_channel_no,
      </if>
      <if test="purchaseStatus != null" >
        purchase_status,
      </if>
      <if test="purchaseTime != null" >
        purchase_time,
      </if>
      <if test="callbackContent != null" >
        callback_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="onlineId != null" >
        #{onlineId,jdbcType=VARCHAR},
      </if>
      <if test="productPurchaseId != null" >
        #{productPurchaseId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseMemeberId != null" >
        #{purchaseMemeberId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseChannel != null" >
        #{purchaseChannel,jdbcType=INTEGER},
      </if>
      <if test="purchaseChannelNo != null" >
        #{purchaseChannelNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseStatus != null" >
        #{purchaseStatus,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null" >
        #{purchaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="callbackContent != null" >
        #{callbackContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchaseOnline" >
    update t_purchase_online
    <set >
      <if test="productPurchaseId != null" >
        product_purchase_id = #{productPurchaseId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseMemeberId != null" >
        purchase_memeber_id = #{purchaseMemeberId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseChannel != null" >
        purchase_channel = #{purchaseChannel,jdbcType=INTEGER},
      </if>
      <if test="purchaseChannelNo != null" >
        purchase_channel_no = #{purchaseChannelNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseStatus != null" >
        purchase_status = #{purchaseStatus,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null" >
        purchase_time = #{purchaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="callbackContent != null" >
        callback_content = #{callbackContent,jdbcType=VARCHAR},
      </if>
    </set>
    where online_id = #{onlineId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.bus.product.market.common.entity.PurchaseOnline" >
    update t_purchase_online
    set product_purchase_id = #{productPurchaseId,jdbcType=VARCHAR},
      purchase_memeber_id = #{purchaseMemeberId,jdbcType=VARCHAR},
      purchase_channel = #{purchaseChannel,jdbcType=INTEGER},
      purchase_channel_no = #{purchaseChannelNo,jdbcType=VARCHAR},
      purchase_status = #{purchaseStatus,jdbcType=VARCHAR},
      purchase_time = #{purchaseTime,jdbcType=TIMESTAMP},
      callback_content = #{callbackContent,jdbcType=VARCHAR}
    where online_id = #{onlineId,jdbcType=VARCHAR}
  </update>
</mapper>