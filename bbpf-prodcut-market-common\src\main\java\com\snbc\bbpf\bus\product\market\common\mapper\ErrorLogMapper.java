package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.ErrorLog;
import org.apache.ibatis.annotations.Mapper;
/**
 * @author: LiangJB
 * 功能描述: <br>
 * 错误日志表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface ErrorLogMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 删除数据
     * @date: 2020/8/12 13:21
     * @param: errorLogId
     * @return: interesting
     */
    int deleteByPrimaryKey(String errorLogId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     * @param: record
     * @return: 编号
     */
    int insert(ErrorLog errorLog);
    /**
     * @author: LiangJ<PERSON>
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     * @param: record
     * @return: 编号
     */
    int insertSelective(ErrorLog errorLog);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 获取数据
     * @date: 2020/8/12 13:21
     * @param: errorLogId
     * @return: ErrorLog
     */
    ErrorLog selectByPrimaryKey(String errorLogId);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     * @param: record
     * @return: 编号
     */
    int updateByPrimaryKeySelective(ErrorLog errorLog);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     * @param: record
     * @return: 编号
     */
    int updateByPrimaryKey(ErrorLog errorLog);
}
