package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/*
* @author: liangJB
     * 功能描述: <br>
     * 商家服务基础订单列表
     * @date: 2020/8/12 13:13
字段名称	类型	是否必填	描述
purchaseNo	String	是	订单编号
productName	String	是	服务名称
productTypeName	String	是	服务类型
payTypeName	String	是	支付方式

productQuantity	int	是	购买量/台
paymentAmount	Decimal	是	实付金额(元)

purchaseStatus	int	是	具体直参考订单状态0：待支付 1：已支付 2：购买成功 3:已取消
purchaseTime	Date	是	下单时间
payTime	Date	是	支付时间


* */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ServiceShopOrderDto {

    private String purchaseNo;
    private String productName;
    private String payTypeName;
    private String productTypeName;
    private String productgradeUnit;
    private String productGrade;
    private String productImage;
    private String chargeTypeName;

    private Integer productQuantity;
    private Integer purchaseStatus;
    private String purchaseStatusName;

    private BigDecimal paymentAmount;
    private BigDecimal purchaseAmount;
    private BigDecimal discountAmount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime purchaseTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime payTime;


}
