package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ApplyInvoiceParam;

import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: InvoiceDeliveryService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/10/27 11:05
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/27 11:05
 */
public interface InvoiceHandleService {
    /**
     * 增加发票快递信息处理
     *
     * @param applyInvoiceParam
     * @return
     */
    String invoiceDeliveryProcess(ApplyInvoiceParam applyInvoiceParam) throws BusinessException;

    String addInvoice(ApplyInvoiceParam applyInvoiceParam, String invoiceDeliveryId,
                      List<BigDecimal> payAmountList) throws BusinessException, NoSuchAlgorithmException;

    void invoiceTitleProcess(ApplyInvoiceParam applyInvoiceParam);
}
