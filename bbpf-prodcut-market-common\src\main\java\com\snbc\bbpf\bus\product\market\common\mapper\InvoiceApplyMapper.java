package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceListRRQDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoicePurchaseDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceTitleDto;
import com.snbc.bbpf.bus.product.market.common.entity.InvoiceApply;
import com.snbc.bbpf.bus.product.market.common.entity.InvoicePurchase;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.InvoiceParamQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.mapper
 * @ClassName: InvoiceApplyMapper
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票申请持久层
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:21
 */
@Mapper
public interface InvoiceApplyMapper {

    int insertInvoiceRecord(InvoiceApply invoiceApply);

    List<InvoiceListRRQDto> selectInvoiceList(InvoiceParamQuery invoiceParamQuery);

    List<InvoiceApply> selectExportInvoice(InvoiceParamQuery invoiceParamQuery);

    InvoiceDetailDto selectByPrimaryKey(@Param("invoiceApplyId") String invoiceApplyId);

    List<InvoicePurchaseDto> invoiceDetailPurchaseList(@Param("invoiceApplyId") String invoiceApplyId,
                                                       @Param("tenantId") String tenantId,
                                                       @Param("userId") String userId,
                                                       @Param("sortField") String sortField,
                                                       @Param("sequence") String sequence);

    List<InvoicePurchaseDto> invoicePurchaseList(@Param("tenantId") String tenantId,
                                                 @Param("userId") String userId,
                                                 @Param("sortField") String sortField,
                                                 @Param("sequence") String sequence);

    void updateinvoiceStatus(String invoiceApplyId);

    void insertInvoicePurchase(@Param("invoicePurchaseList") List<InvoicePurchase> invoicePurchaseList);

    InvoiceTitleDto getInvoiceTitleByTenantId(String tenantId);


    int invoiceStatusById(String invoiceApplyId);
}
