package com.snbc.bbpf.bus.product.manager.resp.product;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductOrderDto;
import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import lombok.Data;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.resp.product
 * @ClassName: ServiceOrderPage
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: j合作运营需要的订单数据对象
 *      * 以设备ID为查询条件，找出对应的购买服务记录以订单倒叙时间排序
 * @Author: 梁俊斌
 * @CreateDate: 2020/9/10 10:43
 * @UpdateUser: 梁俊斌
 * @UpdateDate: 2020/9/10 10:43
 */
@Data
public class ServiceVemsOrderResp {
    
    private CallResponse head;
    
    private PageInfo<VemsProductOrderDto> body;
}
