<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductCountMapper">
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.dto.order.ProductCountDto">
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_categoryName" jdbcType="VARCHAR" property="productCategoryName" />
    <result column="product_typeName" jdbcType="VARCHAR" property="productTypeName" />
    <result column="charge_typeName" jdbcType="VARCHAR" property="chargeTypeName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="purchaseAmount" jdbcType="DECIMAL" property="purchasesAmount" />
    <result column="discountAmount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="paymentAmount" jdbcType="DECIMAL" property="paymentAmount" />
  </resultMap>

  <sql id="Base_Column_List">
    ts.product_id, product_code,       product_name,sum( tpp.purchase_amount) as purchaseAmount,
    sum(tpp.payment_amount) as paymentAmount,
    sum(tpp.discount_amount) AS discountAmount,
      tpc.value_name     product_categoryName,  tpt.value_name     product_typeName,tct.value_name charge_typeName
  </sql>

  <select id="getSeviceCountList" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountPageQuery" resultMap="BaseResultMap">
      select tps.product_id,
             product_code,
             product_name,create_time,
             tpc.value_name     product_categoryName,
             tpt.value_name     product_typeName,
             tct.value_name charge_typeName,
      IFNULL(purchaseAmount,0) purchaseAmount,
      IFNULL(paymentAmount,0) paymentAmount,
      IFNULL(discountAmount,0) discountAmount from
          t_product_services  tps
              left join (select value_code,value_name from t_dict_value  where type_code='product_category') tpc on tps.product_category=tpc.value_code
              left join (select value_code,value_name from t_dict_value  where type_code='product_type') tpt on tps.product_type=tpt.value_code
              left join (select value_code,value_name from t_dict_value  where type_code='charge_type') tct on tps.charge_type=tct.value_code
              left join
              (select product_id,
                      sum( purchase_amount) as purchaseAmount,
                      sum(payment_amount) as paymentAmount,
                      sum(discount_amount) AS discountAmount
               from  t_product_purchase
               where purchase_status !=-1
               group by product_id) grp on grp.product_id=tps.product_id
              where 1=1
                <if test = "productName != null and productName != ''">
      and product_name like CONCAT ('%',#{productName,jdbcType = VARCHAR},'%')
  </if>
      <if test = "productType != 0">
          and tps.product_type = #{productType,jdbcType=INTEGER}
      </if>
      <if test = "productCategory != 0">
          and tps.product_category = #{productCategory,jdbcType=INTEGER}
      </if>
      <if test = "chargeType != 0">
          and tps.charge_type = #{chargeType,jdbcType=INTEGER}
      </if>
      order by publish_time desc
  </select>
    <select id="getSeviceCountListEX" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountPageQuery" resultMap="BaseResultMap">
        select product_id,
        product_code,
        product_name,create_time,
        tpc.value_name     product_categoryName,
        tpt.value_name     product_typeName,
        tct.value_name charge_typeName,
        purchaseAmount,
        paymentAmount,
        discountAmount
        from (
        SELECT
        tps.product_id,    tps.product_code,    tps.product_name,    tps.product_category,    tps.product_type,
        tps.charge_type, tps.create_time,  sum( tpp.purchase_amount) as purchaseAmount,    sum(tpp.payment_amount) as paymentAmount,
        sum(tpp.discount_amount) AS discountAmount
        FROM
        t_product_services  tps
        left join t_product_purchase tpp on tps.product_id=tpp.product_id
        where purchase_status !=-1
        <if test = "productName != null and productName != ''">
            and product_name like CONCAT ('%',#{productName,jdbcType = VARCHAR},'%')
        </if>
        <if test = "productType != 0">
            and tps.product_type = #{productType,jdbcType=INTEGER}
        </if>
        <if test = "productCategory != 0">
            and tps.product_category = #{productCategory,jdbcType=INTEGER}
        </if>
        <if test = "chargeType != 0">
            and tps.charge_type = #{chargeType,jdbcType=INTEGER}
        </if>
        group by product_id
        ) grp
        left join (select value_code,value_name from t_dict_value  where type_code='product_category') tpc on grp.product_category=tpc.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='product_type') tpt on grp.product_type=tpt.value_code
        left join (select value_code,value_name from t_dict_value  where type_code='charge_type') tct on grp.charge_type=tct.value_code
    </select>
  <select id="getSeviceCountByType" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountQuery" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ProductAllCount">
      select count(DISTINCT tps.product_id) as serviceCount,
      IFNULL(sum( serviceCountAmount),0) as serviceCountAmount,
      IFNULL(sum(serviceCountPayAmount),0) as serviceCountPayAmount,
      IFNULL(sum(serviceCountDisAmount),0) AS serviceCountDisAmount from
      t_product_services  tps
      left join
      (select product_id,
      sum( purchase_amount) as serviceCountAmount,
      sum(payment_amount) as serviceCountPayAmount,
      sum(discount_amount) AS serviceCountDisAmount
      from  t_product_purchase
      where purchase_status !=-1
      group by product_id) grp on grp.product_id=tps.product_id
      where 1=1
    <if test = "productName != null and productName != ''">
      and product_name like CONCAT ('%',#{productName,jdbcType = VARCHAR},'%')
    </if>
    <if test = "productType != 0">
      and tps.product_type = #{productType,jdbcType=INTEGER}
    </if>
    <if test = "productCategory !=0">
      and tps.product_category = #{productCategory,jdbcType=INTEGER}
    </if>
    <if test = "chargeType != 0">
      and tps.charge_type = #{chargeType,jdbcType=INTEGER}
    </if>
  </select>
    <select id="getSeviceMRPCountReport" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCount" resultType ="com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountDto">
        select
        tenant_name tenantName ,report_Month reportMonth,purchase_times purchaseTimes,
        total_fee totalFee,discount_fee discountFee,    actual_fee actualFee
        FROM
        t_purchase_month_report
        where 1=1
        <if test = "shopName != null and shopName != ''">
            and tenant_name like CONCAT ('%',#{shopName,jdbcType = VARCHAR},'%')
        </if>
        <if test="startTime !=null and startTime !=''">
            <![CDATA[ and report_month >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime !=null and endTime !=''">
            <![CDATA[ and report_month <= #{endTime,jdbcType=TIMESTAMP} ]]>
        </if>
        order by report_month desc
    </select>
    <select id="getSeviceMRPCountSum" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCount" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPAllCount">
        select
        sum(total_fee) serviceCountAmount,
        sum(discount_fee) serviceCountDisAmount,
        sum(actual_fee) serviceCountPayAmount
        from t_purchase_month_report
        where 1=1
        <if test = "shopName != null and shopName != ''">
            and tenant_name like CONCAT ('%',#{shopName,jdbcType = VARCHAR},'%')
        </if>
        <if test="startTime !=null and startTime !=''">
            <![CDATA[ and report_month >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime !=null and endTime !=''">
            <![CDATA[ and report_month <=#{endTime,jdbcType=TIMESTAMP}]]>
        </if>
    </select>
    <select id="getSeviceDRPCountReport" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCount" resultType ="com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountDto">
        select
        tenant_name tenantName ,report_day reportMonth,purchase_times purchaseTimes,
        total_fee totalFee,discount_fee discountFee,    actual_fee actualFee
        FROM
        t_purchase_day_report
        where 1=1
        <if test = "shopName != null and shopName != ''">
            and tenant_name like CONCAT ('%',#{shopName,jdbcType = VARCHAR},'%')
        </if>
        <if test="startTime !=null and startTime !=''">
            <![CDATA[ and report_day >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime !=null and endTime !=''">
            <![CDATA[ and report_day < #{endTime,jdbcType=TIMESTAMP} ]]>
        </if>
        order by report_day
    </select>
    <select id="getSeviceDRPCountSum" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCount" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPAllCount">
        select
        sum(total_fee) serviceCountAmount,
        sum(discount_fee) serviceCountDisAmount,
        sum(actual_fee) serviceCountPayAmount
        from t_purchase_day_report
        where 1=1
        <if test = "shopName != null and shopName != ''">
            and tenant_name like CONCAT ('%',#{shopName,jdbcType = VARCHAR},'%')
        </if>
        <if test="startTime !=null and startTime !=''">
            <![CDATA[ and report_day >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime !=null and endTime !=''">
            <![CDATA[ and report_day <#{endTime,jdbcType=TIMESTAMP}]]>
        </if>
    </select>
    <select id="getSeviceRPByTenant" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.RPOrderQuery" resultType="com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountIdDto">
        select count(product_id) as purchaseTimes,
        sum( purchase_amount) as totalFee,
        sum(payment_amount) as actualFee,
        sum(discount_amount) AS discountFee,
        tenant_id tenantId,tenant_name tenantName, CONCAT ("'",#{reportTime,jdbcType = VARCHAR},"'") reportMonth
        from t_product_purchase
        where purchase_status=2
        <if test="startTime !=null and startTime !=''">
            <![CDATA[ and success_time >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime !=null and endTime !=''">
            <![CDATA[ and success_time <#{endTime,jdbcType=TIMESTAMP}]]>
        </if>
        group by tenant_id,tenant_name
    </select>
    <delete id="deleteSeviceRPByTenant" parameterType="java.lang.String" >
        delete from t_purchase_day_report where report_day= #{reportDay,jdbcType=VARCHAR}
    </delete>
    <insert id="insertSeviceRPByTenant" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.RPOrderQuery" >
        insert into t_purchase_day_report(day_report_id,tenant_id,tenant_name,report_day,
        total_fee,discount_fee,actual_fee,purchase_times)
        select UUID() day_report_id,tenant_id,tenant_name,#{reportTime,jdbcType = VARCHAR} report_day,
        sum( purchase_amount) as total_fee,
        sum(discount_amount) AS discount_fee,
        sum(payment_amount) as actual_fee,
        count(product_id) as purchase_times
        from t_product_purchase
        where purchase_status=2
        <if test="startTime !=null and startTime !=''">
            <![CDATA[ and success_time >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime !=null and endTime !=''">
            <![CDATA[ and success_time <#{endTime,jdbcType=TIMESTAMP}]]>
        </if>
        group by tenant_id,tenant_name;
    </insert>
    <delete id="deleteSeviceRPByTenantM" parameterType="java.lang.String" >
        delete from t_purchase_month_report where report_month= #{reportDay,jdbcType=VARCHAR}
    </delete>
    <insert id="insertSeviceRPByTenantM" parameterType="com.snbc.bbpf.bus.product.market.common.vo.product.RPOrderQuery" >
        insert into t_purchase_month_report(month_report_id,tenant_id,tenant_name,report_month,
        total_fee,discount_fee,actual_fee,purchase_times)
        select UUID() month_report_id,tenant_id,tenant_name,#{reportTime,jdbcType = VARCHAR} report_month,
        sum( total_fee) as total_fee,
        sum(discount_fee) AS discount_fee,
        sum(actual_fee) as actual_fee,
        sum(purchase_times) as purchase_times
        from t_purchase_day_report where 1=1
        <if test="startTime !=null and startTime !=''">
        <![CDATA[   and report_day >= #{startTime,jdbcType=TIMESTAMP} ]]>
        </if>
        <if test="endTime !=null and endTime !=''">
            <![CDATA[ and report_day <#{endTime,jdbcType=TIMESTAMP}]]>
        </if>
        group by tenant_id,tenant_name
    </insert>
</mapper>