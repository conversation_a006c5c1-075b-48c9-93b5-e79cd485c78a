package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @ProjectName: product-manager
 * @Package: com.snbc.vems.product.dto.order
 * @ClassName: ProductByConutStatistics
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2021/3/1 10:55
 * @UpdateUser: wangsong
 * @UpdateDate: 2021/3/1 10:55
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
@Builder
public class ProductByConutStatistics {

    
    private Integer totalPurchases;

    
    private Integer remainingCount;

    
    @JsonFormat(pattern = "YYYY-MM-dd")
    private LocalDateTime clearDate;

    
    private Integer clearCount;
}
