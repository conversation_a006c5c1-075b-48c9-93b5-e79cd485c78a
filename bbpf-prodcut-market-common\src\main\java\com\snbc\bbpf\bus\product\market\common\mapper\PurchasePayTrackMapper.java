package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: LiangJB
 * 功能描述: <br>
 * 线下支付表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface PurchasePayTrackMapper {

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(PurchasePayTrack purchasePayTrack);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(PurchasePayTrack purchasePayTrack);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    PurchasePayTrack selectByPrimaryKey(String purchasePayTrackId);
    /**
     * @author: LiangJ<PERSON>
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    List<PurchasePayTrack> selectPayTracks(PurchasePayTrack purchasePayTrack);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKeySelective(PurchasePayTrack purchasePayTrack);

    /**
     * 批量更新
     * @param list
     * @return
     */
    void updateBatchSelective(@Param("list") List<PurchasePayTrack> list);

}
