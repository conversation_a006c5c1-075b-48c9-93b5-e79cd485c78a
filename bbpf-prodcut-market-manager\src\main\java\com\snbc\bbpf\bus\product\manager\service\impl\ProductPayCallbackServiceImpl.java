package com.snbc.bbpf.bus.product.manager.service.impl;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.config.payconfig.AlipayConfig;
import com.snbc.bbpf.bus.product.manager.config.payconfig.WechatConfig;
import com.snbc.bbpf.bus.product.manager.config.payconfig.request.RefundRequest;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.DictValueService;
import com.snbc.bbpf.bus.product.manager.service.OrderService;
import com.snbc.bbpf.bus.product.manager.service.ProductOrderService;
import com.snbc.bbpf.bus.product.manager.service.ProductPayCallbackService;
import com.snbc.bbpf.bus.product.manager.service.ProductPayConfigService;
import com.snbc.bbpf.bus.product.manager.service.ProductPayService;
import com.snbc.bbpf.bus.product.manager.service.PurchasePayTrackService;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderDetailDto;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseOnline;
import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseOnlineMapper;
import com.snbc.pay.Pay;
import com.snbc.pay.entity.response.TradeRefundResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: ProductPayCallbackServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述 回调
 * @Author: wangsong
 * @CreateDate: 2020/10/13 13:03
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/13 13:03
 */
@Service
@Transactional
public class ProductPayCallbackServiceImpl implements ProductPayCallbackService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProductPayCallbackServiceImpl.class);

    @Autowired
    private ProductOrderService productOrderService;
    @Autowired
    private ProductPayService productPayService;
    @Autowired
    private ProductPayConfigService productPayConfigService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private PurchaseOnlineMapper purchaseOnlineMapper;
    @Autowired
    private DictValueService dictValueService;
    @Autowired
    private PurchasePayTrackService purchasePayTrackService;
    @Autowired
    private ProductPurchaseMapper productPurchaseMapper;

    @Override
    public void aliNotify(Map<String, String> map) {
        String timeoutStr = dictValueService.getDictValueByTypeCodeAndValueCode(Constant.TIMEOUT_MIN_KEY,Constant.TIMEOUT_MIN_VALUE_KEY);
        map.remove("payType");
        String payTrackNo = map.get(Constant.OUTTRADENO);
        //根据流水号查询订单号
        PurchasePayTrack record = new PurchasePayTrack();
        record.setPayTrackNo(payTrackNo);
        record.setPayType(Constant.ALIPAY);
        List<PurchasePayTrack> trackList = purchasePayTrackService.selectPayTracks(record);
        if(!CollectionUtils.isEmpty(trackList)){
            PurchasePayTrack track = trackList.get(0);
            String outTradeNo = track.getPurchaseNo();
            //校验订单状态 非待支付状态执行退款操作
            if(!checkOrderStatus(outTradeNo)){
                LOGGER.info("aliNotify checkOrderStatus is not waitpay:{}",outTradeNo);
                refundAliOrder(payTrackNo,map);
                return;
            }
            //创建锁 根据订单号创建
            boolean isLock = productPayService.lock(outTradeNo, 1, Long.parseLong(timeoutStr));
            // 判断是否获取锁
            if (!isLock) {
                //获取所失败
                LOGGER.info("Wechat has paid successfully，Prepare to return the alipay order，The order number：{} ",outTradeNo);
                //退款
                refundAliOrder(payTrackNo,map);
                productPayService.deleteLock(outTradeNo);
                return;
            }
            //更新流水
            track.setPayStatus(Constant.PAY_TRACK_SUCCESS);
            track.setUpdateTime(LocalDateTime.now());
            purchasePayTrackService.updateByPrimaryKeySelective(track);
            //保存回调记录
            PurchaseOnline purchaseOnline = new PurchaseOnline();
            String aliUuid = UUID.randomUUID().toString();
            purchaseOnline.setOnlineId(aliUuid);
            purchaseOnline.setProductPurchaseId(outTradeNo);
            purchaseOnline.setPurchaseChannel(Constant.ALIPAY_CHANNEL);
            purchaseOnline.setCallbackContent(JSON.toJSONString(map));
            purchaseOnline.setPurchaseTime(LocalDateTime.now());
            purchaseOnlineMapper.insertSelective(purchaseOnline);
            ProductOrderDetailDto podd = productOrderService.orderDetail(outTradeNo);
            if (null == podd) {
                LOGGER.error("Alipay query payment flow is null,The order number：{}", outTradeNo);
                return;
            }
            AlipayConfig alipayConfig = JSON.parseObject(productPayConfigService.getAlipayPayConfig(Constant.ALIPAY_CONFIG_ID),AlipayConfig.class);
            boolean verify = this.orderVerify(alipayConfig, map);
            if (!verify) {
                // 验签失败
                LOGGER.error("As alipay callback check fails, the callback parameter order number is abandoned：{}", outTradeNo);
                return;
            }
            LOGGER.info("Alipay payment success, call the public method to update the status of the order and other follow-up work");
            //更新回调结果
            purchaseOnline = new PurchaseOnline();
            purchaseOnline.setOnlineId(aliUuid);
            purchaseOnline.setPurchaseChannelNo(map.get(Constant.TRADENO));
            purchaseOnline.setPurchaseMemeberId(map.get(Constant.BUYERID));
            purchaseOnline.setPurchaseStatus(map.get(Constant.TRADESTATUS));
            purchaseOnlineMapper.updateByPrimaryKeySelective(purchaseOnline);
            //更新后续
            orderService.payConfirm(outTradeNo, Constant.ALIPAY_CHANNEL);
            productPayService.deleteLock(Constant.ALIPAY+"_"+payTrackNo);
            productPayService.deleteLock(Constant.WECHATPAY+"_"+payTrackNo);
        }else{
            throw new BusinessException(Errors.NOPAYTRACKDATA.getMessage(), Errors.NOPAYTRACKDATA.getCode());
        }
    }

    /**
     * 支付宝退款
     * @param payTrackNo
     * @param map
     */
    private void refundAliOrder(String payTrackNo ,Map<String, String> map){
        //退款
        try {
            //退款需要用流水号 不是订单号
            RefundRequest refundRequest = initAliRefundRequest(payTrackNo,map);
            TradeRefundResponse tradeRefundResponse = productPayService.refundAliOrder(refundRequest);
            LOGGER.info("Alipay repeat orders:{},The refund result is:{}",payTrackNo,JSON.toJSONString(tradeRefundResponse));
        } catch (Exception e) {
            LOGGER.error("Alipay repeated payment refund failed，Please refund manually.，The order number:{}",payTrackNo,e);
        }
    }

    /**
     * 微信退款
     * @param payTrackNo
     * @param map
     */
    private void refundWechatOrder(String payTrackNo ,Map<String, String> map){
        //退款
        try {
            RefundRequest refundRequest = initWXRefundRequest(payTrackNo,map);
            TradeRefundResponse tradeRefundResponse = productPayService.refundWechatOrder(refundRequest);
            LOGGER.info("Repeat orders on wechat:{},The refund result:{}",payTrackNo,JSON.toJSONString(tradeRefundResponse));
        } catch (Exception e) {
            LOGGER.error("Wechat repeated payment refund failed，Please refund manually，The order number:{}",payTrackNo,e);
        }
    }
    /**
     * 校验订单状态是否为待支付
     * @param purchaseNo
     * @return
     */
    private boolean checkOrderStatus(String purchaseNo){
        ProductPurchase productPurchase = productPurchaseMapper.selectByPurchaseNo(purchaseNo);
        return null != productPurchase && Constant.PURCHASE_STATUS_WAITPAY == productPurchase.getPurchaseStatus();
    }
    /**
     * 拼装支付宝退款参数
     * @param outTradeNo
     * @param map
     * @return
     */
    private RefundRequest initAliRefundRequest(String outTradeNo, Map<String, String> map) {
        RefundRequest refundRequest = new RefundRequest();
        refundRequest.setOutTradeNo(outTradeNo);
        refundRequest.setRefundAmount(map.get(Constant.TOTALAMOUNT));
        refundRequest.setTotalAmount(map.get(Constant.TOTALAMOUNT));
        return refundRequest;
    }

    @Override
    public void wxNotify(Map<String, String> map) throws Exception {
        map.remove("payType");
        String timeoutStr = dictValueService.getDictValueByTypeCodeAndValueCode(Constant.TIMEOUT_MIN_KEY,Constant.TIMEOUT_MIN_VALUE_KEY);
        String payTrackNo = map.get(Constant.OUTTRADENO);
        //根据流水号查询订单号
        PurchasePayTrack record = new PurchasePayTrack();
        record.setPayTrackNo(payTrackNo);
        record.setPayType(Constant.WECHATPAY);
        List<PurchasePayTrack> trackList = purchasePayTrackService.selectPayTracks(record);
        if(!CollectionUtils.isEmpty(trackList)) {
            PurchasePayTrack track = trackList.get(0);
            String outTradeNo = track.getPurchaseNo();
            //校验订单状态 非待支付状态执行退款操作
            if(!checkOrderStatus(outTradeNo)){
                LOGGER.info("wxNotify checkOrderStatus is not waitpay:{}",outTradeNo);
                refundWechatOrder(payTrackNo,map);
                return;
            }
            //创建锁
            boolean isLock = productPayService.lock(outTradeNo, 1, Long.parseLong(timeoutStr));
            // 判断是否获取锁 没拿到锁  代表其他支付方式以及支付
            if (!isLock) {
                //获取所失败
                LOGGER.info("Alipay has paid successfully，Ready to return the wechat order，The order number：{} ",outTradeNo);
                //退款
                refundWechatOrder(payTrackNo,map);
                productPayService.deleteLock(outTradeNo);
                return;
            }
            //更新流水
            track.setPayStatus(Constant.PAY_TRACK_SUCCESS);
            track.setUpdateTime(LocalDateTime.now());
            purchasePayTrackService.updateByPrimaryKeySelective(track);
            //保存回调记录
            PurchaseOnline purchaseOnline = new PurchaseOnline();
            String wxUuid = UUID.randomUUID().toString();
            purchaseOnline.setOnlineId(wxUuid);
            purchaseOnline.setProductPurchaseId(outTradeNo);
            purchaseOnline.setPurchaseChannel(Constant.WECHATPAY_CHANNEL);
            purchaseOnline.setCallbackContent(JSON.toJSONString(map));
            purchaseOnline.setPurchaseTime(LocalDateTime.now());
            purchaseOnlineMapper.insertSelective(purchaseOnline);
            try {
                ProductOrderDetailDto podd = productOrderService.orderDetail(outTradeNo);
                if (null == podd) {
                    LOGGER.error("Wechat query payment flow is null,The order number：{}", outTradeNo);
                    return;
                }
                WechatConfig wechatConfig = JSON.parseObject(productPayConfigService.getWechatPayConfig(Constant.WECHAT_CONFIG_ID),WechatConfig.class);
                boolean verify = this.wechatOrderVerify(wechatConfig, map);
                if (!verify) {
                    // 验签失败
                    LOGGER.error("Wechat callback check failed，Discard the callback，The order number：{}", outTradeNo);
                    return;
                }
                LOGGER.info("Wechat Pay successfully called the public method to update " +
                        "the status of the order and other follow-up work");
                //更新回调结果
                purchaseOnline = new PurchaseOnline();
                purchaseOnline.setOnlineId(wxUuid);
                purchaseOnline.setPurchaseChannelNo(map.get(Constant.TRANSID));
                purchaseOnline.setPurchaseMemeberId(map.get(Constant.OPENID));
                purchaseOnline.setPurchaseStatus(map.get(Constant.RESULTCODE));
                purchaseOnlineMapper.updateByPrimaryKeySelective(purchaseOnline);
                //更新后续
                orderService.payConfirm(outTradeNo, Constant.WECHATPAY_CHANNEL);
                productPayService.deleteLock(Constant.WECHATPAY+"_"+payTrackNo);
                productPayService.deleteLock(Constant.ALIPAY+"_"+payTrackNo);
            } catch (Exception e) {
                LOGGER.warn("The database is not available for warning,orderId is:{},exception:{}", outTradeNo, e);
                throw new BusinessException(Errors.DB_ERROR.getMessage(), Errors.DB_ERROR.getCode(), e);
            }
        }else{
            throw new BusinessException(Errors.NOPAYTRACKDATA.getMessage(), Errors.NOPAYTRACKDATA.getCode());
        }
    }

    /**
     * 微信退款订单信息组装
     * @param outTradeNo
     * @param map
     * @return
     */
    private RefundRequest initWXRefundRequest(String outTradeNo, Map<String, String> map) {
        RefundRequest refundRequest = new RefundRequest();
        try {
            refundRequest.setOutTradeNo(outTradeNo);
            Double totalFee = Double.valueOf(map.get(Constant.TOTALFEE))/Constant.FENTOYUAN;
            refundRequest.setRefundAmount(Double.toString(totalFee));
            refundRequest.setTotalAmount(Double.toString(totalFee));
            refundRequest.setOutRequestNo(outTradeNo);
        } catch (Exception e) {
            LOGGER.error("Failed to assemble wechat refund order information,order number:{}",outTradeNo,e);
        }
        return refundRequest;
    }

    /****
     * 验证是否是商户订单号，验证是否为支付宝订单
     *
     * @param aliPayConfig
     * @return
     * @throws Exception
     */
    private boolean orderVerify(AlipayConfig aliPayConfig, Map<String, String> resultParams) throws BusinessException {
        // 验证是否是商户订单号，验证是否为支付宝订单
        String signType = aliPayConfig.getSignType();
        if (StringUtils.isBlank(signType)) {
            signType = Constant.RSA;
        }
        Pay pay = new com.snbc.pay.alipay.PayImpl(aliPayConfig.getAppId(), aliPayConfig.getAppPrivateKey(),
                aliPayConfig.getCharset(), aliPayConfig.getPublicKey(), aliPayConfig.getPid(), signType, null, null);
        String payResult;
        try {
            // 返回success 和failed
            payResult = pay.notifyVerify(resultParams);
            return Constant.SUCCESS.equals(payResult);
        } catch (Exception e) {
            LOGGER.error("Alipay callback check failed，The order number：{}", resultParams.get(Constant.OUTTRADENO),e);
            throw new BusinessException(Errors.VERIFY_ERROR.getMessage(), Errors.VERIFY_ERROR.getCode(), e);
        }
    }

    /**
     * 回调验签
     */
    private boolean wechatOrderVerify(WechatConfig entity, Map<String, String> map) throws BusinessException {
        //实例化Pay
        Pay pay = new com.snbc.pay.weixin.PayImpl(entity.getAppId(), null, entity.getAppPrivateKey(), entity.getMchId(),
                null, entity.getCertLocalPath(), entity.getCertPassword(), null);
        boolean verify = false;
        try {
            //回调验签
            String verifyResult = pay.notifyVerify(map);
            if (!Constant.SUCCESS.equals(verifyResult)) {
                LOGGER.info("Wechat callback verification failed. Procedure");
            } else {
                verify = true;
            }
            return verify;
        } catch (Exception e) {
            LOGGER.error("Wechat callback verification failed. Procedure,The order number：{}", map.get(Constant.OUTTRADENO), e);
            throw new BusinessException(Errors.VERIFY_ERROR.getMessage(), Errors.VERIFY_ERROR.getCode(), e);
        }
    }
}
