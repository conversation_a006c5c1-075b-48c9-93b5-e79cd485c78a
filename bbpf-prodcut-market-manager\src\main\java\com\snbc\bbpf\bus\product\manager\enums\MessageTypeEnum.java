/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.enums;

/**
 * @ClassName: MessageTypeEnum
 * 消息类型：从字典中获取的
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/20 13:32
 */
public enum MessageTypeEnum {
    NO1(1, "系统消息"),
    NO2(2, "服务消息"),
    NO3(3, "产品消息"),
    NO4(4, "运营消息"),
    NO5(5, "故障消息");

    private Integer status;
    private String statusName;

    MessageTypeEnum(Integer status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }
    public Integer getStatus() {
        return status;
    }

}
