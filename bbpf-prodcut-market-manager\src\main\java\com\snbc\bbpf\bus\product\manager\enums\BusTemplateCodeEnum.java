package com.snbc.bbpf.bus.product.manager.enums;

/**
 * @ClassName: BusTemplateCodeEnum
 * @Description: 业务模板编码
 * @module: bbpf-bus-system
 * @Author: wjc
 * @date: 2023/3/14
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public enum BusTemplateCodeEnum {
    // 服务开通
    MSG_TYPE_CALLBACK("MSG_TYPE_CALLBACK","MSG_TYPE_CALLBACK", "服务开通"),
    MSG_TYPE_PAY_CONFIRM("MSG_TYPE_PAY_CONFIRM", "MSG_TYPE_PAY_CONFIRM","汇款订单已确认"),
    MSG_TYPE_TIMEOUT("MSG_TYPE_TIMEOUT", "MSG_TYPE_TIMEOUT","订单超时"),
    ;

    //code 和name一样
    private String code;
    // name 用于消息中心业务编码
    private String name;
    private String title;

    BusTemplateCodeEnum(String code , String name,String title) {
        this.code = code;
        this.name = name;
        this.title=title;
    }

    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 通过状态获取名称
     * @Date 16:54 2021/5/20
     * @Param [status]
     **/
    public static String getNameByCode(String code) {
        for (BusTemplateCodeEnum value : BusTemplateCodeEnum.values()) {
            if (code != null && code.equals(value.code)) {
                return value.name;
            }
        }
        return null;
    }
    public static String getTitleByCode(String code) {
        for (BusTemplateCodeEnum value : BusTemplateCodeEnum.values()) {
            if (code != null && code.equals(value.code)) {
                return value.title;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
