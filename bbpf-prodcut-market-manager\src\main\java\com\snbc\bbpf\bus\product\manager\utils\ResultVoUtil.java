package com.snbc.bbpf.bus.product.manager.utils;


import com.snbc.bbpf.bus.product.manager.config.CallResponse;


public final class ResultVoUtil {
    /**
     * 成功
     */
    private static final String SUCCESS = "000000";

    /**
     * 失败
     */
    private static final String FAILURE = "111111";

    /**
     * 私有化工具类 防止被实例化
     * j
     */
    private ResultVoUtil() {}

    /**
     * 成功
     * @return result
     */
    public static CallResponse success() {
        CallResponse result = new CallResponse();
        result.setCode(SUCCESS);
        result.setMessage("操作成功");
        return result;
    }

    public static CallResponse failure() {
        CallResponse result = new CallResponse();
        result.setCode(FAILURE);
        result.setMessage("操作失败");
        return result;
    }
    /**
     * 错误
     * @param code 状态码
     * @param msg 消息
     * @return CallResponse
     */
    public static CallResponse error(String code, String msg) {
        CallResponse result = new CallResponse();
        result.setCode(code);
        result.setMessage(msg);
        return result;
    }

    /**
     * 错误
     * @param msg 错误信息
     * @return CallResponse
     */
    public static CallResponse error(String msg) {
        return error(FAILURE, msg);
    }

}
