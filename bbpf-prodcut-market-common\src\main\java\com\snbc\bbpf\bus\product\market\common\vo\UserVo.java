/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: UserVo
import java.util.List;
import java.util.Map;

/**
 * @ClassName: UserDto
 * @Description: 返回前端的用户信息
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserVo {
    private String userId;

    private String userName;

    private String jobNumber;

    private Integer hasLock;

    private String loginTime;

    private String createTime;

    private String phone;

    private String userDesc;

    private String avatar;

    private String email;

    private Integer userStatus;
    private String userStatusName;

    private Integer hasResign;
    private String hasResignName;

    private List<Map<String,String>> orgInfo;

    private List<Map<String,String>> roleInfo;

}
