package com.snbc.bbpf.bus.product.manager.controller.dict;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.manager.resp.dict.DictTypeListPage;
import com.snbc.bbpf.bus.product.manager.resp.dict.DictTypeListResp;
import com.snbc.bbpf.bus.product.manager.resp.dict.DictTypeResp;
import com.snbc.bbpf.bus.product.manager.resp.dict.DictValueListResp;
import com.snbc.bbpf.bus.product.manager.resp.dict.DictValueResp;
import com.snbc.bbpf.bus.product.manager.resp.dict.DictValueStringResp;
import com.snbc.bbpf.bus.product.manager.service.DictTypeService;
import com.snbc.bbpf.bus.product.manager.service.DictValueService;
import com.snbc.bbpf.bus.product.manager.utils.ResultUtil;
import com.snbc.bbpf.bus.product.manager.utils.ResultVoUtil;
import com.snbc.bbpf.bus.product.market.common.Constant;
import com.snbc.bbpf.bus.product.market.common.dto.dict.DictValueDto;
import com.snbc.bbpf.bus.product.market.common.entity.DictType;
import com.snbc.bbpf.bus.product.market.common.entity.DictValue;
import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典統一rest接口
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/console/v1/dict")
public class DictController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DictController.class);
    private static final Integer PAGESIZE = Constant.TEN;

    @Autowired
    private DictTypeService dictTypeService;
    @Autowired
    private DictValueService dictValueService;



    /**
     * 获取所有的dictType
     *
     * @return
     */
//    
    @RequestMapping(value = "/getAllDictType", method = RequestMethod.GET)
    //@HystrixCommand(fallbackMethod = "defaultStores")
    public DictTypeListResp getAllDictType() throws IOException {
        CallResponse callResponse;
        DictTypeListResp dictTypeListResp = new DictTypeListResp();
        try {
            List<DictType> dictTypeList = dictTypeService.quertAllDictTypeCode();
            callResponse = ResultVoUtil.success();
            dictTypeListResp.setHead(callResponse);
            dictTypeListResp.setBody(dictTypeList);
        } catch (Exception e) {
            LOGGER.error("Failed to get the dictionary list", e);
            callResponse=ResultVoUtil.error("111111","Failed to get the dictionary list");
            dictTypeListResp.setHead(callResponse);
        }
        return dictTypeListResp;
    }

    /**
     * 根据dictType主键查询字典类型
     *
     * @param dictTypeCode
     * @return
     */
//    
    @RequestMapping(value = "/getDictType", method = RequestMethod.GET)
    //@HystrixCommand(fallbackMethod = "defaultStores")
    public DictTypeResp getDictType(
            
            @RequestParam("dictTypeCode") String dictTypeCode) throws IOException {
        CallResponse callResponse;
        DictTypeResp dictTypeResp = new DictTypeResp();
        DictType dictType = dictTypeService.selectByPrimary(dictTypeCode);
        callResponse = ResultVoUtil.success();
        dictTypeResp.setHead(callResponse);
        dictTypeResp.setBody(dictType);
        return dictTypeResp;
    }

    /**
     * 根据map获取dictType
     *
     * @param map
     * @return
     */
//    ", response = DictTypeListPage.class)
    @RequestMapping(value = "/getDictTypeByMap", method = RequestMethod.GET)
    //@HystrixCommand(fallbackMethod = "defaultStores")
    public DictTypeListPage getDictTypeByMap(@RequestParam("map") String map,
                                              String pageNum,
                                              String pageSize) throws IOException {
        CallResponse callResponse;
        DictTypeListPage dictTypeListPage = new DictTypeListPage();
        try {
            int pageSizeInt = PAGESIZE;
            if (pageSize != null && !"".equals(pageSize)) {
                pageSizeInt = Integer.parseInt(pageSize);
            }
            int pageNumInt = Integer.parseInt(pageNum);
            PageHelper.startPage(pageNumInt, pageSizeInt);
            //需要具体的条件组装MAP
            Map<String, Object> convertMap = new HashMap<>();
            List<DictType> dictTypeList = dictTypeService.getDictTypeByMap(convertMap);
            PageInfo<DictType> page = new PageInfo<>(dictTypeList);
            callResponse = ResultVoUtil.success();
            dictTypeListPage.setHead(callResponse);
            dictTypeListPage.setBody(page);
        } catch (Exception e) {
            LOGGER.error("Dictionary query failed:parameter:{},pageNum:{},pageSize:{}",map,pageNum,pageSize, e);
            callResponse= ResultVoUtil.failure();
            dictTypeListPage.setHead(callResponse);
        }
        return dictTypeListPage;
    }

    /**
     * 根据map获取dictValue
     *
     * @param map
     * @return
     */
//    
    @RequestMapping(value = "/getDictValueByMap", method = RequestMethod.GET)
    //@HystrixCommand(fallbackMethod = "defaultStores")
    public DictValueListResp getDictValueByMap(@RequestParam("map") String map) throws IOException {
        CallResponse callResponse;
        DictValueListResp dictValueListResp = new DictValueListResp();
        List<DictValue> dictValueList;
        //需要具体的条件组装MAP
        Map<String, Object> convertMap = new HashMap<>();
        dictValueList = dictValueService.getDictValueByMap(convertMap);
        callResponse = ResultVoUtil.success();
        dictValueListResp.setHead(callResponse);
        dictValueListResp.setBody(dictValueList);
        return dictValueListResp;
    }

    /**
     * 根据dictValue主键查询字典类型
     *
     * @param dictValueId
     * @return
     */
//    
    @RequestMapping(value = "/getDictValue", method = RequestMethod.GET)
    //@Buslog(opration = "查询字典值", param = "dictValueId:$<dictValueId>", target = "dictValue")
    //@HystrixCommand(fallbackMethod = "defaultStores")
    public DictValueResp getDictValue(
            
            @RequestParam("dictValueId") String dictValueId) throws IOException {
        CallResponse callResponse;
        DictValueResp dictValueResp = new DictValueResp();
        try {
            int dictValueIdInt = Integer.parseInt(dictValueId);
            DictValue dictValue = dictValueService.selectByPrimary(dictValueIdInt);
            callResponse = ResultVoUtil.success();
            dictValueResp.setHead(callResponse);
            dictValueResp.setBody(dictValue);
        } catch (Exception e) {
            callResponse= ResultVoUtil.failure();
            dictValueResp.setHead(callResponse);
            LOGGER.error("Failed to query the dictionary value by Id，dictValueId：{}",dictValueId, e);
        }
        return dictValueResp;
    }

    /**
     * 根据DictTypeCode获取dictValueList
     *
     * @param dictTypeCode
     * @return
     */
    
    @RequestMapping(value = "/getDictValueListByDictTypeCode", method = RequestMethod.GET)
    //@HystrixCommand(fallbackMethod = "defaultStores")
    public DictValueListResp getDictValueListByDictTypeCode(
                                                                @RequestParam("dictTypeCode") String dictTypeCode) throws IOException {
        CallResponse callResponse;
        DictValueListResp dictValueListResp = new DictValueListResp();
        try {
            List<DictValue> dictValueList = dictValueService.selectDictValueByTypeCode(dictTypeCode);
            callResponse = ResultVoUtil.success();
            dictValueListResp.setBody(dictValueList);
            dictValueListResp.setHead(callResponse);
        } catch (Exception e) {
            callResponse= ResultVoUtil.failure();
            LOGGER.error("Failed to get the list of values by type,dictTypeCode:{}",dictTypeCode, e);
            dictValueListResp.setHead(callResponse);
        }
        return dictValueListResp;
    }
    /**
     * 根据父节点id获取dictValueList
     *
     * @param parentId
     * @return
     */
    
    @RequestMapping(value = "/getDictValueListByParentId", method = RequestMethod.GET)
    public DictValueListResp getDictValueListByParentId(
                                                            @RequestParam("parentId") Integer parentId) throws IOException {
        CallResponse callResponse;
        DictValueListResp dictValueListResp = new DictValueListResp();
        try {
            List<DictValue> dictValueList = dictValueService.getDictValueByParentId(parentId);
            callResponse = ResultVoUtil.success();
            dictValueListResp.setBody(dictValueList);
            dictValueListResp.setHead(callResponse);
        } catch (Exception e) {
            callResponse= ResultVoUtil.failure();
            LOGGER.error("Failed to set the value list according to the dictionary parent node ID,parentId:{}",parentId, e);
            dictValueListResp.setHead(callResponse);
        }
        return dictValueListResp;
    }
    /**
     * 根据DictTypeCode 和 dictValueCode获取dictValueName
     *
     * @param dictTypeCode
     * @param dictValueCode
     * @return
     */
//    
    @RequestMapping(value = "/getDictValueByTypeCodeAndValueCode", method = RequestMethod.GET)
    //@HystrixCommand(fallbackMethod = "defaultStores")
    public DictValueStringResp getDictValueByTypeCodeAndValueCode(
            
            @RequestParam("dictTypeCode") String dictTypeCode,
            
            @RequestParam("dictValueCode") String dictValueCode) throws IOException {
        CallResponse callResponse;
        DictValueStringResp dictValueStringResp = new DictValueStringResp();
        try {
            String dictValue = dictValueService.getDictValueByTypeCodeAndValueCode(dictTypeCode, dictValueCode);
            callResponse = ResultVoUtil.success();
            dictValueStringResp.setHead(callResponse);
            dictValueStringResp.setBody(dictValue);
        } catch (Exception e) {
            callResponse= ResultVoUtil.failure();
            LOGGER.error("Failed to get dictionary value based on dictionary type Code and dictionary value Code,dictValueCode:{}",dictValueCode, e);
            dictValueStringResp.setHead(callResponse);
        }
        return dictValueStringResp;
    }

    /**
     * 根据DictTypeCode获取dictValueList
     *
     * @param dictTypeCodes
     * @return
     */
    
    @RequestMapping(value = "/getMultipleDictValue", method = RequestMethod.GET)
    public CommonResp multipleDictValues(
                                         @RequestParam("dictTypeCodes") List<String> dictTypeCodes) throws IOException {
        Map<String, List<DictValueDto>> multipleDictValues = dictValueService.getMultipleDictValues(dictTypeCodes);
        return CommonResp.builder().
                body(multipleDictValues)
                .head(ResultVoUtil.success())
                .build();
    }
}
