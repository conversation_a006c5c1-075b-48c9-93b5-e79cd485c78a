/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: OrderServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 订单服务实现类
 * @CreateDate: 2020/8/24 15:21
 * @UpdateDate: 2020/8/24 15:21
 */
package com.snbc.bbpf.bus.product.manager.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.enums.AvailableUnitEnum;
import com.snbc.bbpf.bus.product.manager.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.product.manager.event.CloseOrderEvent;
import com.snbc.bbpf.bus.product.manager.event.SendMsgEvent;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.AuthorityService;
import com.snbc.bbpf.bus.product.manager.service.OrderService;
import com.snbc.bbpf.bus.product.manager.service.PurchaseTrackService;
import com.snbc.bbpf.bus.product.manager.service.RemittanceService;
import com.snbc.bbpf.bus.product.manager.utils.OrderServiceUtils;
import com.snbc.bbpf.bus.product.market.common.dto.order.AuthorityCallBackDto;
import com.snbc.bbpf.bus.product.market.common.entity.ProductAvailable;
import com.snbc.bbpf.bus.product.market.common.entity.ProductDetail;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.entity.ProductServices;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline;
import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductAvailableMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductDetailMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductServicesMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseOfflineMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchasePayTrackMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseTrackMapper;
import com.snbc.bbpf.bus.product.market.common.vo.OrderVo;
import com.snbc.bbpf.bus.product.market.common.vo.ProductOrderDetailVo;
import com.snbc.bbpf.bus.product.market.common.vo.RenewOrderVo;
import com.snbc.bbpf.buslog.pool.ThreadPool;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.snbc.bbpf.bus.product.manager.exception.Errors.ORDER_ISPAYED;
import static com.snbc.bbpf.bus.product.manager.exception.Errors.ORDER_NOEXIST;
import static com.snbc.bbpf.bus.product.manager.exception.Errors.REMITTANCE_EXIST;

/**
 * <AUTHOR>
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: OrderServiceImpl
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 订单服务实现类
 * @CreateDate: 2020/8/24 15:21
 * @UpdateDate: 2020/8/24 15:21
 */

@Service
@Transactional
@EnableAsync
public class OrderServiceImpl implements OrderService {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderServiceImpl.class);
    /**
     * 成功标识
     */
    public static final int SUCCESS = 1;
    //支付状态 暂存
    public static final int PURCHASE_STATUS = -1;
    //试用支付
    public static final int ONE_NUM = 1;

    @Autowired
    private PurchaseOfflineMapper purchaseOfflineMapper;
    @Autowired
    private ProductAvailableMapper productAvailableMapper;
    @Autowired
    private ProductDetailMapper productDetailMapper;
    @Autowired
    private ProductPurchaseMapper productPurchaseMapper;
    @Autowired
    private PurchaseTrackMapper purchaseTrackMapper;
    @Autowired
    private PurchaseTrackService purchaseTrackService;
    @Autowired
    private OrderServiceUtils orderServiceUtils;
    @Autowired
    private RemittanceService remittanceService;
    @Autowired
    private ProductServicesMapper productServicesMapper;
    @Autowired
    private PurchasePayTrackMapper purchasePayTrackMapper;
    @Autowired
    private AuthorityService authorityService;
    @Autowired
    private ApplicationContext applicationContext;
    //日志记录

    /***
     * @Description: 订单取消
     * @Author: wangsong
     * @param :         productPurchaseId
     * @CreateDate: 2020/8/31 15:27
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/31 15:27
     * @return :        boolean
     */
    @Override
    public boolean orderCancel(String purchaseNo,int purchaseStatus) {
        ProductPurchase productPurchase = productPurchaseMapper.selectByPurchaseNo(purchaseNo);
        //订单已经支付
        if (Constant.PURCHASE_STATUS_SUCCESS == productPurchase.getPurchaseStatus()) {
            throw new BusinessException(ORDER_ISPAYED.getMessage(), ORDER_ISPAYED.getCode(), null);
        }
        PurchaseOffline purchaseOffline = purchaseOfflineMapper.selectByPurchaseNo(purchaseNo);
        //已经存在汇款单
        if (null != purchaseOffline) {
            throw new BusinessException(REMITTANCE_EXIST.getMessage(), REMITTANCE_EXIST.getCode(), null);
        }
        LOGGER.info("Cancel order entry：{}",purchaseNo);
        productPurchase.setPurchaseNo(purchaseNo);
        productPurchase.setPurchaseStatus(purchaseStatus);
        productPurchase.setCancelTime(LocalDateTime.now());
        PurchaseTrack purchaseTrack = orderServiceUtils.getPurchaseTrackEntity();
        purchaseTrack.setPurchaseStatus(purchaseStatus);
        purchaseTrack.setPurchaseNo(purchaseNo);
        purchaseTrack.setCancelTime(LocalDateTime.now());
        //插入跟踪表
        purchaseTrackMapper.insertSelective(purchaseTrack);
        int id = productPurchaseMapper.updateByPurchaseNoSelective(productPurchase);

        changePayTrackStatus(purchaseNo);
        return id == SUCCESS;
    }

    /**
     * jiafei 2021-12-09 根据流水号取消未支付的订单
     * @param purchaseNo
     */
    private void changePayTrackStatus(String purchaseNo){
        PurchasePayTrack track = new PurchasePayTrack();
        track.setPurchaseNo(purchaseNo);
        track.setPayStatus(Constant.PAY_TRACK_WAIT);
        List<PurchasePayTrack> trackList = purchasePayTrackMapper.selectPayTracks(track);
        if (!CollectionUtils.isEmpty(trackList)) {
            for(PurchasePayTrack ppt : trackList){
                LOGGER.info("orderCancel PurchasePayTrack :{}，payType={}",ppt.getPurchasePayTrackId(),ppt.getPayType());
                ppt.setPayStatus(Constant.PAY_TRACK_CLOSE);
                ppt.setUpdateTime(LocalDateTime.now());
                //根据支付类型关闭对应流水，异步20230608
                if(Constant.ALIPAY.equalsIgnoreCase(ppt.getPayType())){
                    applicationContext.publishEvent(new CloseOrderEvent(this,ppt.getPayTrackNo(),Constant.ALIPAY));
                }else if(Constant.WECHATPAY.equalsIgnoreCase(ppt.getPayType())){
                    applicationContext.publishEvent(new CloseOrderEvent(this,ppt.getPayTrackNo(),Constant.WECHATPAY));
                }else{
                    LOGGER.warn("orderCancel payType not exist");
                }
            }
            purchasePayTrackMapper.updateBatchSelective(trackList);
        }
    }
    @Override
    public void authorityCallBack(AuthorityCallBackDto authorityCallBackDto) {
        if ("success".equalsIgnoreCase(authorityCallBackDto.getStatus())) {
            ProductPurchase productPurchase = productPurchaseMapper.selectByPurchaseNo(authorityCallBackDto.getPurchaseNo());
            if(productPurchase!=null){
                //授权成功
                productPurchase.setPurchaseStatus(Constant.PURCHASE_STATUS_SUCCESS);
                productPurchase.setSuccessTime(LocalDateTime.now());
                productPurchaseMapper.updateByPurchaseNoSelective(productPurchase);
            }
            //授权回调成功 发消息
            sendMsg(authorityCallBackDto.getPurchaseNo(),BusTemplateCodeEnum.MSG_TYPE_CALLBACK.getCode());
        } else {
            LOGGER.error("authorityCallBack status fail,authorityCallBackDto:{}", JSON.toJSONString(authorityCallBackDto));
        }
    }

    /***
     * @Description: 订单确认
     * @Author: wangsong
     * @param :         productPurchaseId
     * @CreateDate: 2020/9/1 11:25
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/9/1 11:25
     * @return :        boolean
     */
    @Override
    public boolean payConfirm(String purchaseNo, Integer payType) throws BusinessException {
        ProductPurchase productPurchase = productPurchaseMapper.selectByPurchaseNo(purchaseNo);
        if (productPurchase == null) {
            throw new BusinessException(ORDER_NOEXIST.getMessage(), ORDER_NOEXIST.getCode(), null);
        }
        productPurchase.setPurchaseNo(purchaseNo);
        //jiafei2021-12-09 状态变为1 支付完成
        productPurchase.setPurchaseStatus(Constant.PURCHASE_STATUS_PAY);
        productPurchase.setPayType(payType);
        productPurchase.setPayTime(LocalDateTime.now());
        //在支付完成时插入实付金额 2022-02-28 jiafei
        BigDecimal payAmount = productPurchase.getProductPrice()
                .multiply(BigDecimal.valueOf(productPurchase.getProductQuantity()))
                .subtract(productPurchase.getDiscountAmount());
        LOGGER.info("payConfirm payAmount:{}",payAmount);
        productPurchase.setPaymentAmount(payAmount);
        //更新可用表
        boolean needAuthority = orderServiceUtils.updateProductAvailable(purchaseNo);
        LOGGER.info("payConfirm needAuthority= {}",needAuthority);
        //授权 jiafei2021-12-09 续费不需要从新授权
        if (needAuthority) {
            ProductServices product = productServicesMapper.selectByPrimaryKey(productPurchase.getProductId());
            //如果该产品无功能权限 则就不会后续授权操作 当前订单状态就卡在已支付状态 jiafei2021-12-16
            authorityService.authority(product.getProductCode(), purchaseNo, productPurchase.getTenantId());
        } else {
            //不需要授权时状态更新为购买成功 赋值成功时间
            productPurchase.setPurchaseStatus(Constant.PURCHASE_STATUS_SUCCESS);
            //更新成功时间
            productPurchase.setSuccessTime(LocalDateTime.now());
            //不需要授权时 直接发送信息
            sendMsg(purchaseNo,BusTemplateCodeEnum.MSG_TYPE_CALLBACK.getCode());
            int id = productPurchaseMapper.updateByPurchaseNoSelective(productPurchase);
            return id == SUCCESS;
        }
        //更新reids状态0
        LOGGER.debug("Start updating the cache");
        remittanceService.updateProductStatusOnRedis(productPurchase.getProductId(), productPurchase.getPurchaseNo());
        changePayTrackStatus(purchaseNo);
        return  true;
    }

    /***
     * @Description: 状态更新
     * @Author: wangsong
     * @param :         productPurchaseId
     * @CreateDate: 2020/8/31 15:27
     * @UpdateUser: liang
     * @UpdateDate: 2020/8/31 15:27
     * @return :        boolean
     */
    @Override
    public boolean updateStatus(String purchaseNo, Integer status) throws BusinessException {
        ProductPurchase productPurchase = productPurchaseMapper.selectByPurchaseNo(purchaseNo);
        //订单不存在
        if(ObjectUtil.isEmpty(productPurchase)){
            throw new BusinessException(ORDER_NOEXIST.getMessage(), ORDER_NOEXIST.getCode(), null);
        }
        // 校验状态
        if(PURCHASE_STATUS !=productPurchase.getPurchaseStatus()){
            throw new BusinessException(ORDER_ISPAYED.getMessage(), ORDER_ISPAYED.getCode(), null);
        }
        productPurchase.setPurchaseStatus(status);
        //现下支付状态为购买成功调用确认方法
        if (Constant.PURCHASE_STATUS_SUCCESS == status) {
            payConfirm(purchaseNo, Constant.PAY_TYPE_OFFLINE);
        }
        PurchaseTrack purchaseTrack = orderServiceUtils.getPurchaseTrackEntity();
        purchaseTrack.setPurchaseStatus(status);
        purchaseTrack.setPurchaseNo(purchaseNo);
        purchaseTrack.setPurchaseTime(LocalDateTime.now());
        purchaseTrackMapper.insertSelective(purchaseTrack);
        int id = productPurchaseMapper.updateByPurchaseNoSelective(productPurchase);
        return id == SUCCESS;
    }

    /***
     * @Description: 订单提交
     * @Author: wangsong
     * @param :         productPurchaseId
     * @CreateDate: 2020/8/31 15:27
     * @UpdateUser: liang
     * @UpdateDate: 2020/8/31 15:27
     * @return :        boolean
     */
    @Override
    public ProductPurchase order(OrderVo orderVo) throws BusinessException {
        ProductServices product = productServicesMapper.selectByPrimaryKey(orderVo.getProductId());
        //产品服务未发布
        if (product != null && ONE_NUM != product.getProductStatus()) {
            throw new BusinessException(Errors.ORDER_UNPUBLISH.getMessage(), Errors.ORDER_UNPUBLISH.getCode(), null);
        }
        //该租户购买过该产品 不能再次试用
        if (Constant.IS_RENEW_TRYORDER == orderVo.getIsRenew()) {
            List<String> productIds = new ArrayList<>();
            productIds.add(orderVo.getProductId());
            List<ProductPurchase> proList = productPurchaseMapper.selectByTenantAndProductIds(orderVo.getTenantId(), productIds);
            for(ProductPurchase pro : proList){
                if((Constant.PURCHASE_STATUS_PAY == pro.getPurchaseStatus()) || (Constant.PURCHASE_STATUS_SUCCESS == pro.getPurchaseStatus())){
                    throw new BusinessException(Errors.HASTRYDATA.getMessage(), Errors.HASTRYDATA.getCode(), null);
                }
            }
        }
        String productPurchaseId = UUID.randomUUID().toString();
        //如果有详情详细，循环插入详情
        if (!CollectionUtils.isEmpty(orderVo.getProducts())) {
            List<ProductDetail> productDetails = new ArrayList<>();
            //获取子单信息
            List<ProductAvailable> productAvailableList = productAvailableMapper.selectByTenant(orderVo.getTenantId(), orderVo.getProductId());
            for (ProductOrderDetailVo productOrderDetailVo : orderVo.getProducts()) {
                //插入详情
                ProductDetail productDetail = convertProductDetail(product, orderVo, productPurchaseId, productOrderDetailVo,productAvailableList);
                productDetails.add(productDetail);
            }
            //批量插入服务购买详情
            productDetailMapper.insertBatch(productDetails);
        }
        // 拼装 服务购买记录 数据：待支付状态，并生成不重复的订单号
        ProductPurchase productPurchase = orderServiceUtils.getProductPurchase(orderVo, productPurchaseId, Constant.PURCHASE_STATUS_WAITPAY);
        purchaseTrackService.savePurchaseTrack(orderVo, productPurchase.getPurchaseNo(), Constant.PURCHASE_STATUS_WAITPAY);
        //插入订单
        productPurchaseMapper.insert(productPurchase);
        //试用订单直接调用支付完成接口 jiafei2021-12-09
        if (Constant.IS_RENEW_TRYORDER == orderVo.getIsRenew()) {
            ThreadPool.execute(() -> {
                try {
                    //调用支付完成接口
                    payConfirm(productPurchase.getPurchaseNo(), Constant.PAY_TYPE_TRY);
                } catch (BusinessException e) {
                    LOGGER.error("调用支付完成接口失败", e);
                }
            });
        }
        //返回订单ID
        return productPurchase;
    }

    /**
     * wjc1 拼装详情数据，20230608 复杂度优化：<br>
     * 1、将productDetail.setBeginTime提取到最外面<br>
     * 2、AvailableUnit 单位和天数转换，使用枚举<br>
     * 3、productAvailableList 方法循环外面查询
     * @param product
     * @param orderVo
     * @param productPurchaseId
     * @param detailVo
     * @param productAvailableList
     * @return com.snbc.bbpf.bus.product.market.common.entity.ProductDetail
     */
    private static ProductDetail convertProductDetail(ProductServices product, OrderVo orderVo, String productPurchaseId,
                                                      ProductOrderDetailVo detailVo, List<ProductAvailable> productAvailableList) {
        ProductDetail productDetail = new ProductDetail();
        String detailId = UUID.randomUUID().toString();
        productDetail.setDetailId(detailId);
        productDetail.setProductQuantity(ONE_NUM);
        //.getProductPrice() 改为
        productDetail.setPaymentAmount(orderVo.getPaymentAmount());
        productDetail.setPurchaseAmount(orderVo.getPurchaseAmount());
        productDetail.setDiscountAmount(orderVo.getDiscountAmount());
        productDetail.setProductPurchaseId(productPurchaseId);
        productDetail.setCreateTime(LocalDateTime.now());
        productDetail.setProductId(orderVo.getProductId());
        productDetail.setTenantId(detailVo.getTenantId());
        productDetail.setVemsId(detailVo.getVemsId());
        productDetail.setVemsName(detailVo.getVemsName());
        productDetail.setAvailableUnit(detailVo.getAvailableUnit());
        productDetail.setAvailableValue(detailVo.getAvailableValue());
        productDetail.setIsRenew(orderVo.getIsRenew());
        //如果该订单为试用订单 则试用期从后台获取 防篡改 jiafei2021-12-09
        if (Constant.IS_RENEW_TRYORDER == orderVo.getIsRenew()) {
            productDetail.setAvailableValue(product.getProbation());
            productDetail.setAvailableUnit(Constant.DAY_STR);
            detailVo.setAvailableUnit(Constant.DAY_STR);
        }
        //标准服务
        if (StringUtils.isNotBlank(productDetail.getVemsId())) {
            Map<String, ProductAvailable> pMap = getProductAvailableMap(productAvailableList);
            ProductAvailable productAvailable = pMap.get(detailVo.getVemsId());
            // 默认开始时间，当天凌晨
            productDetail.setBeginTime(LocalDate.now().atStartOfDay());
            if (productAvailable != null && productAvailable.getActivationTime() != null) {
                setDualTime(detailVo, productDetail, productAvailable);
            }
            return productDetail;
        }

        boolean isTime = true;
        int name = AvailableUnitEnum.getName(detailVo.getAvailableUnit());
        if (Constant.ZERO == name){
            isTime = false;
            productDetail.setAvailableValue(productDetail.getAvailableValue());
        }else {
            // 单位 * 天数
            productDetail.setAvailableValue(productDetail.getAvailableValue() * name);
        }
        // 开始时间 默认 当天凌晨：今天是 2023 年 6 月 9 日，则调用 LocalDate.now().atStartOfDay() 方法会返回一个表示 2023 年 6 月 9 日 00:00:00 的 LocalDateTime 对象。
        productDetail.setBeginTime(LocalDate.now().atStartOfDay());
        if (!productAvailableList.isEmpty()) {
            if (isTime) {
                setDualTime(detailVo, productDetail, productAvailableList.get(0));
            }
        } else {
            if (isTime) {
                productDetail.setDueTime(LocalDate.now().plusDays(productDetail.getAvailableValue() + ONE_NUM).atStartOfDay());
            }
        }
        return productDetail;
    }

    /**
     * 设置到期时间
     *
     * @param productOrderDetailVo
     * @param productDetail
     * @param productAvailable
     */
    private static void setDualTime(ProductOrderDetailVo productOrderDetailVo, ProductDetail productDetail, ProductAvailable productAvailable) {
        productDetail.setActivationTime(productAvailable.getActivationTime());
        //如果可用表中有到期时间，再原有的基础上增加，如果没有在当期日期上增加
        if (productAvailable.getDueTime().isAfter(LocalDateTime.now())) {
            productDetail.setBeginTime(productAvailable.getBeginTime());
            productDetail.setDueTime(productAvailable.getDueTime().plusDays(productOrderDetailVo.getAvailableValue()));
        } else {
            productDetail.setDueTime(LocalDate.now().plusDays(productDetail.getAvailableValue() + ONE_NUM).atStartOfDay());
        }
    }

    /**
     * 根据订单获取服务可用表：list转map：</br>
     * key:vemsId;value:productAvailable
     *
     * @param productAvailableList
     * @return
     */
    private static Map<String, ProductAvailable> getProductAvailableMap(List<ProductAvailable> productAvailableList) {
        Map<String, ProductAvailable> pMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(productAvailableList)) {
            pMap = productAvailableList.stream().collect(Collectors.toMap(ProductAvailable::getVemsId, item -> item));
        }
        return pMap;
    }

    /**
     * 服务续费
     *
     * @param orderVo
     * @return
     */
    @Override
    public ProductPurchase renew(RenewOrderVo orderVo) {

        String productPurchaseId = UUID.randomUUID().toString();
        Integer availableValue = Integer.parseInt(orderVo.getProductGrade());
        //jiafei2021-12-09 修改为不用vemids获取 仅通过productid获取购买产品
        List<ProductAvailable> productAvailables = productAvailableMapper.selectByTenant(orderVo.getTenantId(), orderVo.getProductId());

        if (productAvailables != null && !productAvailables.isEmpty()) {
            for (ProductAvailable productAvailable : productAvailables) {
                //插入详情
                ProductDetail productDetail = getRenewProductDetail(orderVo, productPurchaseId, availableValue, productAvailable);
                productDetailMapper.insert(productDetail);

            }
        }
        ProductPurchase productPurchase = orderServiceUtils.getProductPurchase(orderVo, productPurchaseId, Constant.PURCHASE_STATUS_WAITPAY);
        purchaseTrackService.savePurchaseTrack(orderVo, productPurchase.getPurchaseNo(), Constant.PURCHASE_STATUS_WAITPAY);
        //插入订单
        productPurchaseMapper.insert(productPurchase);
        //返回订单ID
        return productPurchase;
    }


    private static ProductDetail getRenewProductDetail(RenewOrderVo orderVo, String productPurchaseId,
                                                       Integer availableValue, ProductAvailable productAvailable) {
        ProductDetail productDetail = new ProductDetail();
        String detailId = UUID.randomUUID().toString();
        productDetail.setDetailId(detailId);
        productDetail.setProductPurchaseId(productPurchaseId);
        //jiafei2021-12-09 修改为单位由前台传递获取
        productDetail.setAvailableUnit(orderVo.getAvailableUnit());
        productDetail.setAvailableValue(availableValue);
        productDetail.setPaymentAmount(orderVo.getProductPrice());
        productDetail.setDiscountAmount(orderVo.getDiscountAmount());
        productDetail.setPurchaseAmount(orderVo.getProductPrice());
        productDetail.setProductId(orderVo.getProductId());
        productDetail.setProductQuantity(ONE_NUM);
        productDetail.setCreateTime(LocalDateTime.now());
        productDetail.setTenantId(productAvailable.getTenantId());
        productDetail.setVemsId(productAvailable.getVemsId());
        productDetail.setVemsName(productAvailable.getVemsName());
        productDetail.setActivationTime(productAvailable.getActivationTime());
        productDetail.setIsRenew(orderVo.getIsRenew());
        //设置到期时间，当服务单位为天或天/台是才设置
        ////jiafei2021-12-09 根据前台传递过来的单位计算续费天数
        boolean isAvailable = false;
        if (Constant.DAY_STR.equals(orderVo.getAvailableUnit()) || Constant.DAY_STR2.equals(orderVo.getAvailableUnit())) {
            isAvailable = true;
        } else if (Constant.MONTH_STR.equals(orderVo.getAvailableUnit())) {
            isAvailable = true;
            availableValue = availableValue * Constant.MONTH_NUM;
        } else if (Constant.SEASON_STR.equals(orderVo.getAvailableUnit())) {
            isAvailable = true;
            availableValue = availableValue * Constant.SEASON_NUM;
        } else if (Constant.YEAR_STR.equals(orderVo.getAvailableUnit())) {
            isAvailable = true;
            availableValue = availableValue * Constant.YEAR_NUM;
        }else{
            LOGGER.warn("unit is not find");
        }
        if (isAvailable) {
            productDetail.setAvailableValue(availableValue);
            productDetail.setBeginTime(LocalDate.now().atStartOfDay());
            //如果可用表中有到期时间，再原有的基础上增加，如果没有在当期日期上增加
            if (productAvailable.getDueTime().isBefore(LocalDateTime.now())) {
                productDetail.setDueTime(LocalDate.now().plusDays((long)availableValue + ONE_NUM).atStartOfDay());
            } else {
                productDetail.setBeginTime(productAvailable.getDueTime());
                //jiafei2021-12-09 未到期续费天数不加1
                productDetail.setDueTime(productAvailable.getDueTime().plusDays(availableValue));
            }
        } else {
            productDetail.setBeginTime(LocalDate.now().atStartOfDay());
        }
        return productDetail;
    }

    @Override
    public void sendMsg(String purchaseNo,String msgType){
        applicationContext.publishEvent(new SendMsgEvent(this, purchaseNo, msgType));
    }

}
