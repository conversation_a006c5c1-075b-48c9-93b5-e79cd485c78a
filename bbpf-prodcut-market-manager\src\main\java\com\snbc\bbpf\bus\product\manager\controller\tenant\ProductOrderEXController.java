/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.controller.rrq
 * @ClassName: ProductOrderController
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 人人取运营系统产品服务订单接口
 * @Author: ouyang，liangjunbin
 * @CreateDate: 2020/8/24 14:55
 * @UpdateUser: ouyang
 * @UpdateDate: 2020/10/10 14:55
 */
package com.snbc.bbpf.bus.product.manager.controller.tenant;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.manager.service.impl.PurchasedOrderFactory;
import com.snbc.bbpf.bus.product.manager.utils.ResultVoUtil;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProducedOrderDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/console/v1/tenant/productorder")
public class ProductOrderEXController {
    @Autowired
    private PurchasedOrderFactory purchasedOrderFactory;

    /***
     * @Description: 已购产品订单分页列表
     * @Author: wangsong
     * @param :         tenantId
     * @param :         productId
     * @param :         pageNum
     * @param :         pageSize
     * @param :         productType
     * @CreateDate: 2021/2/25 19:01
     * @UpdateUser: wangsong
     * @UpdateDate: 2021/2/25 19:01
     * @return :        CommonResp
     */

    @GetMapping(value = "/purchasedOrderWithPage")
    public CommonResp<PageInfo<ProducedOrderDto>> purchasedOrderWithPage(
            @Valid @RequestParam(value = "tenantId") String tenantId,

            @Valid @RequestParam(value = "productId") String productId,

            @Valid @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,

            @Valid @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        CommonResp commonResp = new CommonResp<>();
        PageInfo<Object> objectPageInfo = purchasedOrderFactory.purchasedOrderWithPage(tenantId, productId, pageNum, pageSize);
        commonResp.setHead(ResultVoUtil.success());
        commonResp.setBody(objectPageInfo);
        return commonResp;
    }

    /***
     * @Description: 查询人人取已购服务剩余使用次数/天数信息
     * @Author: wangsong
     * @param :         tenantId
     * @param :         productId
     * @param :         productType
     * @CreateDate: 2021/3/1 19:05
     * @UpdateUser: wangsong
     * @UpdateDate: 2021/3/1 19:05
     * @return :        CommonResp<java.lang.Object>
     */

    @GetMapping(value = "/productRemainingUse", produces = {"application/json"})
    public CommonResp<Object> productRemainingUse( String tenantId,
                                                   String productId) {
        CommonResp commonResp = new CommonResp<>();
        Object o = purchasedOrderFactory.productRemainingUse(tenantId, productId);
        commonResp.setHead(ResultVoUtil.success());
        commonResp.setBody(o);
        return commonResp;
    }
}
