package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/*
* @author: liangJB
     * 功能描述: <br>
     * 产品服务市场类
     * @date: 2020/8/12 13:13
* 字段名称	类型	是否必填	描述
productCode	String	是	服务编号
productName	String	是	服务名称
productBrief	String	是	服务简介
productImage	String	是	服务图片
productTypeName	String	是	服务类型
chargeTypeName	String	是	收费方式
isNewProduct Integer	是	是否属于新服务 0不是，1是服务
* */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ServicePDto {

    private String productId;

    private String productCode;

    private String productName;

    private String productBrief;

    private String productImage;

    private Integer isNewProduct;

    private String defaultPrice;

    private String defaultUnitCode;

    private String defaultGrade;

    private String defaultPriceUnit;

    private String productTypeName;
    // 是否购买过服务：购买过就没有试用权限了 wjc add
    private boolean hasPurchaseProduct;
    // 试用期限
    private int probation;
}
