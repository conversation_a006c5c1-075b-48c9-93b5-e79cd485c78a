package com.snbc.bbpf.bus.product.manager.consumer;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.service.OrderService;
import com.snbc.bbpf.bus.product.manager.service.ProductPayCallbackService;
import com.snbc.bbpf.bus.product.market.common.dto.order.AuthorityCallBackDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Argument;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 回调消息消费MQ
 * @ClassName NotifyConsumer
 * @date 2019-08-15 11:09
 */
@Slf4j
@Component
public class NotifyConsumer {

    public static final String TENANT_AUTHORITY ="tenant_product_authority" ;
    public static final String TENANT_AUTHORITY_EXCHANGE ="tenant_product_authority_exchange" ;
    public static final String TENANT_AUTHORITY_ROUTINGKEY ="tenant_product_authority_routingkey" ;
    //队列中消息的过期时间
    private static final String TTL = "300000";
    @Autowired
    private ProductPayCallbackService productPayCallbackService;
    @Autowired
    private OrderService orderService;
    /**
     * 创建固定线程池
     */
    private ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    @RabbitListener(queues = Constant.PRODUCT_PAY)
    public void consumerMessage(Message message) {
        log.debug("rabbitMQ consumerMes sage request param:{}", message);
        executorService.execute(() -> poolTranConsumer(new String(message.getBody(),StandardCharsets.UTF_8)));
    }

    /**
     * 功能描述: <br>
     * 〈事物托管spring〉
     *
     * @throws
     * @param: message
     * @return: void
     * @author: gs
     * @date: 2019-08-27 19:06
     */
    public void poolTranConsumer(String message) {
        Map<String, String> map = (Map<String, String>) JSON.parse(message);
        try {
            if(StringUtils.isBlank(map.get(Constant.OUTTRADENO))){
                log.warn("data invalid out_trade_no is null={} ",JSON.toJSONString(map));
                return;
            }
            switch (map.get("payType")) {
                case Constant.ALIPAY:
                    productPayCallbackService.aliNotify(map);
                    break;
                case Constant.WECHATPAY:
                    productPayCallbackService.wxNotify(map);
                    break;
                default:
                    log.error("Incorrect payment type occurred in callback processing:{}",map.get("payType"));
                    break;
            }
        }  catch (Exception e) {
            log.error("The callback processing is abnormal,message:{}",e.getMessage(), e);
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            key = TENANT_AUTHORITY_ROUTINGKEY,
            value = @Queue(value = TENANT_AUTHORITY, arguments = @Argument(name = "x-message-ttl", value = TTL, type = "int")),
            exchange = @Exchange(value = TENANT_AUTHORITY_EXCHANGE, type = ExchangeTypes.TOPIC)))
    public void listenActivationTimeMQ(Message message) {
        log.debug("The message listening to the MQ queue is:{}", new String(message.getBody(),StandardCharsets.UTF_8));

        try {
            String json = new String(message.getBody(), StandardCharsets.UTF_8);
            ObjectMapper objectMapper = new ObjectMapper();
            //反序列化
            objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
            AuthorityCallBackDto auth = objectMapper.readValue(json, AuthorityCallBackDto.class);

            orderService.authorityCallBack(auth);
        }catch (Exception ex){
            log.error("The product service authorization callback is abnormal,message:{}",message,ex);
        }

    }
}

