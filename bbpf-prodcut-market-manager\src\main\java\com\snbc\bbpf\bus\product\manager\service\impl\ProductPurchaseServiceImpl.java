package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.service.ProductPurchaseService;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @ProjectName: product-manager
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: PurchasedProductOrderByFrequency
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 按次数服务购买订单信息
 * @Author: wangsong
 * @CreateDate: 2021/2/25 18:29
 * @UpdateUser: wangsong
 * @UpdateDate: 2021/2/25 18:29
 */
@Service
@Transactional
public class ProductPurchaseServiceImpl implements ProductPurchaseService {
    @Autowired
    private ProductPurchaseMapper productPurchaseMapper;

    @Override
    public ProductPurchase selectByPurchaseNo(String purchaseNo) {
        return productPurchaseMapper.selectByPurchaseNo(purchaseNo);
    }
}
