/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @ClassName: PermissionVo
 * PermissionVo对象
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class PermissionVo extends BasePermissonVo {

    private String parentName;

    private String remarks;

    private String permissionIcon;

    private Integer permissionLevel;

    private Integer sysType;

}
