package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ProjectName: product-manager
 * @Package: com.snbc.vems.product.dto.order
 * @ClassName: ProducedOrderDto
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 按天数服务类型订单
 * @Author: wangsong
 * @CreateDate: 2021/2/25 17:36
 * @UpdateUser: wangsong
 * @UpdateDate: 2021/2/25 17:36
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProducedOrderDto {
    
    private String purchaseNo;

    
    private String availableValue;

    
    private BigDecimal purchaseAmount;

    
    private BigDecimal discountAmount;

    
    private BigDecimal paymentAmount;

    
    private String payType;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchaseTime;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;
}
