package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class BaseOrderVo {
    //服务名称

    @NotEmpty(message = "服务名称不能为空")
    private String  productName;
    //服务编码

    @NotEmpty(message = "服务编码不能为空")
    private String productCode;
    //服务id

    @NotEmpty(message = "产品服务id不能为空")
    private String productId;
    //产品阶梯

    @NotEmpty(message = "产品阶梯不能为空")
    private String productGrade;

    //产品数量

    @NotNull(message = "产品数量不能为空")
    private Integer productQuantity;
    //支付类型

    private Integer payType;
    //订单金额

    @NotNull(message = "订单金额不能为空")
    private BigDecimal purchaseAmount;
    //实付金额

    @NotNull(message = "实付金额不能为空")
    private BigDecimal paymentAmount;
    //优惠金额

    @NotNull(message = "优惠金额不能为空")
    private BigDecimal discountAmount;
    //优惠金额

    @NotNull(message = "产品单价不能为空")
    private BigDecimal productPrice;
    //是否续费

    @NotNull(message = "是否续费不能为空")
    private Integer isRenew;
    /**
     * 租户id
     */

    @NotEmpty(message = "租户Id不能为空")
    private String tenantId;
    private String tenantName;
    /**
     * 租户id
     */

    @NotEmpty(message = "用户不能为空")
    private String userId;

    private String remark;
}
