package com.snbc.bbpf.bus.product.market.common.dto.invoice;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.vo
 * @ClassName: InvoiceDetailVo
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票详情vo
 * @Author: wangsong
 * @CreateDate: 2020/8/25 11:28
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/25 11:28
 */
@Data
@NoArgsConstructor
public class InvoiceDetailDto extends  InvoiceDetailBaseDto {

    
    private String invoiceTitle;

    
    private String invoiceCode;

    
    private String invoiceContent;

    
    private String applyUser;

    
    private String invoiceAmount;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date applyTime;

    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date invoiceTime;

    
    private String mail;

    
    private String tenantName;

    
    private String receiverName;

    
    private String receiverTel;

    
    private String invoiceCarrier;

    
    private String invoiceType;

    
    private String applyType;

    
    private String invoiceStatus;
}
