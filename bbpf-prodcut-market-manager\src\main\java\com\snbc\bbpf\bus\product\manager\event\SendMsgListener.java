/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.event;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.config.NumberConstant;
import com.snbc.bbpf.bus.product.manager.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.BbpfTenantConfigService;
import com.snbc.bbpf.bus.product.manager.utils.SendMsgUtil;
import com.snbc.bbpf.bus.product.market.common.entity.Message;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.entity.ProductServices;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductServicesMapper;
import com.snbc.bbpf.bus.product.market.common.vo.User4ProductVo;
import com.snbc.bbpf.commons.dates.DateFormatUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: CloseOrderListener
 * 发送消息监听
 * @module: si-bbpf-product-market
 * @Author: wjc1
 * @date: 2023/6/8 15:30
 */
@Component
@Slf4j
@AllArgsConstructor
public class SendMsgListener {
    private final ProductServicesMapper productServicesMapper;
    private final ProductPurchaseMapper productPurchaseMapper;
    private final BbpfTenantConfigService bbpfTenantConfigService;
    private final RedisTemplate redisTemplate;

    @Async
    @EventListener
    public void onApplicationEvent(@NotNull SendMsgEvent event) {
        String purchaseNo=event.getPurchaseNo();
        String msgType=event.getMsgType();
        log.debug("async sendMsg purchaseNo={}, msgType={}",purchaseNo,msgType);
        ProductPurchase productPurchase = productPurchaseMapper.selectByPurchaseNo(purchaseNo);
        //20230629 wjc 如果为空则new 个新对象
        ProductPurchase optional = Optional.ofNullable(productPurchase).orElseGet(ProductPurchase::new);
        ProductServices productServices = productServicesMapper.selectByPrimaryKey(optional.getProductId());
        if (null != productServices) {
            //20230629 wjc 如果 userId和tenantId为空，接口会返回失败： 用户不存在
            CommonResp<List<User4ProductVo>> result = bbpfTenantConfigService.getUserByUserIdTenantId(optional.getTenantId(), Collections.singletonList(optional.getUserId()));
             log.info("sendMsg bbpf tenantConfigService.getUserByUserIdTenantId result= {}", JSON.toJSONString(result));
            if (Errors.SUCCESS.getCode().equals(result.getHead().getCode())) {
                //防同一时间发送
                checkTimeMillis(purchaseNo);
                //组装信息，发送
                installMsg(msgType, result.getBody().get(0), optional.getTenantId(), productServices.getProductName(),optional.getUserId());
            }
        }
    }
    /**
     * 防同一时间发送，放入redis 1秒失效的缓存 能获取说明同一秒发送，线程睡1秒
     * @param purchaseNo
     */
    private void checkTimeMillis(String purchaseNo){
        String key = purchaseNo+Constant.MSG_TIME;
        String nowTime =  getNewFormatDateString(Date.from(Instant.now()));
        if(null != redisTemplate.opsForValue().get(key)){
            log.info("checkTimeMillis value:{}",redisTemplate.opsForValue().get(key));
            try {
                Thread.sleep(NumberConstant.NO_ONE_K);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("sendMsg checkTimeMillis currentThread.sleep error:",e);
            }
            redisTemplate.opsForValue().set(key,getNewFormatDateString(Date.from(Instant.now())), NumberConstant.NO_ONE, TimeUnit.SECONDS);
        }else{
            redisTemplate.opsForValue().set(key,nowTime, NumberConstant.NO_ONE, TimeUnit.SECONDS);
        }
        log.info("sendMsg checkTimeMillis:{}", redisTemplate.opsForValue().get(key));
    }
    /**
     * yyyy-MM-dd HH:mm:ss:SSS
     * @param date 日期
     * @return yyyy-MM-dd HH:mm:ss:SSS
     */
    private static String getNewFormatDateString(Date date) {
        return DateFormatUtil.format(date, DateFormatUtil.DATETIME_MS_FORMATTER);
    }
    /**
     * 组装消息，调消息中心
     * @param msgType
     * @param user
     * @param tenantId
     * @param productName
     * @param userId
     */
    private void installMsg(String msgType,User4ProductVo user,String tenantId,String productName,String userId){
        //20230317 wjc update
        String templateCode = BusTemplateCodeEnum.getNameByCode(msgType);
        String msgTitle = BusTemplateCodeEnum.getTitleByCode(msgType);
        if(StringUtils.isNotBlank(templateCode)){
            Message message=Message.builder().receiveNos(new String[]{userId})
                    .templateCode(templateCode)
                    .templateParamJsonArr(convertTemplateParamJsonArr(user.getUserName(),productName))
                    .msgTitle(msgTitle)
                    .receiverGroup(tenantId).build();
            log.debug("sendSysMessage message:{}",JSON.toJSONString(message));
            SendMsgUtil.sendSysMessage(message);
            if(StringUtils.isNotBlank(user.getPhone())){
                message.setReceiverPhone(new String[]{user.getPhone()});
                log.debug("sendShortMessage message:{}",JSON.toJSONString(message));
                SendMsgUtil.sendShortMessage(message);
            }
        }
    }

    /**
     * 转换模板参数
     * @param userName
     * @param productName
     * @return
     */
    private static String[] convertTemplateParamJsonArr(String userName,String productName){
        Map<String,String> templateParamMap=new HashMap<>();
        templateParamMap.put("userName", userName);
        if(StringUtils.isNotBlank(productName)){
            templateParamMap.put("productName", productName);
        }
        return new String[]{new Gson().toJson(templateParamMap)};
    }
}
