/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.utils;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Map;

/**
 * @ClassName: CurrentUser
 * 获取当前用户信息类
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Component
@Slf4j
public final class CurrentUser {

    private static final String REDISTENANTLISTMAP = "RedisTenantListMap";
    private static final String TENANTNAME = "tenantName";
    @Autowired
    private RedisTemplate redisTemplate;

    private static final CurrentUser currentUser = new CurrentUser();

    private CurrentUser() {
    }

    @PostConstruct
    private void init() {
        currentUser.redisTemplate = this.redisTemplate;
    }

    /**
     * 获取用户id
     * 如果用户ID存在直接获取，如果没有默认为1
     *
     * @return
     */
    public static String getUserId() {
        HttpServletRequest request = getRequest();
        return request.getHeader(Constant.COMHEADUSERID);
    }

    private static HttpServletRequest getRequest() {
        RequestAttributes req = RequestContextHolder.getRequestAttributes();
        return ((ServletRequestAttributes) req).getRequest();
    }

    /**
     * 获取用户名称
     *
     * @return
     */
    public static String getUserName() {
        HttpServletRequest request = getRequest();
        String userName=request.getHeader(Constant.COMHEADUSERNAME);
        try {
            userName=URLDecoder.decode(userName,"utf-8");
            log.debug("get userName:{}", userName);
        } catch (UnsupportedEncodingException e) {
            log.error("getUserName error",e);
        }
        return userName;
    }
    /**
     * 获取用户登录的端
     *
     * @return
     */
    public static String getSysType() {
        HttpServletRequest request = getRequest();
        return request.getHeader(Constant.CURRENTSYSTYPE);
    }
    /**
     * 获取租户id
     * @return
     */
    public static String getTenantId() {
        HttpServletRequest request = getRequest();
        return request.getHeader(Constant.COMHEATENANTID);
    }

    /**
     * 获取租户名称
     *
     * @return
     */
    public static String getTenantName() {
        String tenantId = getTenantId();
        try {
            Map<Object, Object> redisTenant = (Map) JSON.parse(currentUser.redisTemplate.opsForHash().get(REDISTENANTLISTMAP, tenantId).toString());
            return redisTenant.get(TENANTNAME).toString();
        } catch (Exception e) {
            log.error("CurrentUser get tenantName error", e);
            return "";
        }
    }

    /**
     * 获取租户名称
     *
     * @return
     */
    public static String getTenantName(String tenantId) {
        try {
            Map<Object, Object> redisTenant = (Map) JSON.parse(currentUser.redisTemplate.opsForHash().get(REDISTENANTLISTMAP, tenantId).toString());
            return redisTenant.get(TENANTNAME).toString();
        } catch (Exception e) {
            log.error("CurrentUser get tenantName error", e);
            return "";
        }
    }
}
