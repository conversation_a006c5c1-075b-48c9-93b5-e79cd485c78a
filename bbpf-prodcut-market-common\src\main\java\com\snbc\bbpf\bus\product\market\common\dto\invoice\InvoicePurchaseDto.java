package com.snbc.bbpf.bus.product.market.common.dto.invoice;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.dto.invoice
 * @ClassName: InvoiceOrderDto
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票中的服务购买记录dto
 * @Author: wangsong
 * @CreateDate: 2020/8/25 17:40
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/25 17:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoicePurchaseDto {


    private String productPurchaseId;


    private String purchaseNo;


    private String productName;


    private Double purchaseAmount;


    private Double discountAmount;


    private Double paymentAmount;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime purchaseTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime payTime;


}
