<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductPayConfigMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.ProductPayConfig" >
    <id column="product_payconfig_id" property="productPayconfigId" jdbcType="VARCHAR" />
    <result column="product_payconfig_type" property="productPayconfigType" jdbcType="INTEGER" />
    <result column="product_payconfig_content" property="productPayconfigContent" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    product_payconfig_id, product_payconfig_type, product_payconfig_content
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from t_product_payconfig
    where product_payconfig_id = #{productPayconfigId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from t_product_payconfig
    where product_payconfig_id = #{productPayconfigId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductPayConfig" >
    insert into t_product_payconfig (product_payconfig_id, product_payconfig_type, 
      product_payconfig_content)
    values (#{productPayconfigId,jdbcType=VARCHAR}, #{productPayconfigType,jdbcType=INTEGER}, 
      #{productPayconfigContent,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductPayConfig" >
    insert into t_product_payconfig
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="productPayconfigId != null" >
        product_payconfig_id,
      </if>
      <if test="productPayconfigType != null" >
        product_payconfig_type,
      </if>
      <if test="productPayconfigContent != null" >
        product_payconfig_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="productPayconfigId != null" >
        #{productPayconfigId,jdbcType=VARCHAR},
      </if>
      <if test="productPayconfigType != null" >
        #{productPayconfigType,jdbcType=INTEGER},
      </if>
      <if test="productPayconfigContent != null" >
        #{productPayconfigContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductPayConfig" >
    update t_product_payconfig
    <set >
      <if test="productPayconfigType != null" >
        product_payconfig_type = #{productPayconfigType,jdbcType=INTEGER},
      </if>
      <if test="productPayconfigContent != null" >
        product_payconfig_content = #{productPayconfigContent,jdbcType=VARCHAR},
      </if>
    </set>
    where product_payconfig_id = #{productPayconfigId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductPayConfig" >
    update t_product_payconfig
    set product_payconfig_type = #{productPayconfigType,jdbcType=INTEGER},
      product_payconfig_content = #{productPayconfigContent,jdbcType=VARCHAR}
    where product_payconfig_id = #{productPayconfigId,jdbcType=VARCHAR}
  </update>
</mapper>