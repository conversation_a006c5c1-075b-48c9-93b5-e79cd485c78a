/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.market.common.vo.User4ProductVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import java.util.List;

/**
 * bbpf-bus-tenantconfig feign类
 */
@FeignClient(value = "bbpf-bus-tenantconfig")
public interface BbpfTenantConfigService {

    /**
     * 调用多嘱咐配置 创建租户管理员账号
     *
     * @param userId
     * @return
     */
    @PostMapping(value = "/api/user/getUserByUserIdTenantId")
    CommonResp<List<User4ProductVo>> getUserByUserIdTenantId(@RequestHeader ( name = "tenantId" ) String tenantId ,
                                                             @RequestBody List<String> userIds);

    /**
     * 调用多嘱咐配置 根据租户id查询租户管理员：role=-1的那个userId
     *
     * @return CommonResp<Object>
     */
    @GetMapping(value = "/api/role/getAdminUserIdByTenantId")
    CommonResp<Object> getAdminUserIdByTenantId(@RequestHeader(name = "tenantId") String tenantId);

}
