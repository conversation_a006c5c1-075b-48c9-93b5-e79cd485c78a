package com.snbc.bbpf.bus.product.manager.resp.vems;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 售货机响应
 * @Author: wangsong
 * @CreateDate: 2020/8/24 14:55
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 14:55
 */

@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2017-04-17T01:33:03.272Z")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HeaderResp {


    @JsonProperty("code")
    private String code = null;


    @JsonProperty("message")
    private String message = null;
}
