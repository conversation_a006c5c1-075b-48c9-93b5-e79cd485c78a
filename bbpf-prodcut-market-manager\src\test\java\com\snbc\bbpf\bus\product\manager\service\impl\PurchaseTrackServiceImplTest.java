package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseTrackMapper;
import com.snbc.bbpf.bus.product.market.common.vo.BaseOrderVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * PurchaseTrackServiceImpl 单元测试
 */
@DisplayName("采购跟踪服务实现类测试")
class PurchaseTrackServiceImplTest {

    @Mock
    private PurchaseTrackMapper purchaseTrackMapper;

    @InjectMocks
    private PurchaseTrackServiceImpl purchaseTrackService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("保存采购跟踪记录 - 成功")
    void testSavePurchaseTrack_Success() {
        // Given
        BaseOrderVo orderVo = new BaseOrderVo();
        orderVo.setUserId("user123");
        orderVo.setTenantId("tenant123");
        orderVo.setPaymentAmount(new BigDecimal("100.00"));
        orderVo.setDiscountAmount(new BigDecimal("10.00"));
        orderVo.setPurchaseAmount(new BigDecimal("90.00"));
        
        String purchaseNo = "PO123456";
        Integer purchaseStatus = 1;
        
        when(purchaseTrackMapper.insertSelective(any(PurchaseTrack.class))).thenReturn(1);
        
        // When
        boolean result = purchaseTrackService.savePurchaseTrack(orderVo, purchaseNo, purchaseStatus);
        
        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("保存采购跟踪记录 - 失败")
    void testSavePurchaseTrack_Failure() {
        // Given
        BaseOrderVo orderVo = new BaseOrderVo();
        orderVo.setUserId("user123");
        orderVo.setTenantId("tenant123");
        orderVo.setPaymentAmount(new BigDecimal("100.00"));
        orderVo.setDiscountAmount(new BigDecimal("10.00"));
        orderVo.setPurchaseAmount(new BigDecimal("90.00"));
        
        String purchaseNo = "PO123456";
        Integer purchaseStatus = 1;
        
        when(purchaseTrackMapper.insertSelective(any(PurchaseTrack.class))).thenReturn(0);
        
        // When
        boolean result = purchaseTrackService.savePurchaseTrack(orderVo, purchaseNo, purchaseStatus);
        
        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("保存采购跟踪记录 - 空订单信息")
    void testSavePurchaseTrack_NullOrderVo() {
        // Given
        BaseOrderVo orderVo = new BaseOrderVo();
        String purchaseNo = "PO123456";
        Integer purchaseStatus = 1;
        
        when(purchaseTrackMapper.insertSelective(any(PurchaseTrack.class))).thenReturn(1);
        
        // When
        boolean result = purchaseTrackService.savePurchaseTrack(orderVo, purchaseNo, purchaseStatus);
        
        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("保存采购跟踪记录 - 空采购单号")
    void testSavePurchaseTrack_NullPurchaseNo() {
        // Given
        BaseOrderVo orderVo = new BaseOrderVo();
        orderVo.setUserId("user123");
        orderVo.setTenantId("tenant123");
        orderVo.setPaymentAmount(new BigDecimal("100.00"));
        orderVo.setDiscountAmount(new BigDecimal("10.00"));
        orderVo.setPurchaseAmount(new BigDecimal("90.00"));
        
        String purchaseNo = null;
        Integer purchaseStatus = 1;
        
        when(purchaseTrackMapper.insertSelective(any(PurchaseTrack.class))).thenReturn(1);
        
        // When
        boolean result = purchaseTrackService.savePurchaseTrack(orderVo, purchaseNo, purchaseStatus);
        
        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("保存采购跟踪记录 - 空采购状态")
    void testSavePurchaseTrack_NullPurchaseStatus() {
        // Given
        BaseOrderVo orderVo = new BaseOrderVo();
        orderVo.setUserId("user123");
        orderVo.setTenantId("tenant123");
        orderVo.setPaymentAmount(new BigDecimal("100.00"));
        orderVo.setDiscountAmount(new BigDecimal("10.00"));
        orderVo.setPurchaseAmount(new BigDecimal("90.00"));
        
        String purchaseNo = "PO123456";
        Integer purchaseStatus = null;
        
        when(purchaseTrackMapper.insertSelective(any(PurchaseTrack.class))).thenReturn(1);
        
        // When
        boolean result = purchaseTrackService.savePurchaseTrack(orderVo, purchaseNo, purchaseStatus);
        
        // Then
        assertTrue(result);
    }
}