/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * @ClassName: Permission
 * 权限信息
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class Permission {

    private String permissionId;

    private String permissionType;

    private String permissionName;

    private String permissionCode;

    private String parentName;

    private String parentId;

    private String permissionDesc;

    private Integer orderBy;

    private String permissionImage;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer permissionLevel;

    private String  permissionPath;

    private Integer sysType;

    private Integer hasEnable;

    private String routingUrl;

    private String createUserId;

    private String createOrgId;
}
