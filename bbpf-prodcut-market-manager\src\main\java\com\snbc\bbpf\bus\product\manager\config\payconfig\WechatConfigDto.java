package com.snbc.bbpf.bus.product.manager.config.payconfig;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

/**
 * 功能描述: <br>
 * 〈微信配置〉
 *
 * @param: null
 * @return:
 * @exception
 * @author: gs
 * @date: 2019-08-08 16:27
 */

@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-04-28T10:05:59.601Z")

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WechatConfigDto {
    /**
     * callback_addr
     */
    @JsonProperty("callback_addr")
    private String callbackAddr;

    /**
     * mch_id
     */
    @JsonProperty("mch_id")
    private String mchId;

    /**
     * app_private_key
     */
    @JsonProperty("app_private_key")
    private String appPrivateKey;

    /**
     * cert_local_path
     */
    @JsonProperty("cert_local_path")
    private String certLocalPath;

    /**
     * app_id
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * cert_password
     */
    @JsonProperty("cert_password")
    private String certPassword;

    /**
     * tenantId
     */
    @JsonProperty("tenantId")
    private String tenantId;

    /**
     * sign_type
     */
    @JsonProperty("sign_type")
    private String signType;

    /**
     * app_secret
     */
    @JsonProperty("app_secret")
    private String appSecret;

    @JsonProperty("sub_appid")
    private String subAppId;

    @JsonProperty("sub_mchId")
    private String subMchId;

}



