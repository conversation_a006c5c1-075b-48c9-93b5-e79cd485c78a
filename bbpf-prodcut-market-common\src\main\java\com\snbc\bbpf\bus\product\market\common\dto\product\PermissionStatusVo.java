/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * @ClassName: PermissionStatusVo
 * 权限状态Vo对象
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/20
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PermissionStatusVo {
    @NotBlank(message = "功能ID不能为空")
    private String permissionId;
    @NotBlank(message = "启用状态不能为空")
    private String hasEnable;

    @Override
    public String toString() {
        return "PermissionStatusVo{" +
                "permissionId='" + permissionId + '\'' +
                ", hasEnable='" + hasEnable + '\'' +
                '}';
    }
}
