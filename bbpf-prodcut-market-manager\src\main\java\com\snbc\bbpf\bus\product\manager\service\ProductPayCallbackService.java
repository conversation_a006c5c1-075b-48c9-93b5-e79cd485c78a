package com.snbc.bbpf.bus.product.manager.service;

import java.util.Map;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service
 * @ClassName: ProductPayCallbackService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 支付回调
 * @Author: MR.LI
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: MR.LI
 * @UpdateDate: 2020/8/24 15:21
 */
public interface ProductPayCallbackService {
    /**
     * ali回调
     * @param map
     */
    void aliNotify(Map<String, String> map) throws Exception;
    /**
     * wechat回调
     * @param map
     */
    void wxNotify(Map<String, String> map) throws Exception;

}
