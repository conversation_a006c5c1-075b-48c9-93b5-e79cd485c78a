package com.snbc.bbpf.bus.product.manager.controller.tenant;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.manager.resp.product.ServiceOrderPage;
import com.snbc.bbpf.bus.product.manager.service.ProductService;
import com.snbc.bbpf.bus.product.market.common.dto.product.ServicePDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ServicePPDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailExDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductPageQueryEx;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ServiceMarketQueryProduct;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ProductController单元测试类
 */
@DisplayName("产品控制器测试")
class ProductControllerTest {

    @Mock
    private ProductService productService;

    @InjectMocks
    private ProductController productController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(productController).build();
    }

    @Test
    @DisplayName("查询产品服务市场列表 - 成功")
    void testSevicesListWithPage_Success() {
        // 准备测试数据
        ServiceMarketQueryProduct query = new ServiceMarketQueryProduct();
        query.setPageNum(1);
        query.setPageSize(10);
        query.setProductType(1); // 使用int类型
        query.setGradeTypeNo(1); // 使用int类型
        
        List<ServicePDto> serviceList = new ArrayList<>();
        ServicePDto service1 = new ServicePDto();
        service1.setProductId("PROD_001");
        service1.setProductName("测试产品1");
        serviceList.add(service1);
        
        ServicePDto service2 = new ServicePDto();
        service2.setProductId("PROD_002");
        service2.setProductName("测试产品2");
        serviceList.add(service2);
        
        PageInfo<ServicePDto> pageInfo = new PageInfo<>(serviceList);
        pageInfo.setTotal(2);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
        
        // Mock服务方法
        when(productService.sevicesListWithPage(any(ProductPageQuery.class))).thenReturn(pageInfo);

        // 执行测试
        CommonResp result = productController.sevicesListWithPage(query);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        PageInfo<ServicePDto> resultBody = (PageInfo<ServicePDto>) result.getBody();
        assertEquals(2, resultBody.getTotal());
        assertEquals(2, resultBody.getList().size());
        assertEquals("PROD_001", resultBody.getList().get(0).getProductId());
        assertEquals("测试产品1", resultBody.getList().get(0).getProductName());
        
        // 验证Mock调用
        verify(productService, times(1)).sevicesListWithPage(any(ProductPageQuery.class));
    }

    @Test
    @DisplayName("查询产品服务市场列表 - 空结果")
    void testSevicesListWithPage_EmptyResult() {
        // 准备测试数据
        ServiceMarketQueryProduct query = new ServiceMarketQueryProduct();
        query.setPageNum(1);
        query.setPageSize(10);
        
        PageInfo<ServicePDto> emptyPageInfo = new PageInfo<>(new ArrayList<>());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPageNum(1);
        emptyPageInfo.setPageSize(10);
        
        // Mock服务方法
        when(productService.sevicesListWithPage(any(ProductPageQuery.class))).thenReturn(emptyPageInfo);

        // 执行测试
        CommonResp result = productController.sevicesListWithPage(query);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        PageInfo<ServicePDto> resultBody = (PageInfo<ServicePDto>) result.getBody();
        assertEquals(0, resultBody.getTotal());
        assertTrue(resultBody.getList().isEmpty());
        
        // 验证Mock调用
        verify(productService, times(1)).sevicesListWithPage(any(ProductPageQuery.class));
    }

    @Test
    @DisplayName("查询已购产品服务列表 - 成功")
    void testSevicesOrderListWithPage_Success() {
        // 准备测试数据
        ProductPageQueryEx recordEx = new ProductPageQueryEx();
        recordEx.setPageNum(1);
        recordEx.setPageSize(10);
        recordEx.setTenantId("TENANT_001");
        
        List<ServicePPDto> orderList = new ArrayList<>();
        ServicePPDto order1 = new ServicePPDto();
        order1.setProductId("ORDER_PROD_001");
        order1.setProductName("已购产品1");
        // ServicePPDto没有orderStatus字段，移除此行
        orderList.add(order1);
        
        PageInfo<ServicePPDto> pageInfo = new PageInfo<>(orderList);
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
        
        // Mock服务方法
        when(productService.sevicesOrderListWithPage(any(ProductPageQuery.class))).thenReturn(pageInfo);

        // 执行测试
        ServiceOrderPage result = productController.sevicesOrderListWithPage(recordEx);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        assertEquals("操作成功", result.getHead().getMessage());
        
        PageInfo<ServicePPDto> resultBody = result.getBody();
        assertEquals(1, resultBody.getTotal());
        assertEquals(1, resultBody.getList().size());
        assertEquals("ORDER_PROD_001", resultBody.getList().get(0).getProductId());
        assertEquals("已购产品1", resultBody.getList().get(0).getProductName());
        // ServicePPDto没有orderStatus字段，移除此断言
        
        // 验证Mock调用
        verify(productService, times(1)).sevicesOrderListWithPage(any(ProductPageQuery.class));
    }

    @Test
    @DisplayName("查询已购产品服务列表 - 无订单")
    void testSevicesOrderListWithPage_NoOrders() {
        // 准备测试数据
        ProductPageQueryEx recordEx = new ProductPageQueryEx();
        recordEx.setPageNum(1);
        recordEx.setPageSize(10);
        recordEx.setTenantId("TENANT_002");
        
        PageInfo<ServicePPDto> emptyPageInfo = new PageInfo<>(new ArrayList<>());
        emptyPageInfo.setTotal(0);
        
        // Mock服务方法
        when(productService.sevicesOrderListWithPage(any(ProductPageQuery.class))).thenReturn(emptyPageInfo);

        // 执行测试
        ServiceOrderPage result = productController.sevicesOrderListWithPage(recordEx);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        PageInfo<ServicePPDto> resultBody = result.getBody();
        assertEquals(0, resultBody.getTotal());
        assertTrue(resultBody.getList().isEmpty());
        
        // 验证Mock调用
        verify(productService, times(1)).sevicesOrderListWithPage(any(ProductPageQuery.class));
    }

    @Test
    @DisplayName("查询产品服务详情 - 成功")
    void testSeviceRRQStatusDetail_Success() {
        // 准备测试数据
        String productId = "PROD_DETAIL_001";
        String tenantId = "TENANT_001";
        
        ProductDetailExDto productDetail = new ProductDetailExDto();
        productDetail.setProductId(productId);
        productDetail.setProductName("产品详情测试");
        // ServicePDto没有setProductDesc和setProductStatus方法，移除这些调用
        
        // Mock服务方法
        when(productService.seviceTypeDetail(productId, tenantId)).thenReturn(productDetail);

        // 执行测试
        CommonResp result = productController.seviceRRQStatusDetail(productId, tenantId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        ProductDetailExDto resultBody = (ProductDetailExDto) result.getBody();
        assertEquals(productId, resultBody.getProductId());
        assertEquals("产品详情测试", resultBody.getProductName());
        // ProductDetailExDto继承自ServicePDto，没有productDesc和productStatus字段，移除这些断言
        
        // 验证Mock调用
        verify(productService, times(1)).seviceTypeDetail(productId, tenantId);
    }

    @Test
    @DisplayName("查询产品服务详情 - 产品不存在")
    void testSeviceRRQStatusDetail_ProductNotFound() {
        // 准备测试数据
        String productId = "NON_EXIST_PROD";
        String tenantId = "TENANT_001";
        
        // Mock服务方法返回null
        when(productService.seviceTypeDetail(productId, tenantId)).thenReturn(null);

        // 执行测试
        CommonResp result = productController.seviceRRQStatusDetail(productId, tenantId);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertEquals("000000", result.getHead().getCode());
        assertNull(result.getBody());
        
        // 验证Mock调用
        verify(productService, times(1)).seviceTypeDetail(productId, tenantId);
    }

    @Test
    @DisplayName("获取线下支付配置详情 - 成功")
    void testGetOfflineDetail_Success() {
        // 准备测试数据
        String offlineConfig = "{\"bankAccount\":\"**********\",\"bankName\":\"测试银行\",\"accountName\":\"测试账户\"}";
        
        // Mock服务方法
        when(productService.getOfflineDetail()).thenReturn(offlineConfig);

        // 执行测试
        CommonResp result = productController.getOfflineDetail();
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertNotNull(result.getBody());
        assertEquals("000000", result.getHead().getCode());
        
        String resultBody = (String) result.getBody();
        assertEquals(offlineConfig, resultBody);
        assertTrue(resultBody.contains("bankAccount"));
        assertTrue(resultBody.contains("测试银行"));
        
        // 验证Mock调用
        verify(productService, times(1)).getOfflineDetail();
    }

    @Test
    @DisplayName("获取线下支付配置详情 - 配置为空")
    void testGetOfflineDetail_EmptyConfig() {
        // Mock服务方法返回空字符串
        when(productService.getOfflineDetail()).thenReturn("");

        // 执行测试
        CommonResp result = productController.getOfflineDetail();
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getHead());
        assertEquals("000000", result.getHead().getCode());
        assertEquals("", result.getBody());
        
        // 验证Mock调用
        verify(productService, times(1)).getOfflineDetail();
    }

    @Test
    @DisplayName("分页参数验证测试")
    void testPaginationParameters() {
        // 准备测试数据 - 测试边界值
        ServiceMarketQueryProduct query = new ServiceMarketQueryProduct();
        query.setPageNum(0); // 边界值测试
        query.setPageSize(1000); // 大页面测试
        
        PageInfo<ServicePDto> pageInfo = new PageInfo<>(new ArrayList<>());
        
        // Mock服务方法
        when(productService.sevicesListWithPage(any(ProductPageQuery.class))).thenReturn(pageInfo);

        // 执行测试
        CommonResp result = productController.sevicesListWithPage(query);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("000000", result.getHead().getCode());
        
        // 验证Mock调用
        verify(productService, times(1)).sevicesListWithPage(any(ProductPageQuery.class));
    }

    @Test
    @DisplayName("产品类型过滤测试")
    void testProductTypeFilter() {
        // 准备测试数据
        ServiceMarketQueryProduct query = new ServiceMarketQueryProduct();
        query.setPageNum(1);
        query.setPageSize(10);
        query.setProductType(2); // 使用int类型表示HARDWARE
        query.setGradeTypeNo(2); // 使用int类型表示GRADE_B
        
        List<ServicePDto> filteredList = new ArrayList<>();
        ServicePDto hardwareProduct = new ServicePDto();
        hardwareProduct.setProductId("HW_001");
        hardwareProduct.setProductName("硬件产品");
        hardwareProduct.setProductTypeName("HARDWARE");
        filteredList.add(hardwareProduct);
        
        PageInfo<ServicePDto> pageInfo = new PageInfo<>(filteredList);
        
        // Mock服务方法
        when(productService.sevicesListWithPage(any(ProductPageQuery.class))).thenReturn(pageInfo);

        // 执行测试
        CommonResp result = productController.sevicesListWithPage(query);
        
        // 验证结果
        assertNotNull(result);
        PageInfo<ServicePDto> resultBody = (PageInfo<ServicePDto>) result.getBody();
        assertEquals(1, resultBody.getList().size());
        assertEquals("HARDWARE", resultBody.getList().get(0).getProductTypeName());
        
        // 验证Mock调用
        verify(productService, times(1)).sevicesListWithPage(any(ProductPageQuery.class));
    }

    @Test
    @DisplayName("租户隔离测试")
    void testTenantIsolation() {
        // 准备测试数据 - 不同租户
        String tenantId1 = "TENANT_001";
        String tenantId2 = "TENANT_002";
        String productId = "SHARED_PROD_001";
        
        ProductDetailExDto tenant1Detail = new ProductDetailExDto();
        tenant1Detail.setProductId(productId);
        // ServicePDto没有setTenantId方法，移除此调用
        tenant1Detail.setProductName("租户1的产品");
        
        ProductDetailExDto tenant2Detail = new ProductDetailExDto();
        tenant2Detail.setProductId(productId);
        // ServicePDto没有setTenantId方法，移除此调用
        tenant2Detail.setProductName("租户2的产品");
        
        // Mock服务方法 - 根据租户返回不同结果
        when(productService.seviceTypeDetail(productId, tenantId1)).thenReturn(tenant1Detail);
        when(productService.seviceTypeDetail(productId, tenantId2)).thenReturn(tenant2Detail);

        // 执行测试 - 租户1
        CommonResp result1 = productController.seviceRRQStatusDetail(productId, tenantId1);
        ProductDetailExDto resultBody1 = (ProductDetailExDto) result1.getBody();
        // ServicePDto没有tenantId字段，删除此断言
        assertEquals("租户1的产品", resultBody1.getProductName());
        
        // 执行测试 - 租户2
        CommonResp result2 = productController.seviceRRQStatusDetail(productId, tenantId2);
        ProductDetailExDto resultBody2 = (ProductDetailExDto) result2.getBody();
        // ServicePDto没有tenantId字段，删除此断言
        assertEquals("租户2的产品", resultBody2.getProductName());
        
        // 验证Mock调用
        verify(productService, times(1)).seviceTypeDetail(productId, tenantId1);
        verify(productService, times(1)).seviceTypeDetail(productId, tenantId2);
    }
}