package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.DictType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 字典类型数据访问接口
 */
@Mapper
public interface DictTypeMapper {
    int deleteByPrimaryKey(String typeCode);

    int insert(DictType dictType);

    int insertSelective(DictType dictType);

    DictType selectByPrimaryKey(String typeCode);

    int updateByPrimaryKeySelective(DictType dictType);

    int updateByPrimaryKey(DictType dictType);
    /**
     * 查询所有的字典类型
     * @return
     */
    List<DictType> queryAllDictType();
    /**
     * 根据查询条件查询dictType列表
     * @param map
     * @return
     */
    List<DictType> queryDictTypeByMap(Map map);
}
