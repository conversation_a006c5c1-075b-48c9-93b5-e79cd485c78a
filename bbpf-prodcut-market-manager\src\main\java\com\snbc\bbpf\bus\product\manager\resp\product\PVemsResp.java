package com.snbc.bbpf.bus.product.manager.resp.product;

import com.snbc.bbpf.bus.product.market.common.dto.product.PVemsDto;
import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import lombok.Data;

import java.util.List;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.resp.product
 * @ClassName: PVemsResp
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 全部售货机返回信息
 * @Author: wangsong
 * @CreateDate: 2020/9/24 11:34
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/9/24 11:34
 */
@Data
public class PVemsResp {
    
    private CallResponse header;

    
    private List<PVemsDto> body;
}
