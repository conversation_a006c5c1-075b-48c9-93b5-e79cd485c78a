package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: LiangJB
 * 功能描述: <br>
 * 服务订单表数据解析
 * @date: 2020/8/12 13:21
 */
@Mapper
public interface ProductPurchaseMapper {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insert(ProductPurchase productPurchase);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 插入数据
     * @date: 2020/8/12 13:21
     */
    int insertSelective(ProductPurchase productPurchase);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 获取数据
     * @date: 2020/8/12 13:21
     */
    ProductPurchase selectByPrimaryKey(String productPurchaseId);

    /**
     * @author: liangJB
     * 根据订单号查询订单
     * @param purchaseNo
     * @return
     */
    ProductPurchase selectByPurchaseNo(@Param("purchaseNo") String purchaseNo);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKeySelective(ProductPurchase productPurchase);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 根据订单号更新数据
     * @date: 2020/8/12 13:21
     */
    int updateByPurchaseNoSelective(ProductPurchase productPurchase);
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 更新数据
     * @date: 2020/8/12 13:21
     */
    int updateByPrimaryKey(ProductPurchase productPurchase);

    void updatePurchaseInvoiceStatus(@Param("productPurchaseIdList") List<String> productPurchaseIdList,
                                     @Param("invoiceStatus")Integer invoiceStatus);

    List<BigDecimal> selectNotIssued(@Param("productPurchaseIdList") List<String> productPurchaseIdList,@Param("invoiceStatus") Integer invoiceStatus);

    /**
     * @author: liangJB
     * 根据tenantId 和 productIds查询
     * @param tenantId
     * @param productIds
     * @return
     */
    List<ProductPurchase> selectByTenantAndProductIds(@Param("tenantId") String tenantId,@Param("productIds")List<String> productIds);


    void updatePurchaseByInvoiceId(@Param("invoiceApplyId")String invoiceApplyId);
}
