package com.snbc.bbpf.bus.product.manager.config.payconfig.request;

import lombok.Data;

/**
 * 功能描述: <br>
 * 〈AliQRCodeRequest〉
 *
 * @exception
 * @author: MR.LI
 * @date: 2019-08-08 18:39
 */
@Data
public class AliQRCodeRequest {
    //商品内容
    
    private String productSubject;
    //总金额
    
    private String totalAmount;
    //过期时间
    
    private String timeExpress;
    //业务订单号
    
    private String outTradeNo;

}
