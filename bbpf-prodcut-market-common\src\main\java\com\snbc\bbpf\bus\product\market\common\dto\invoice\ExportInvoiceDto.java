package com.snbc.bbpf.bus.product.market.common.dto.invoice;

import lombok.Data;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.vo.invoice
 * @ClassName: ExportInvoiceVo
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票导出vo
 * @Author: wangsong
 * @CreateDate: 2020/8/26 10:57
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/26 10:57
 */
@Data
public class ExportInvoiceDto{
    private String invoiceApplyId;
    private String invoiceCode;
    private String tenantName;
    private String applyTime;
    private String invoiceContent;
    private String invoiceAmount;
    private String invoiceTitle;
    private String taxRegisterNo;
    private String bankName;
    private String bankNo;
    private String registerAddress;
    private String registerPhonenum;
    private String address;
    private String invoiceCarrier;
    private String invoiceType;
    private String applyType;
    private String invoiceStatus;

}
