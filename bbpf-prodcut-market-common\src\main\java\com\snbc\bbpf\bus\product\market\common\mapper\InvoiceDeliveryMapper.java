package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoceExpressDeliDto;
import com.snbc.bbpf.bus.product.market.common.entity.InvoiceDelivery;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ExperssInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.mapper
 * @ClassName: InvoiceDeliveryMapper
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票快递信息持久层
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:21
 */
@Mapper
public interface InvoiceDeliveryMapper {
    int insert(InvoiceDelivery invoiceDelivery);

    int insertSelective(InvoiceDelivery invoiceDelivery);

    InvoiceDelivery selectByPrimaryKey(String deliveryId);

    int updateByPrimaryKeySelective(InvoiceDelivery invoiceDelivery);

    int updateByPrimaryKey(InvoiceDelivery invoiceDelivery);

    InvoiceDelivery selectExpressDelivery(String invoiceApplyId);

    InvoceExpressDeliDto selectExpressInfo(String invoiceApplyId);

    List<InvoiceDelivery> selectByIds(@Param("list") List<String> invoiceDeliveryIds);

    void updateExpressInfo(ExperssInfo experssInfo);
}
