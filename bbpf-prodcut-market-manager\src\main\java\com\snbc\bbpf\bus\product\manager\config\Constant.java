/**
* 版权所有 2019山东新北洋信息技术股份有限公司
* 保留所有权利。
*/
package com.snbc.bbpf.bus.product.manager.config;

/**
 * 常量定义
 *
 * <AUTHOR>
 *
 */
public final class Constant {

	public static final String PRODUCT_AVAILABLE_PREFIX = "bbpf_product_";
	public static final String ALIPAY_CONFIG_ID = "alipay";
	// UTF-8字符编码
	public static final String WECHAT_CONFIG_ID = "wechat";
    public static final String ALIPAY = "alipay";
	public static final String WECHATPAY = "wechat";
    public static final Integer ALIPAY_CHANNEL = 96;
	public static final Integer WECHATPAY_CHANNEL = 97;
	public static final Integer QRCODE_WIDTH = 200;
	public static final Integer QRCODE_HEIGHT = 200;
	public static final String TENANTID = "tenantId";
	public static final String OUTTRADENO = "out_trade_no";
	public static final String TRADENO = "trade_no";
	public static final String TRANSID = "transaction_id";
	public static final String BUYERID = "buyer_id";
	public static final String OPENID = "openid";
	public static final String TRADESTATUS = "trade_status";
	public static final String RESULTCODE = "result_code";
	public static final String TOTALAMOUNT = "total_amount";
	public static final String TOTALFEE = "total_fee";
	public static final Integer FENTOYUAN = 100;
	public static final String TIMEOUT_MIN_KEY = "paytimeoutmin";
	public static final String TIMEOUT_MIN_VALUE_KEY = "timeout";
	public static final String SUCCESS = "success";
	public static final Integer ZERO = 0;
	public static final String TWO = "2";

	public static final String PRODUCT_PAY ="bbpf_product_paycallback" ;
	public static final String PRODUCT_PAY_EXCHANGE ="bbpf_product_paycallback_exchange" ;
	public static final String PRODUCT_PAY_DEAD ="bbpf_product_paycallback_dead" ;
	public static final String PRODUCT_PAY_DEAD_EXCHANGE ="bbpf_product_paycallback_dead_exchange" ;
	public static final String DEAD_LETTER_PAY_KEY = "TDL_KEY";
	public static final String PAY_ROUTING_KEY = "PAYCALLBACK_KEY";

	public static final String PRODUCT_AUTHORITY_EXCHANGE ="bbpf_product_authority_exchange" ;
	public static final String PRODUCT_AUTHORITY_ROUTING_KEY ="bbpf_product_authority_routingkey" ;

	/**
	 * TradeType
	 */
	public static final String NATIVE = "NATIVE";
    public static final String RSA = "RSA";
    public static final long EXPIRE_TIME = 43200L;
	// 产品可用
	public static final String PRODUCT_AVAILABLE_AVAILABLE = "available";
	/**
	 * snbcPay返回成功code
	 */
	public static final String SNBC_PAY_SUCCESS_CODE = "000000";

	public static final Integer TEN = 10;
	public static final int ONE_NUM = 1;

	public static final int NO_THREE_K_SIXH = 3600;
	//权限路径默认分隔符号
	public static final String PERMISSION_FILTER_CHAR = "/";
	public static final String PERMISSION_CACHE_CATEGORY = "permission_Cache_Data";
	public static final String INNER = "inner";
	public static final String PREV = "prev";
	public static final String PRODUCTCATEGORY = "product_category";
	public static final String PRODUCTSTATUS = "product_status";
	public static final String CHARGETYPE = "charge_type";

	public static final int IS_RENEW_TRYORDER = 2;
	//订单状态 0：待支付 1：已支付 2：购买成功 3:已取消 4:已退款5：汇款待确认6：已超时
	public static final int PURCHASE_STATUS_WAITPAY = 0;
	public static final int PURCHASE_STATUS_PAY = 1;
	public static final int PURCHASE_STATUS_SUCCESS = 2;
	public static final int PURCHASE_STATUS_CANCEL = 3;
	public static final int PURCHASE_STATUS_WAITCONFIRM = 5;
	public static final int PURCHASE_STATUS_TIMEOUT = 6;
	//支付方式 0：线下支付 1：微信，2支付宝 3试用
	public static final int PAY_TYPE_OFFLINE = 0;
	public static final int PAY_TYPE_TRY = 3;
	//支付流水状态  0待支付1支付成功2关闭
	public static final int PAY_TRACK_WAIT = 0;
	public static final int PAY_TRACK_SUCCESS = 1;
	public static final int PAY_TRACK_CLOSE = 2;
	//产品服务单位
	public static final String DAY_STR = "Day";
	public static final String DAY_STR2 = "天/台";
	public static final String MONTH_STR = "Month";
	public static final int MONTH_NUM = 30;
	public static final String SEASON_STR = "Season";
	public static final int SEASON_NUM = 90;
	public static final String YEAR_STR = "Year";
	public static final int YEAR_NUM = 365;
	//发送消息常量
	public static final String MSG_TIME = "_msgtime";

	public static final String COMHEADUSERID = "userId";
	public static final String COMHEADUSERNAME = "userName";
	public static final String CURRENTSYSTYPE = "sysType";
	public static final String COMHEATENANTID = "tenantId";
	private Constant() {
	}
}
