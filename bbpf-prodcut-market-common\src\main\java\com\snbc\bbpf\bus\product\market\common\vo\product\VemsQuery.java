package com.snbc.bbpf.bus.product.market.common.vo.product;

import com.snbc.bbpf.bus.product.market.common.Constant;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.vo.product
 * @ClassName: VemsQuery
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 查询售货机列表入参
 * @Author: wangsong
 * @CreateDate: 2020/9/23 11:19
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/9/23 11:19
 */
@Data
public class VemsQuery {
    
    private String allSelectAreas;

    
    private List<String> lineIds;

    
    private String operatorId;

    
    private String vemsName;

    
    private List<String> vemsIds;

    
    private String serviceExpStartTime;

    
    private String serviceExpEndTime;

    
    @NotBlank(message = "商户id不能为空")
    private String tenantId;

    
    private Integer pageSize = Constant.TEN;

    
    private Integer pageNum = Constant.ONE;

    
    @NotBlank(message = "服务id不能为空")
    private String productId;

}
