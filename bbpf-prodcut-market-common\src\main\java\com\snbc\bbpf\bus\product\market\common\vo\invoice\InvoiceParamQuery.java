package com.snbc.bbpf.bus.product.market.common.vo.invoice;

import lombok.Data;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.vo
 * @ClassName: InvoiceParamVo
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票列表查询参数
 * @Author: wangsong
 * @CreateDate: 2020/8/25 10:52
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/25 10:52
 */
@Data
public class InvoiceParamQuery {

    
    private String invoiceType;

    
    private String invoiceStatus;

    
    private String startTime;

    
    private String endTime;

    
    private Integer pageNum;

    
    private Integer pageSize;

    
    private String tenantName;

    
    private String invoiceCarrier;

    
    private String invoiceApplyType;

    
    private String tenantId;

    
    private  String sortField = "applyTime";

    
    private  String sequence = "desc";
}
