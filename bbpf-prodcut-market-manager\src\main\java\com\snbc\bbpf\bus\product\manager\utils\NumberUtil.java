/**
 * 版权所有 2019 山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.product.manager.utils;

import java.util.regex.Pattern;

/**
 * <AUTHOR> gs
 * @ClassName : NumberUtil
 * @Description : 数字工具类
 * @Date: 2020-07-30 16:08
 */

public class NumberUtil {

    /**
     * @author: gs
     * 功能描述: <br>
     * <正则表达式校验字符串为数字>
     * @date: 2020/7/30 16:06
     * @param: str
     * @return: boolean
     */
    private static final Pattern myRegex = Pattern.compile("^?(([0-9]+))$");
    public static boolean isNum(String str) {
        return myRegex.matcher(str).matches();
    }
}
