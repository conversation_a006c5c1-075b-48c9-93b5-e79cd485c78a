/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.service.impl;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.product.manager.utils.CurrentUser;
import com.snbc.bbpf.bus.product.market.common.vo.MessageVo;
import com.snbc.bbpf.bus.product.market.common.vo.TenantLogVo;
import com.snbc.bbpf.buslog.entity.BusLog;
import com.snbc.bbpf.buslog.service.BusLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * @ClassName: BusLogSaveServiceImpl
 * @Description: 日志存储接口实现
 * @module: si-bbpf-system
 * @Author: jiafei
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Service
@Slf4j
public class BusLogSaveServiceImpl implements BusLogService {
    @Autowired
    private RabbitTemplate rabbitTemplate;
    private static final DateTimeFormatter FMT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    /***
     * @Description: 插入日志
     * @Author: jiafei
     * @param :         logType
     * @param :         logUrl
     * @param :         logTarget
     * @param :         userId
     * @param :         ip
     * @param :         remark
     * @CreateDate: 2021/5/25 17:15
     * @UpdateDate: 2021/5/25 17:15
     * @return :        int
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void insertLog(BusLog busLog) {
        try {
            TenantLogVo tenantLogVo = TenantLogVo.builder().zhContent(busLog.getZhContent())
                    .logId(UUID.randomUUID().toString()).enContent(busLog.getEnContent())
                    .createTime(FMT.format(busLog.getCreateTime())).userId(CurrentUser.getUserId()).userName(CurrentUser.getUserName())
                    .tenantId(CurrentUser.getTenantId()).tenantName(CurrentUser.getTenantName())
                    .logTarget(busLog.getLogTarget()).logType(busLog.getLogType()).ip(busLog.getIp())
                    .remark(busLog.getRequestSource()).build();
            MessageVo<TenantLogVo> tenantLogVoMessageVo = new MessageVo<>();
            tenantLogVoMessageVo.setCreateTime(LocalDateTime.now().toString());
            tenantLogVoMessageVo.setMessageData(tenantLogVo);
            rabbitTemplate.convertAndSend("bbpf.tenantconfig.buslog", "tenantconfig.buslog.productmarket", JSON.toJSONString(tenantLogVoMessageVo));
        }catch (Exception ex){
            log.error("The tenant service market fails to write logs，logContent：{}",busLog.getEnContent(),ex);
        }
    }
}
