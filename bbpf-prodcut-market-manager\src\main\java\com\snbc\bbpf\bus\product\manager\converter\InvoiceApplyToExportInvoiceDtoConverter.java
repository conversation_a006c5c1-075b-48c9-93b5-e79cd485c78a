package com.snbc.bbpf.bus.product.manager.converter;

import com.snbc.bbpf.bus.product.market.common.dto.invoice.ExportInvoiceDto;
import com.snbc.bbpf.bus.product.market.common.entity.InvoiceApply;
import com.snbc.bbpf.commons.objs.IConvert;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * InvoiceApply 到 ExportInvoiceDto 的转换器
 */
@Mapper
public interface InvoiceApplyToExportInvoiceDtoConverter extends IConvert<InvoiceApply, ExportInvoiceDto> {
    InvoiceApplyToExportInvoiceDtoConverter INSTANCE = Mappers.getMapper(InvoiceApplyToExportInvoiceDtoConverter.class);
}