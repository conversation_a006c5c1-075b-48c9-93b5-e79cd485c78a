/*
 * 版权所有 2009-2025山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.product.manager.controller;

import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import com.snbc.bbpf.bus.product.manager.service.HealthCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检测控制器
 *
 * <AUTHOR>
 * @module bbpf-umis-manager
 * @date 2025-07-03
 * @version 2.0.0
 */
@Slf4j
@RestController
@RequestMapping("/health")
public class HealthCheckController {

    /** 健康检查服务 */
    @Autowired
    private HealthCheckService healthCheckService;

    /**
     * Pod健康检测接口
     * 通过执行SELECT 1查询来验证数据库连接是否正常
     *
     * @return CommonResp<Object> 返回状态码200
     * <AUTHOR>
     * @date 2025-07-03
     * @since 2.0.0
     */
    @GetMapping("/podHealth")
    public ResponseEntity<Object> podHealth() {
        try {
            // 执行SELECT 1查询验证数据库连接
            healthCheckService.checkDatabaseConnection();
            log.info("Pod health check successful, database connection is normal");
            CallResponse header =new  CallResponse();
            header.setCode("200");
            header.setMessage("Pod health check successful, database connection is normal");
            return ResponseEntity.ok(header);
        } catch (Exception e) {
            log.error("Pod health check failed, database connection error: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}