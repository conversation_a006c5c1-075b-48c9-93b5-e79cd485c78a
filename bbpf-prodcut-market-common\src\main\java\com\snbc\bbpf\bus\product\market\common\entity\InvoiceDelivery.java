package com.snbc.bbpf.bus.product.market.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.entity
 * @ClassName: InvoiceDeliveryService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 发票快递信息表映射类
 * @Author: wangsong
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 15:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvoiceDelivery {
    private String deliveryId;

    private String tenantId;

    private String receiverName;

    private String tel;

    private String provinceId;

    private String cityId;

    private String areaId;

    private String address;

    private String expressCompany;

    private String expressNo;

}
