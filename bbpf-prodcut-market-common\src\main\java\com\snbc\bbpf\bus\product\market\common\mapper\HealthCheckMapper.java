/*
 * 版权所有 2009-2025山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.product.market.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 健康检查数据访问接口
 *
 * <AUTHOR>
 * @module bbpf-umis-manager-db-common
 * @date 2025-07-03
 * @version 2.0.0
 */
@Mapper
public interface HealthCheckMapper {

    /**
     * 执行数据库连接检查
     * 通过执行简单的SELECT 1查询来验证数据库连接是否正常
     *
     * @return Integer 查询结果，正常情况下返回1
     * <AUTHOR>
     * @date 2025-07-03
     * @since 2.0.0
     */
    @Select("SELECT 1")
    Integer checkDatabaseConnection();
}