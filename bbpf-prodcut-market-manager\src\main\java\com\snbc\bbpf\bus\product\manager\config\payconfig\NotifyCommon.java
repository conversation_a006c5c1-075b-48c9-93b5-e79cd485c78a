/**
* <p>Title: NotifyCommon.java</p>
* <p>Description: </p>
* <p>Copyright: Copyright (c) 2019</p>
* <p>Company: newbeiyang</p>
* <AUTHOR>
* @date 2020年5月28日
* @version 2.0
*/
package com.snbc.bbpf.bus.product.manager.config.payconfig;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 	支付宝异步通知内容解析公共方法
* <p>Title: NotifyCommon</p>
* <p>Description: </p>
* <AUTHOR>
* @date 2020年5月28日
*/
@Slf4j
@Component
public class NotifyCommon {
	  /**
     * 功能描述: <br>
     * 〈解析request信息〉
     *
     * @throws
     * @param: request
     * @return: void
     * @author: gs
     * @date: 2019-08-15 10:40
     */
    @SuppressWarnings("rawtypes")
    public void printHeader(HttpServletRequest request) {
        Enumeration headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String key = (String) headerNames.nextElement();
            log.info("request header key:{},value:{}",key,request.getHeader(key));
        }
    }

    /****
     * 解析支付宝响应内容
     *
     * @param params
     * @return
     */
    public Map<String, String> getResultParams(Map<String, String[]> params) throws Exception {
        Map<String, String> resulParams = new HashMap<>();
        for (Map.Entry<String, String[]> entry : params.entrySet()) {
            resulParams.put(entry.getKey(), entry.getValue()[0]);
        }
        log.debug("Analysis of alipay response content:{}", JSON.toJSONString(resulParams));
        return resulParams;
    }
}
