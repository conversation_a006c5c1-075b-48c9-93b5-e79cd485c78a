/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ProjectName: bus-module-sys
 * @Package: com.snbc.bbpf.organize.domain.entity.organization
 * @ClassName: RedisUserStatus
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * Cisneros新增更新，用户名称，电话，部门列表信息
 * @copyright Copyright: 2014-2020
 * @Description: redis中存储的用户状态
 * @Author: wangsong
 * @CreateDate: 2020/6/9 16:18
 * @UpdateUser: liangjb
 * @UpdateDate: 2021/5/27 16:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RedisUserStatus {

    private String userId;

    private String sysType;

    private String optTime;

    private String ip;

    private String userStatus;

    private String loginTime;

    private String logoutTime;
    /**
     * 电话
     */
    private String phone;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 部门列表
     */
    private List<UserSimpleOrg> orgs;
    /**
     * TOKEN 网关判断在TOKEN不失效的情况下重新登录的前面TOKEN失效
     */
    private String token;
}
