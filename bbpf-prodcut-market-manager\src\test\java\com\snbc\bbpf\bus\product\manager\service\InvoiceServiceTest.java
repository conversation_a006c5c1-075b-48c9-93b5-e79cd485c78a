/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service.impl
 * @ClassName: InvoiceServiceImplTest
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/8/27 18:05
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/27 18:05
 */
package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.impl.InvoiceHandleServiceImpl;
import com.snbc.bbpf.bus.product.manager.service.impl.InvoiceServiceImpl;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceListRRQDto;
import com.snbc.bbpf.bus.product.market.common.entity.InvoiceApply;
import com.snbc.bbpf.bus.product.market.common.entity.InvoiceDelivery;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceApplyMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceDeliveryMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ApplyInvoiceParam;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.InvoiceParamQuery;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.OpenerInvoice;
import org.codehaus.jackson.map.ObjectMapper;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.times;


@RunWith(SpringRunner.class)
public class InvoiceServiceTest {

    @InjectMocks
    InvoiceService invoiceService = new InvoiceServiceImpl();
    @Mock
    InvoiceHandleService invoiceHandleService = new InvoiceHandleServiceImpl();
    @Mock
    private InvoiceApplyMapper invoiceApplyMapper;
    @Mock
    private ProductPurchaseMapper productPurchaseMapper;
    @Mock
    private InvoiceDeliveryMapper invoiceDeliveryMapper;

    private MockHttpServletResponse httpServletResponse;
    List<InvoiceListRRQDto> invoiceApplyList = new ArrayList<>();

    private String paramJson;

    private ApplyInvoiceParam applyInvoiceParam;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Before
    public void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);
        InvoiceListRRQDto invoiceApply = new InvoiceListRRQDto();
        invoiceApply.setInvoiceApplyId("88b00fba-907d-49cd-8b67-c19ec7c9a1c6");
        invoiceApply.setApplyTime(LocalDateTime.now());
        invoiceApplyList.add(invoiceApply);
        httpServletResponse = new MockHttpServletResponse();

        paramJson = "{\n" +
                "    \"tenantId\": \"143\",\n" +
                "    \"applyType\": 2,\n" +
                "    \"invoiceType\": 2,\n" +
                "    \"invoiceTitle\": \"企业\",\n" +
                "    \"invoiceContent\": \"增值服务\",\n" +
                "    \"applyUser\": \"1\",\n" +
                "    \"invoiceCarrier\": 2,\n" +
                "    \"invoiceAmount\": 400,\n" +
                "\t\"productPurchaseId\":[\"2\"],\n" +
                "    \"expressCompany\": null,\n" +
                "    \"expressNo\": null,\n" +
                "    \"taxRegisterNo\": \"**************\",\n" +
                "    \"bankName\": \"农业银行\",\n" +
                "    \"bankNo\": \"37001111222233330137\",\n" +
                "    \"registerAddress\": \"北京市海淀区\",\n" +
                "    \"registerPhonenum\": \"***********\",\n" +
                "    \"mail\": \"<EMAIL>\",\n" +
                "    \"tenantName\": \"农夫山泉\",\n" +
                "    \"isUpdateInvoiceTit\":true,\n" +
                "    \"receiverAddress\":\"北京市海淀区清华大学\",\n" +
                "    \"receiverTel\":\"***********\",\n" +
                "    \"receiverName\":\"laowang\"\n" +
                "}";

        applyInvoiceParam = objectMapper.readValue(paramJson, ApplyInvoiceParam.class);

    }

    /***
     * @Description: 导出发票信息excel单元测试
     * @Author: wangsong
     * @CreateDate: 2020/8/28 11:22
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/28 11:22
     * @return :        void
     */

    @Test
    public void test_exportInvoice() throws BusinessException {
        InvoiceParamQuery invoiceParamQuery = new InvoiceParamQuery();
        Mockito.when(invoiceApplyMapper.selectInvoiceList(invoiceParamQuery)).thenReturn(invoiceApplyList);
        List<InvoiceDelivery> invoiceDeliveryArrayList = new ArrayList<>();
        Mockito.when(invoiceDeliveryMapper.selectByIds(Mockito.any())).thenReturn(invoiceDeliveryArrayList);
        invoiceService.exportInvoice(httpServletResponse, invoiceParamQuery);
        //断言response 返回的文件名称
        assertThat(httpServletResponse.getHeader("Content-Disposition"), Matchers.containsString("filename=%E5%8F%91%E7%A5%A8%E7%94%B3%E8%AF%B7%E8%AE%B0%E5%BD%"));
    }

    /***
     * @Description: 导出超过30000条数据量的excel测试
     * @Author: wangsong
     * @CreateDate: 2020/9/1 9:58
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/9/1 9:58
     * @return :        void
     */

    @Test
    public void test_exportBigDataInvoice() throws BusinessException {
        //模拟30000条以上的数据
        for (int i = 0; i < 30100; i++) {
            invoiceApplyList.add(invoiceApplyList.get(0));
        }
        InvoiceParamQuery invoiceParamQuery = new InvoiceParamQuery();
        Mockito.when(invoiceApplyMapper.selectInvoiceList(invoiceParamQuery)).thenReturn(invoiceApplyList);
        List<InvoiceDelivery> invoiceDeliveryArrayList = new ArrayList<>();
        Mockito.when(invoiceDeliveryMapper.selectByIds(Mockito.any())).thenReturn(invoiceDeliveryArrayList);
        invoiceService.exportInvoice(httpServletResponse, invoiceParamQuery);
        //断言response 返回的文件名称
        assertThat(httpServletResponse.getHeader("Content-Disposition"), Matchers.containsString("filename=%E5%8F%91%E7%A5%A8%E7%94%B3%E8%AF%B7%E8%AE%B0%E5%BD%"));
    }

    /***
     * @Description: 空指针异常测试
     * @Author: wangsong
     * @CreateDate: 2020/9/1 10:19
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/9/1 10:19
     * @return :        void
     */
    @Test(expected = NullPointerException.class)
    public void test_exportInvoiceException() throws BusinessException {
        //模拟返回数据 - 创建一个InvoiceApply对象，其中某些必需字段为null以触发NullPointerException
        InvoiceApply invoiceApply = new InvoiceApply();
        // 设置一些基本字段，但故意让某些字段为null以触发异常
        invoiceApply.setInvoiceApplyId("test-id");
        // invoiceApply.getApplyTime() 为null，会在dataConversion中的dtf.format()时抛出NullPointerException
        List<InvoiceApply> invoiceApplyList = new ArrayList<>();
        invoiceApplyList.add(invoiceApply);
        
        InvoiceParamQuery invoiceParamQuery = new InvoiceParamQuery();
        //mock 返回数据 - 修正为mock selectExportInvoice方法
        Mockito.when(invoiceApplyMapper.selectExportInvoice(invoiceParamQuery)).thenReturn(invoiceApplyList);
        List<InvoiceDelivery> invoiceDeliveryArrayList = new ArrayList<>();
        Mockito.when(invoiceDeliveryMapper.selectByIds(Mockito.any())).thenReturn(invoiceDeliveryArrayList);
        invoiceService.exportInvoice(httpServletResponse, invoiceParamQuery);
    }

    /***
     * @Description: 测试不存在的订单开票
     * @Author: wangsong
     * @CreateDate: 2020/12/24 13:14
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/12/24 13:14
     * @return :        void
     */
    @Test
    public void testApplyInvoice_productPurchaseNotExist() {
        try {
            ArrayList<BigDecimal> amountList = new ArrayList<>();
            //mock 查询不到订单的金额信息
            Mockito.when(productPurchaseMapper.selectNotIssued(Mockito.any(), Mockito.any())).thenReturn(amountList);
            invoiceService.applyInvoice(applyInvoiceParam);
        } catch (BusinessException | NoSuchAlgorithmException e) {
            //断言 订单不存在异常
            Assert.assertEquals(e.getMessage(), Errors.INVOICEDPURCHASE.getMessage());
        }
    }

    /***
     * @Description: 新增发票测试
     * @Author: wangsong
     * @CreateDate: 2020/10/14 16:56
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/10/14 16:56
     * @return :        void
     */
    @Test
    public void testApplyInvoice_applyInvoice() throws BusinessException, NoSuchAlgorithmException {
        ArrayList<BigDecimal> amountList = new ArrayList<>();
        amountList.add(new BigDecimal(400));
        //mock 查询开票订单的金额
        Mockito.when(productPurchaseMapper.selectNotIssued(Mockito.any(), Mockito.any())).thenReturn(amountList);
        invoiceService.applyInvoice(applyInvoiceParam);
        //断言是否调用了发票及订单关联方法（调用则证明发票新增成功）
        Mockito.verify(invoiceApplyMapper, times(1)).insertInvoicePurchase(Mockito.any());
    }


    /***
      * @Description:   开具发票后再次开具抛出业务异常
      * @Author:         wangsong
      * @CreateDate:     2020/12/24 15:54
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/12/24 15:54
      * @return :        void
     */
    @Test
    public void testUpdateInvoiceStatus_updateInvoiceStatusExp() {
        //mock 发票已经开具
        Mockito.when(invoiceApplyMapper.invoiceStatusById(Mockito.any())).thenReturn(2);
        OpenerInvoice openerInvoice = new OpenerInvoice();
        openerInvoice.setInvoiceApplyId("1");
        try {
            invoiceService.updateInvoiceStatus(openerInvoice);
        } catch (BusinessException e) {
            //断言是否抛出发票已开具异常
            Assert.assertEquals(e.getMessage(),Errors.INVOICEISSUE.getMessage());
        }
    }

    @Test
    public void scaleTest(){
        BigDecimal invoiceAmount = new BigDecimal("2.215");
        //发票金额两位小数处理
        Assert.assertEquals(invoiceAmount.setScale(2, RoundingMode.HALF_UP).toString(),"2.22");
        long aa=2;
        double b=1.0;
        double a = Math.round((aa+b) * 100.0);
        System.out.println(a);
    }
}
