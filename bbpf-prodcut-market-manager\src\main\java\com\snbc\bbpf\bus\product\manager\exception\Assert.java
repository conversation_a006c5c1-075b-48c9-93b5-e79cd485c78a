/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.exception;


import org.apache.commons.lang3.StringUtils;

/**
* @ClassName: Assert 
* @Description: 自定义断言  
* <AUTHOR> newbeiyang.com
* @date 2020年7月2日 下午4:11:54 
* @version V1.0 
*/
public interface Assert {
	
	/**
	 * wjc1
	 * Assert that an object is not {@code null}.
	 * @param obj the obj to check
	 * @throws BusinessException if the object is null
	 */
	default void assertNotNull(Object obj) {
		if(obj==null) {
			objIsNullException(obj);
		}
		if((obj instanceof String)&& StringUtils.isEmpty(String.valueOf(obj))) {
			objIsNullException(obj);
		}
	}
	
	/**
	 * wjc1
	 * Assert that an object is {@code null}.
	 * @param obj the obj to check
	 * @throws BusinessException  if the object is not null
	 */
	default void assertNull(Object obj) {
		if(obj!=null) {
			objIsNullException(obj);
		}
	}
	
	/**
     * Asserts that two Object are equal.
     * if not equels thrown objNotEqualException
     */
	default void assertEquals(Object actual, Object expected) {
		if(!isEquals(actual, expected)) {
			objNotEqualException(actual);
		}
    }

	default boolean isEquals(Object actual, Object expected) {
		if (expected == null) {
            return actual == null;
        }
		return expected.equals(actual);
	}
	
	/**对象为空异常*/
	BusinessException objIsNullException(Object... args);
	/**对象不相等异常*/
	BusinessException objNotEqualException(Object... args);
}
