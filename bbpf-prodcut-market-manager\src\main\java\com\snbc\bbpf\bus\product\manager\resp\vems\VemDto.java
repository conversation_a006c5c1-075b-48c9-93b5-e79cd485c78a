package com.snbc.bbpf.bus.product.manager.resp.vems;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import java.util.ArrayList;


/**
 * 售货机基本信息
 */

@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-04-16T07:45:22.317Z")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VemDto {

    /**
     * 激活状态
     */
    
    @JsonProperty("activatonState")
    private String activatonState = null;
    /**
     * 货柜集合
     */
    
    @Valid
    @JsonProperty("ContainerList")
    private ArrayList<?> containerList = null;
    /**
     * 所属线路
     */
    
    @JsonProperty("lineId")
    private String lineId = null;
    /**
     * 线路名称
     */
    
    @JsonProperty("lineName")
    private String lineName = null;
    /**
     * 描述
     */
    
    @JsonProperty("remark")
    private String remark = null;
    /**
     * operatorId
     */
    @JsonProperty("operatorId")
    private String operatorId = null;
    /**
     * 运营人员名称
     */
    
    @JsonProperty("operatorName")
    private String operatorName = null;
    /**
     * 网络状态
     */
    
    @JsonProperty("networkState")
    private String networkState = null;
    /**
     * 售货机名称
     */
    
    @JsonProperty("vemName")
    private String vemName = null;
    /**
     * 创建日期
     */
    
    @JsonProperty("createTime")
    private String createTime = null;
    /**
     * 区域名称
     */
    
    @JsonProperty("areaName")
    private String areaName = null;
    /**
     * 租户id
     */
    
    @JsonProperty("tenantId")
    private String tenantId = null;
    /**
     * 位置
     */
    
    @JsonProperty("location")
    private String location = null;

    
    @JsonProperty("id")
    private String id = null;
    /**
     * 售货机状态
     */
    
    @JsonProperty("state")
    private String state = null;
    /**
     * 激活码
     */
    
    @JsonProperty("activatonCode")
    private String activatonCode = null;
    /**
     * 激活码
     */
    
    @JsonProperty("assetNum")
    private Integer assetNum = null;
    /**
     * country
     */
    @JsonProperty("country")
    private String country = null;
    /**
     * countryId
     */
    @JsonProperty("countryId")
    private String countryId;
    /**
     * city
     */
    @JsonProperty("city")
    private String city = null;
    /**
     * cityId
     */
    @JsonProperty("cityId")
    private String cityId;
    /**
     * province
     */
    @JsonProperty("province")
    private String province = null;
    /**
     * provinceId
     */
    @JsonProperty("provinceId")
    private String provinceId;
    /**
     * areaId
     */
    @JsonProperty("areaId")
    private String areaId;

    @JsonProperty("area")
    private String area;

    @JsonProperty("detailedAddress")
    private String detailedAddress = null;

    @JsonProperty("amount")
    private String amount;

    @JsonProperty("quantity")
    private String quantity;

    @JsonProperty("totalAmount")
    private String totalAmount;

    @JsonProperty("totalQuantity")
    private String totalQuantity;

    @JsonProperty("phone")
    private String phone;

    @JsonProperty("channelQuantity")
    private Integer channelQuantity = null;

    @JsonProperty("modelId")
    private String modelId = null;

    @JsonProperty("channelStartNo")
    private Integer channelStartNo = null;

    @JsonProperty("containerType")
    private String containerType = null;

    @JsonProperty("manufacturer")
    private String manufacturer = null;

    @JsonProperty("modelName")
    private String modelName = null;

    @JsonProperty("refrigeratorType")
    private String refrigeratorType = null;

    @JsonProperty("netWorkStart")
    private String netWorkStart = null;

    @JsonProperty("vemGroupId")
    private String vemGroupId = null;

    @JsonProperty("variable")
    private Boolean variable = null;

    @JsonProperty("assetId")
    private String assetId = null;

    @JsonProperty("tel")
    private String tel = null;

    @JsonProperty("deviceState")
    private String deviceState = null;// 设备状态，运维展示用
    /**
     * 点位id
     */
    
    @JsonProperty("pointLocationId")
    private String pointLocationId;

    
    @JsonProperty("pointLocationName")
    private String pointLocationName;

    
    @JsonProperty("machineType")
    private Integer machineType;

    /**
     * 声音开关boolean（true：开启 flase：关闭，1983默认开启，其他租户为关闭）
     */
    
    @JsonProperty("soundState")
    private boolean soundState;

    /**
     * 声音大小(0:最小，15最大，1983默认15，其他租户0)
     */
    
    @JsonProperty("volumeLevel")
    private int volumeLevel;

    /**
     * 二维码登录（true：开启 flase：关闭,1983默认开启，其他租户为关闭）
     */
    
    @JsonProperty("qrcodeState")
    private boolean qrcodeState;

    
    @JsonProperty("nature")
    private String nature;

    
    @JsonProperty("avgDay")
    private String avgDay;

    
    @JsonProperty("sceneCode")
    private String sceneCode;
    /**
     * 激活时间
     */
    
    @JsonProperty("activationTime")
    private String activationTime;
    /**
     * activationTimeStr
     */
    
    @JsonProperty("activationTimeStr")
    private String activationTimeStr;
}
