package com.snbc.bbpf.bus.product.manager.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductDRPAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductDRPCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsProductOrderDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.VemsUseTimeDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.VemsOrderDayQueryBase;
import com.snbc.bbpf.bus.product.market.common.vo.product.VemsOrderPageQueryBase;

import java.util.List;
/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: ProductOrderExService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 服务设备使用时长相关接口
 * @Author: LiangJb
 * @CreateDate: 2020/9/28 15:59
 */
public interface ProductOrderExService {
    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 关联设备时长获取已购设备订单列表
     * 合作运营需要的订单数据对象
     * @date: 2020/8/12 13:21
     * @param: productOrderPageQuery
     * @return: VemsProductOrderDto
     */
    PageInfo<VemsProductOrderDto> getOrderLstByVemsId(VemsOrderPageQueryBase recoud);
     /**
     * @author: liangJB
     * 功能描述: <br>
     * 合作运营需要的日报表订单数据对象
     *      * 以设备ID为查询条件，找出对应的购买服务记录以订单倒叙时间排序
     * @date: 2020/8/12 13:26
     * @param: tenantId
     * @return:  VemsProductOrderDto
     */
    List<VemsProductDRPCountDto> getOrderLstByPayType(VemsOrderDayQueryBase recoud) ;

    /**
     * @author: liangJB
     * 功能描述: <br>
     * 合作运营需要的日报表统计数据
     *      * 以设备ID为查询条件，找出对应的购买服务记录以订单倒叙时间排序
     * @date: 2020/8/12 13:26
     * @param: tenantId
     * @return:  VemsProductOrderDto
     */
    VemsProductDRPAllCount getOrderCountByPayType(VemsOrderDayQueryBase recoud);
    /**
     * @author: OY
     * 单个售货机添加时长
     * @param vemsUseTimeDto
     * @return
     */
    String addServiceTime(VemsUseTimeDto vemsUseTimeDto) throws BusinessException;

    /**
     * @author: OY
     * 批量添加售货机时长
     * @param vemsList
     * @return
     */
    List<String> addServiceTimeBatch(List<VemsUseTimeDto> vemsList) throws BusinessException;
}
