package com.snbc.bbpf.bus.product.market.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceDetailBaseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InvoiceApply extends InvoiceDetailBaseDto {
    //id
    private String invoiceApplyId;
    //开票编号
    private String invoiceCode;
    //租户Id
    
    private String tenantId;
    //快递信息Id
    
    private String invoiceDeliveryId;
    //开具类型 1:个人 2：企业
    
    private Integer applyType;
    
    //开票类型 1：增值税普通发票2：增值税专用发票
    private Integer invoiceType;
    //发票抬头
    
    private String invoiceTitle;
    //开票内容
    
    private String invoiceContent;
    //申请人
    
    private String applyUser;
    //开票介质 1：纸质发票 2：电子发票
    
    private Integer invoiceCarrier;
    //开票状态 0：已申请 1：已开票
    
    private Integer invoiceStatus;
    //开票金额
    
    private BigDecimal invoiceAmount;
    //申请时间
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime applyTime;
    //开票时间
    
    private LocalDateTime invoiceTime;

    //邮箱
    
    private String mail;
    //商户名称
    
    private String tenantName;
}
