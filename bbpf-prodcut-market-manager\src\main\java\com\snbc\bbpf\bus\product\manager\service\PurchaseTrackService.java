package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.market.common.vo.BaseOrderVo;

/**
 * @ProjectName: product-manager
 * @Package: com.snbc.vems.product.service
 * @ClassName: PurchaseTrackService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2021/3/17 13:43
 * @UpdateUser: wangsong
 * @UpdateDate: 2021/3/17 13:43
 */
public interface PurchaseTrackService {
    boolean savePurchaseTrack(BaseOrderVo orderVo, String purchaseNo, Integer purchaseStatus);
}
