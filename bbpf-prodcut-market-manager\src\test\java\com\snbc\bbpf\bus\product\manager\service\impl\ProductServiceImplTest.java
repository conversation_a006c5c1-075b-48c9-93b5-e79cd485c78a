package com.snbc.bbpf.bus.product.manager.service.impl;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.service.DictValueService;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDetailExDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductGradeDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductListDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductSimpleDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ServicePDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ServicePPDto;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import com.snbc.bbpf.bus.product.market.common.entity.ProductServices;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPermissionMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductQueryMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductServicesMapper;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductPageQuery;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

/**
 * @ClassName: ProductServiceImplTest
 * 服务市场
 * @module: si-bbpf-product-manage
 * @Author: wjc1
 * @date: 2021/11/19 14:00
 */
class ProductServiceImplTest {
    @Mock
    ProductQueryMapper productQueryMapper;
    @Mock
    ProductServicesMapper productServicesMapper;
    @Mock
    ProductPermissionMapper productPermissionMapper;
    @Mock
    DictValueService dictValueService;
    @InjectMocks
    ProductServiceImpl productServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    @DisplayName("服务市场查询服务列表-正常情况-新/旧服务")
    @Tag("@id:23505")
    @Tag("@author:wjc1")
    @Tag("@date:2021/11/19")
    void testSevicesListWithPage1() {
        List<ProductListDto> productList= Lists.newArrayList();
        ProductListDto dto1=new ProductListDto();
        dto1.setProductId("productId");
        dto1.setProductName("productName");
        dto1.setProductCode("productCode");
        dto1.setProductCategoryName("productCategoryName");
        dto1.setProductTypeName("productTypeName");
        dto1.setChargeTypeName("chargeTypeName");
        dto1.setPublishTime(LocalDateTime.of(2021, Month.NOVEMBER, 19, 14, 31, 23));
        productList.add(dto1);
        ProductListDto dto2=new ProductListDto();
        dto2.setProductId("productId");
        dto2.setProductName("productName");
        dto2.setProductCode("productCode");
        dto2.setProductCategoryName("productCategoryName");
        dto2.setProductTypeName("productTypeName");
        dto2.setChargeTypeName("chargeTypeName");
        dto2.setPublishTime(LocalDateTime.of(2021, Month.OCTOBER, 18, 14, 31, 23));
        productList.add(dto2);
        ProductGradeDto productGradeDto=new ProductGradeDto(
                "productId",
                "productGradeId",
                "grade",
                "天",
                new BigDecimal("0.01"),//折扣
                new BigDecimal("0.01"),//价格
                0);
        List<ProductGradeDto> productGradeDtoList = new ArrayList<>();
        productGradeDtoList.add(productGradeDto);
        try {
            when(dictValueService.selectDictValueByTypeCode("charge_type_unit")).thenReturn(Lists.newArrayList());
        } catch (Exception e) {
            e.printStackTrace();
        }
        when(productQueryMapper.selectProductListForTenant(any())).thenReturn(productList);
        when(productQueryMapper.selectDefaultGradeByProductIds(Mockito.anyList())).thenReturn(productGradeDtoList);
        PageInfo<ServicePDto> result = productServiceImpl.sevicesListWithPage(new ProductPageQuery());
        List<ServicePDto> productVoList=result.getList();
        Assertions.assertEquals(0, productVoList.get(0).getIsNewProduct());
        Assertions.assertEquals(0, productVoList.get(1).getIsNewProduct());
    }

    @Test
    @DisplayName("服务市场查询服务列表-正常情况-新/旧服务")
    @Tag("@id:23505")
    @Tag("@author:wjc1")
    @Tag("@date:2021/11/19")
    void testSevicesListWithPage2() {
        List<ProductListDto> productList= Lists.newArrayList();
        ProductListDto dto1=new ProductListDto();
        dto1.setProductId("productId");
        dto1.setProductName("productName");
        dto1.setProductCode("productCode");
        dto1.setProductCategoryName("productCategoryName");
        dto1.setProductTypeName("productTypeName");
        dto1.setChargeTypeName("chargeTypeName");
        dto1.setPublishTime(LocalDateTime.of(2020, Month.NOVEMBER, 19, 14, 31, 23));
        productList.add(dto1);
        ProductListDto dto2=new ProductListDto();
        dto2.setProductId("productId");
        dto2.setProductName("productName");
        dto2.setProductCode("productCode");
        dto2.setProductCategoryName("productCategoryName");
        dto2.setProductTypeName("productTypeName");
        dto2.setChargeTypeName("chargeTypeName");
        dto2.setPublishTime(LocalDateTime.of(2021, Month.OCTOBER, 18, 14, 31, 23));
        productList.add(dto2);
        ProductGradeDto productGradeDto=new ProductGradeDto(
                "productId",
                "productGradeId",
                "grade",
                "天",
                new BigDecimal("0.01"),//折扣
                new BigDecimal("0.01"),//价格
                0);
        List<ProductGradeDto> productGradeDtoList = new ArrayList<>();
        productGradeDtoList.add(productGradeDto);
        try {
            when(dictValueService.selectDictValueByTypeCode("charge_type_unit")).thenReturn(Lists.newArrayList());
        } catch (Exception e) {
            e.printStackTrace();
        }
        when(productQueryMapper.selectProductListForTenant(any())).thenReturn(productList);
        when(productQueryMapper.selectDefaultGradeByProductIds(Mockito.anyList())).thenReturn(productGradeDtoList);
        PageInfo<ServicePDto> result = productServiceImpl.sevicesListWithPage(new ProductPageQuery());
        List<ServicePDto> productVoList=result.getList();
        Assertions.assertEquals(0, productVoList.get(0).getIsNewProduct());
        Assertions.assertEquals(0, productVoList.get(1).getIsNewProduct());
        //Assertions.assertEquals("/"+productGradeDto.getGradeUnit(), productVoList.get(0).getDefaultPriceUnit());
    }

    @Test
    @DisplayName("服务市场查询服务列表-服务梯度不存在")
    @Tag("@id:23505")
    @Tag("@author:wjc1")
    @Tag("@date:2021/11/19")
    void testSevicesListWithPage3() {
        List<ProductListDto> productList= Lists.newArrayList();
        ProductListDto dto1=new ProductListDto();
        dto1.setProductId("productId");
        dto1.setProductName("productName");
        dto1.setProductCode("productCode");
        dto1.setProductCategoryName("productCategoryName");
        dto1.setProductTypeName("productTypeName");
        dto1.setChargeTypeName("chargeTypeName");
        dto1.setPublishTime(LocalDateTime.of(2020, Month.NOVEMBER, 19, 14, 31, 23));
        productList.add(dto1);
        ProductListDto dto2=new ProductListDto();
        dto2.setProductId("productId");
        dto2.setProductName("productName");
        dto2.setProductCode("productCode");
        dto2.setProductCategoryName("productCategoryName");
        dto2.setProductTypeName("productTypeName");
        dto2.setChargeTypeName("chargeTypeName");
        dto2.setPublishTime(LocalDateTime.of(2021, Month.OCTOBER, 18, 14, 31, 23));
        productList.add(dto2);

        when(productQueryMapper.selectProductListForTenant(any())).thenReturn(productList);
        when(productQueryMapper.selectDefaultGradeByProductId(anyString())).thenReturn(null);
        try {
            productServiceImpl.sevicesListWithPage(new ProductPageQuery());
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), dto1.getProductName()+Errors.PRODUCTNOTHASGRAGE.getMessage());
        }
    }

    @Test
    @DisplayName("服务市场查询服务列表详情-租户id为空")
    @Tag("@id:23505")
    @Tag("@author:wjc1")
    @Tag("@date:2021/11/19")
    void testSeviceTypeDetail_tenantNull() {
        ProductDetailExDto dto1=new ProductDetailExDto();
        dto1.setProductId("productId");
        dto1.setProductName("productName");
        dto1.setProductCode("productCode");
        dto1.setProductTypeName("productTypeName");
        dto1.setChargeTypeName("按天收费");

        when(productQueryMapper.selectGradeByProductId(anyString())).thenReturn(Collections.<ProductGradeDto>singletonList(
                new ProductGradeDto("productId", "productGradeId", "grade", "gradeUnit", new BigDecimal(0), new BigDecimal(0), 0)));
        when(productQueryMapper.getProductDetailExByProductId(anyString())).thenReturn(null);
        try {
            ProductDetailExDto result = productServiceImpl.seviceTypeDetail("productId","");
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), Errors.TENANTIDNULL.getMessage());
        }
    }
    @Test
    @DisplayName("服务市场查询服务列表详情-服务不存在")
    @Tag("@id:23505")
    @Tag("@author:wjc1")
    @Tag("@date:2021/11/19")
    void testSeviceTypeDetail() {
        ProductDetailExDto dto1=new ProductDetailExDto();
        dto1.setProductId("productId");
        dto1.setProductName("productName");
        dto1.setProductCode("productCode");
        dto1.setProductTypeName("productTypeName");
        dto1.setChargeTypeName("按天收费");

        when(productQueryMapper.selectGradeByProductId(anyString())).thenReturn(Collections.<ProductGradeDto>singletonList(
                new ProductGradeDto("productId", "productGradeId", "grade", "gradeUnit", new BigDecimal(0), new BigDecimal(0), 0)));
        when(productQueryMapper.getProductDetailExByProductId(anyString())).thenReturn(null);
        try {
            ProductDetailExDto result = productServiceImpl.seviceTypeDetail("productId","tenantId");
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), Errors.PRODUCTNOTEXSIT.getMessage());
        }
    }
    @Test
    @DisplayName("服务市场查询服务列表详情-服务存在-未购买过服务")
    @Tag("@id:23505")
    @Tag("@author:wjc1")
    @Tag("@date:2021/11/19")
    void testSeviceTypeDetail2() {
        ProductDetailExDto dto1=new ProductDetailExDto();
        dto1.setProductId("productId");
        dto1.setProductName("productName");
        dto1.setProductCode("productCode");
        dto1.setProductTypeName("productTypeName");
        dto1.setChargeTypeName("按天收费");

        List<ProductGradeDto> gradeLst = Collections.<ProductGradeDto>singletonList(
                new ProductGradeDto("productId", "productGradeId", "grade", "gradeUnit", new BigDecimal(0), new BigDecimal(0), 0));
        when(productQueryMapper.selectGradeByProductId(anyString())).thenReturn(gradeLst);
        when(productQueryMapper.getProductDetailExByProductId(anyString())).thenReturn(dto1);
        when(productQueryMapper.getProductCountByPIdAndTId("productId","tenantId")).thenReturn(null);
        try {
            when(dictValueService.selectDictValueByTypeCode("charge_type_unit")).thenReturn(Lists.newArrayList());
        } catch (Exception e) {
            e.printStackTrace();
        }
        ProductDetailExDto result = productServiceImpl.seviceTypeDetail("productId","tenantId");
        Assertions.assertEquals(false,result.isHasPurchaseProduct());
    }
    @Test
    @DisplayName("服务市场查询服务列表详情-服务存在-购买过服务")
    @Tag("@id:23505")
    @Tag("@author:wjc1")
    @Tag("@date:2021/11/19")
    void testSeviceTypeDetail3() {
        ProductDetailExDto dto1=new ProductDetailExDto();
        dto1.setProductId("productId");
        dto1.setProductName("productName");
        dto1.setProductCode("productCode");
        dto1.setProductTypeName("productTypeName");
        dto1.setChargeTypeName("按天收费");

        List<ProductGradeDto> gradeLst = Arrays.<ProductGradeDto>asList(
                new ProductGradeDto("productId","productGradeId", "grade", "gradeUnit", new BigDecimal(0), new BigDecimal(0), 0));
        when(productQueryMapper.selectGradeByProductId(anyString())).thenReturn(gradeLst);
        when(productQueryMapper.getProductDetailExByProductId(anyString())).thenReturn(dto1);
        List<ProductCountDto> list=Lists.newArrayList();
        list.add(new ProductCountDto());
        when(productQueryMapper.getProductCountByPIdAndTId("productId","tenantId")).thenReturn(list);
        try {
            when(dictValueService.selectDictValueByTypeCode("charge_type_unit")).thenReturn(Lists.newArrayList());
        } catch (Exception e) {
            e.printStackTrace();
        }
        ProductDetailExDto result = productServiceImpl.seviceTypeDetail("productId","tenantId");
        Assertions.assertEquals(true,result.isHasPurchaseProduct());
    }
    @Test
    @DisplayName("服务市场查询服务列表详情-服务存在")
    @Tag("@id:23505")
    @Tag("@author:wjc1")
    @Tag("@date:2021/11/19")
    void testSeviceTypeDetail5() {
        ProductDetailExDto dto1=new ProductDetailExDto();
        dto1.setProductId("productId");
        dto1.setProductName("productName");
        dto1.setProductCode("productCode");
        dto1.setProductTypeName("productTypeName");
        dto1.setChargeTypeName("按指定收费");

        List<ProductGradeDto> gradeLst = Collections.<ProductGradeDto>singletonList(
                new ProductGradeDto("productId", "productGradeId", "grade", "gradeUnit", new BigDecimal(0), new BigDecimal(0), 0));
        when(productQueryMapper.selectGradeByProductId(anyString())).thenReturn(gradeLst);
        when(productQueryMapper.getProductDetailExByProductId(anyString())).thenReturn(dto1);
        try {
            when(dictValueService.selectDictValueByTypeCode("charge_type_unit")).thenReturn(Lists.newArrayList());
        } catch (Exception e) {
            e.printStackTrace();
        }
        ProductDetailExDto result = productServiceImpl.seviceTypeDetail("productId","tenantId");
        //Assertions.assertEquals("", result.getProductGradeUnit());
    }

    @Test
    @DisplayName("服务市场查询服务列表-不含状态-正常情况")
    void testSeviceListWithPage_Success() {
        List<ProductListDto> productList = Lists.newArrayList();
        ProductListDto dto1 = new ProductListDto();
        dto1.setProductId("productId1");
        dto1.setProductName("productName1");
        dto1.setProductCode("productCode1");
        productList.add(dto1);
        
        when(productQueryMapper.selectProductList(any())).thenReturn(productList);
        
        PageInfo<ProductDto> result = productServiceImpl.seviceListWithPage(new ProductPageQuery());
        
        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getList().size());
    }

    @Test
    @DisplayName("服务市场查询服务列表-不含状态-空结果")
    void testSeviceListWithPage_EmptyResult() {
        when(productQueryMapper.selectProductList(any())).thenReturn(Lists.newArrayList());
        
        PageInfo<ProductDto> result = productServiceImpl.seviceListWithPage(new ProductPageQuery());
        
        Assertions.assertNotNull(result);
        Assertions.assertEquals(0, result.getList().size());
    }

    @Test
    @DisplayName("已购产品列表查询-正常情况")
    void testSevicesOrderListWithPage_Success() {
        List<ServicePPDto> productList = Lists.newArrayList();
        ServicePPDto dto1 = new ServicePPDto();
        dto1.setProductId("productId1");
        dto1.setProductName("productName1");
        productList.add(dto1);
        
        when(productQueryMapper.getProductLstByTranid(any())).thenReturn(productList);
        
        PageInfo<ServicePPDto> result = productServiceImpl.sevicesOrderListWithPage(new ProductPageQuery());
        
        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getList().size());
        Assertions.assertEquals("productId1", result.getList().get(0).getProductId());
    }

    @Test
    @DisplayName("已购产品列表查询-空结果")
    void testSevicesOrderListWithPage_EmptyResult() {
        when(productQueryMapper.getProductLstByTranid(any())).thenReturn(Lists.newArrayList());
        
        PageInfo<ServicePPDto> result = productServiceImpl.sevicesOrderListWithPage(new ProductPageQuery());
        
        Assertions.assertNotNull(result);
        Assertions.assertEquals(0, result.getList().size());
    }

    @Test
    @DisplayName("获取可编辑的简单服务信息-正常情况")
    void testSeviceDetail_Success() {
        ProductSimpleDto productSimpleDto = new ProductSimpleDto();
        productSimpleDto.setProductId("productId1");
        productSimpleDto.setProductName("productName1");
        
        Permission permission1 = new Permission();
        permission1.setPermissionId("perm1");
        permission1.setParentId("parent1");
        permission1.setPermissionType("1"); // 接口类型
        
        Permission permission2 = new Permission();
        permission2.setPermissionId("perm2");
        permission2.setParentId("parent1");
        permission2.setPermissionType("1"); // 接口类型
        
        List<Permission> permissions = Arrays.asList(permission1, permission2);
        
        when(productQueryMapper.getProductSimpleByProductId("productId1")).thenReturn(productSimpleDto);
        when(productPermissionMapper.selelctPermissionByProductId("productId1")).thenReturn(permissions);
        
        ProductSimpleDto result = productServiceImpl.seviceDetail("productId1");
        
        Assertions.assertNotNull(result);
        Assertions.assertEquals("productId1", result.getProductId());
        Assertions.assertNotNull(result.getPermissionInfo());
        Assertions.assertEquals(1, result.getPermissionInfo().size());
        Assertions.assertEquals("parent1", result.getPermissionInfo().get(0).getPermissionId());
        Assertions.assertEquals(2, result.getPermissionInfo().get(0).getChildIds().size());
    }

    @Test
    @DisplayName("获取可编辑的简单服务信息-服务不存在")
    void testSeviceDetail_ServiceNotExist() {
        when(productQueryMapper.getProductSimpleByProductId("nonExistentId")).thenReturn(null);
        
        try {
            productServiceImpl.seviceDetail("nonExistentId");
            Assertions.fail("BusinessException is expected");
        } catch (Exception e) {
            Assertions.assertEquals(Errors.SERVICEDOESNOTEXIST.getMessage(), e.getMessage());
        }
    }

    @Test
    @DisplayName("获取可编辑的简单服务信息-菜单类型权限")
    void testSeviceDetail_MenuPermission() {
        ProductSimpleDto productSimpleDto = new ProductSimpleDto();
        productSimpleDto.setProductId("productId1");
        
        Permission menuPermission = new Permission();
        menuPermission.setPermissionId("menu1");
        menuPermission.setParentId("parent1");
        menuPermission.setPermissionType("2"); // 菜单类型
        
        List<Permission> permissions = Arrays.asList(menuPermission);
        
        when(productQueryMapper.getProductSimpleByProductId("productId1")).thenReturn(productSimpleDto);
        when(productPermissionMapper.selelctPermissionByProductId("productId1")).thenReturn(permissions);
        
        ProductSimpleDto result = productServiceImpl.seviceDetail("productId1");
        
        Assertions.assertNotNull(result);
        Assertions.assertNotNull(result.getPermissionInfo());
        Assertions.assertEquals(1, result.getPermissionInfo().size());
        Assertions.assertEquals("menu1", result.getPermissionInfo().get(0).getPermissionId());
        Assertions.assertEquals(0, result.getPermissionInfo().get(0).getChildIds().size());
    }

    @Test
    @DisplayName("获取服务状态详情-正常情况")
    void testSeviceStatusDetail_Success() {
        ProductDetailDto productDetailDto = new ProductDetailDto();
        productDetailDto.setProductId("productId1");
        productDetailDto.setProductName("productName1");
        
        List<ProductGradeDto> gradeList = Arrays.asList(
            new ProductGradeDto("productId1", "gradeId1", "30", "天", new BigDecimal("0.8"), new BigDecimal("100.00"), 1)
        );
        
        when(productQueryMapper.getProductDetailByProductId("productId1")).thenReturn(productDetailDto);
        when(productQueryMapper.selectGradeByProductId("productId1")).thenReturn(gradeList);
        
        ProductDetailDto result = productServiceImpl.seviceStatusDetail("productId1");
        
        Assertions.assertNotNull(result);
        Assertions.assertEquals("productId1", result.getProductId());
        Assertions.assertNotNull(result.getProductGradeLst());
        Assertions.assertEquals(1, result.getProductGradeLst().size());
    }

    @Test
    @DisplayName("删除产品服务-正常情况")
    void testDelProductService_Success() {
        ProductServices productServices = new ProductServices();
        productServices.setProductId("productId1");
        productServices.setProductStatus(0); // 未发布状态
        
        when(productServicesMapper.selectByPrimaryKey("productId1")).thenReturn(productServices);
        
        productServiceImpl.delProductService("productId1");
        
        Mockito.verify(productServicesMapper).delProductService("productId1");
        Mockito.verify(productPermissionMapper).deleteByProductId("productId1");
    }

    @Test
    @DisplayName("删除产品服务-产品已发布不能删除")
    void testDelProductService_CannotDelete() {
        ProductServices productServices = new ProductServices();
        productServices.setProductId("productId1");
        productServices.setProductStatus(1); // 已发布状态
        
        when(productServicesMapper.selectByPrimaryKey("productId1")).thenReturn(productServices);
        
        try {
            productServiceImpl.delProductService("productId1");
            Assertions.fail("BusinessException is expected");
        } catch (Exception e) {
            Assertions.assertEquals(Errors.CANNOTDEL.getMessage(), e.getMessage());
        }
    }

    @Test
    @DisplayName("获取离线详情-正常情况")
    void testGetOfflineDetail_Success() {
        String expectedOfflineDetail = "offline detail content";
        
        when(productQueryMapper.getOfflineDetail()).thenReturn(expectedOfflineDetail);
        
        String result = productServiceImpl.getOfflineDetail();
        
        Assertions.assertEquals(expectedOfflineDetail, result);
        Mockito.verify(productQueryMapper).getOfflineDetail();
    }

    @Test
    @DisplayName("获取离线详情-返回空")
    void testGetOfflineDetail_EmptyResult() {
        when(productQueryMapper.getOfflineDetail()).thenReturn(null);
        
        String result = productServiceImpl.getOfflineDetail();
        
        Assertions.assertNull(result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme