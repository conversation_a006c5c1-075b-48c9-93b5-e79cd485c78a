package com.snbc.bbpf.bus.product.manager.config.payconfig.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 微信请求二维码请求实体
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class WechatQRCodeRequest {

    /**
     * 订单价格
     */
    @JsonProperty("amount")
    private BigDecimal amount;


    /**
     * 商品名称
     */
    @JsonProperty("subject")
    private String subject;


    /**
     * 过期时间
     */
    @JsonProperty("timeoutExpress")
    private String timeoutExpress;

    //业务订单号
    
    private String outTradeNo;

}

