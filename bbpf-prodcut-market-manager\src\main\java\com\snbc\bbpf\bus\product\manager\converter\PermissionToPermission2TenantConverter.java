package com.snbc.bbpf.bus.product.manager.converter;

import com.snbc.bbpf.bus.product.market.common.dto.order.Permission2Tenant;
import com.snbc.bbpf.bus.product.market.common.entity.Permission;
import com.snbc.bbpf.commons.objs.IConvert;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Permission 到 Permission2Tenant 的转换器
 */
@Mapper
public interface PermissionToPermission2TenantConverter extends IConvert<Permission, Permission2Tenant> {
    PermissionToPermission2TenantConverter INSTANCE = Mappers.getMapper(PermissionToPermission2TenantConverter.class);
}