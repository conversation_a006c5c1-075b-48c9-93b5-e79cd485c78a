package com.snbc.bbpf.bus.product.manager.service.impl;


import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.service.AuthorityService;
import com.snbc.bbpf.bus.product.manager.service.OrderService;
import com.snbc.bbpf.bus.product.manager.service.ProductPayService;
import com.snbc.bbpf.bus.product.manager.service.ProductService;
import com.snbc.bbpf.bus.product.manager.service.PurchaseTrackService;
import com.snbc.bbpf.bus.product.manager.service.RemittanceService;
import com.snbc.bbpf.bus.product.manager.utils.OrderServiceUtils;
import com.snbc.bbpf.bus.product.market.common.entity.ProductAvailable;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.entity.ProductServices;
import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductAvailableMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductDetailMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductServicesMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseOfflineMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchasePayTrackMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseTrackMapper;
import com.snbc.bbpf.bus.product.market.common.vo.OrderVo;
import org.springframework.context.ApplicationContext;
import com.snbc.bbpf.bus.product.market.common.vo.ProductOrderDetailVo;
import com.snbc.bbpf.bus.product.market.common.vo.RenewOrderVo;
import com.snbc.pay.entity.response.TradeCloseResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


public class OrderServiceImplTest {
    @InjectMocks
    private OrderService orderService;
    @Mock
    private ProductAvailableMapper productAvailableMapper;
    @Mock
    private ProductDetailMapper productDetailMapper;
    @Mock
    private ProductPurchaseMapper productPurchaseMapper;
    @Mock
    private PurchaseTrackMapper purchaseTrackMapper;
    @Mock
    private ProductService productService;
    @Mock
    private OrderServiceUtils orderServiceUtils;
    @Mock
    private ProductPayService productPayService;
    @Mock
    private RemittanceService remittanceService;
    @Mock
    private AuthorityService authorityService;
    @Mock
    private ProductServicesMapper productServicesMapper;
    @Mock
    private PurchaseTrackService purchaseTrackService;
    @Mock
    private PurchasePayTrackMapper purchasePayTrackMapper;
    @Mock
    private PurchaseOfflineMapper purchaseOfflineMapper;
    @Mock
    private ApplicationContext applicationContext;

    /**
     * 测试初始化
     *
     * @throws Exception
     */
    @Before
    public void setUp() throws Exception {

        orderService = new OrderServiceImpl();
        MockitoAnnotations.initMocks(this);
        PurchaseTrack purchaseTrack = new PurchaseTrack();
        Mockito.when(orderServiceUtils.getPurchaseTrackEntity()).thenReturn(purchaseTrack);
        Mockito.when(purchaseTrackMapper.insert(Mockito.any())).thenReturn(1);
        TradeCloseResponse response = new TradeCloseResponse();
        Mockito.when(productPayService.closeAliOrder(Mockito.anyString())).thenReturn(response);
        Mockito.when(productPayService.closeAliOrder(Mockito.anyString())).thenReturn(response);
        Mockito.when(productPurchaseMapper.updateByPurchaseNoSelective(Mockito.any())).thenReturn(1);
        // Mock PurchaseOfflineMapper to return null (no existing remittance)
        Mockito.when(purchaseOfflineMapper.selectByPurchaseNo(Mockito.anyString())).thenReturn(null);
        // Mock ApplicationContext for sendMsg method
        Mockito.doNothing().when(applicationContext).publishEvent(Mockito.any());
    }

    /**
     * 测试订单取消正常流程
     *
     * @throws Exception
     */
    @Test
    public void testOrderCancel_normal() throws Exception {
        ProductPurchase productPurchase=new ProductPurchase();
        // 设置必要的字段以避免NullPointerException
        productPurchase.setPurchaseStatus(Constant.PURCHASE_STATUS_WAITPAY); // 设置为待支付状态
        String purchaseNo="PD2020122400001";
        Mockito.when(productPurchaseMapper.selectByPurchaseNo(purchaseNo)).thenReturn(productPurchase);
        Boolean result = orderService.orderCancel(purchaseNo, Constant.PURCHASE_STATUS_TIMEOUT);
        List<PurchasePayTrack> trackList = new ArrayList<>();
        Mockito.when(purchasePayTrackMapper.selectPayTracks(Mockito.any())).thenReturn(trackList);
        Assert.assertEquals(true, result);
    }

    /**
     * 测试订单取消异常流程
     *
     * @throws Exception
     */
    @Test(expected = Exception.class)
    public void testOrderCancel_exception() throws Exception {
        Mockito.when(productPurchaseMapper.updateByPurchaseNoSelective(Mockito.any())).thenThrow(new Exception("取消失败"));
        orderService.orderCancel("ddd", Constant.PURCHASE_STATUS_TIMEOUT);
    }

    /**
     * 测试确认支付正常流程
     *
     * @throws BusinessException
     */
    @Test
    public void testPayComfirm_normal() throws BusinessException {
        ProductPurchase productPurchase = new ProductPurchase();
        // 设置必要的字段以避免NullPointerException
        productPurchase.setProductQuantity(1); // 设置购买数量
        productPurchase.setProductPrice(new BigDecimal("100.00")); // 设置产品价格
        productPurchase.setDiscountAmount(new BigDecimal("0.00")); // 设置优惠金额
        productPurchase.setProductId("PROD001"); // 设置产品ID
        productPurchase.setTenantId("TENANT001"); // 设置租户ID
        Mockito.when(productPurchaseMapper.selectByPurchaseNo(Mockito.anyString())).thenReturn(productPurchase);
        Mockito.when(orderServiceUtils.updateProductAvailable(Mockito.anyString())).thenReturn(false);
        Mockito.when(authorityService.authority(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(remittanceService).updateProductStatusOnRedis(Mockito.anyString(), Mockito.anyString());
        Mockito.when(productPurchaseMapper.updateByPurchaseNoSelective(productPurchase)).thenReturn(1);
        boolean result = orderService.payConfirm("PD2020122400001", 2);
        Assert.assertEquals(true, result);
    }

    /**
     * 测试确认支付异常
     *
     * @throws BusinessException
     */
    @Test(expected = BusinessException.class)
    public void testPayComfirm_exception() throws BusinessException {
        ProductPurchase productPurchase = new ProductPurchase();
        Mockito.when(orderServiceUtils.updateProductAvailable(Mockito.anyString())).thenReturn(false);
        Mockito.when(productPurchaseMapper.selectByPurchaseNo(Mockito.anyString())).thenReturn(null);
        Mockito.when(authorityService.authority(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(remittanceService).updateProductStatusOnRedis(Mockito.anyString(), Mockito.anyString());
        Mockito.when(productPurchaseMapper.updateByPurchaseNoSelective(productPurchase)).thenReturn(1);
        // 当 productPurchase 为 null 时，应该抛出 BusinessException，不需要断言返回值
        orderService.payConfirm("PD2020122400001", 1);
    }

    /**
     * 测试更新订单状态正常状态
     *
     * @throws BusinessException
     */
    @Test
    public void testUpdateStatus_normal() throws BusinessException {
        ProductPurchase productPurchase = new ProductPurchase();
        // 设置必要的字段以避免NullPointerException和BusinessException
        productPurchase.setPurchaseStatus(-1); // 设置为可更新状态（OrderServiceImpl.PURCHASE_STATUS = -1）
        Mockito.when(productPurchaseMapper.selectByPurchaseNo(Mockito.anyString())).thenReturn(productPurchase);
        Mockito.when(productPurchaseMapper.updateByPurchaseNoSelective(Mockito.any())).thenReturn(1);
        boolean result = orderService.updateStatus("PD2020122400001", 1);
        Assert.assertEquals(true, result);
    }

    /**
     * 测试更新订单状态线上支付
     *
     * @throws BusinessException
     */
    @Test
    public void testUpdateStatus_type_onlinepay() throws BusinessException {
        ProductPurchase productPurchase = new ProductPurchase();
        // 设置必要的字段以避免NullPointerException
        productPurchase.setPurchaseStatus(-1); // 设置为可更新状态（OrderServiceImpl.PURCHASE_STATUS = -1）
        productPurchase.setProductQuantity(1); // 设置购买数量
        productPurchase.setProductPrice(new BigDecimal("100.00")); // 设置产品价格
        productPurchase.setDiscountAmount(new BigDecimal("0.00")); // 设置优惠金额
        productPurchase.setProductId("PROD001"); // 设置产品ID
        productPurchase.setTenantId("TENANT001"); // 设置租户ID
        Mockito.when(productPurchaseMapper.updateByPurchaseNoSelective(Mockito.any())).thenReturn(1);
        Mockito.when(productPurchaseMapper.selectByPurchaseNo(Mockito.anyString())).thenReturn(productPurchase);
        Mockito.when(orderServiceUtils.updateProductAvailable(Mockito.anyString())).thenReturn(false);
        Mockito.when(authorityService.authority(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.doNothing().when(remittanceService).updateProductStatusOnRedis(Mockito.anyString(), Mockito.anyString());
        Mockito.when(productPurchaseMapper.updateByPurchaseNoSelective(productPurchase)).thenReturn(1);
        boolean result = orderService.updateStatus("PD2020122400001", 2);
        Assert.assertEquals(true, result);
    }

    /**
     * 测试更新订单状态异常
     *
     * @throws BusinessException
     */
    @Test(expected = BusinessException.class)
    public void testUpdateStatus_exception() throws BusinessException {
        Mockito.when(productPurchaseMapper.selectByPurchaseNo(Mockito.anyString())).thenReturn(null);
        Mockito.when(productPurchaseMapper.updateByPurchaseNoSelective(Mockito.any())).thenReturn(1);
        boolean result = orderService.updateStatus("PD2020122400001", 2);
        Assert.assertEquals(true, result);
    }

    /**
     * 测试下单没有订单详情
     *
     * @throws BusinessException
     */
    @Test
    public void testOrder_without_detail() throws BusinessException {
        OrderVo orderVo = new OrderVo();
        orderVo.setIsRenew(0);
        orderVo.setPayType(0);
        orderVo.setDiscountAmount(BigDecimal.ZERO);
        orderVo.setPaymentAmount(BigDecimal.ZERO);
        orderVo.setProductCode("FW00001");
        orderVo.setProductId("f5f5ddde-235b-4b8d-bc7e-35f17b2fb083");
        orderVo.setProductGrade("30");
        orderVo.setProductQuantity(1);
        orderVo.setTenantId("143");
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseNo("PD2020122400001");
        Mockito.when(orderServiceUtils.getProductPurchase(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(productPurchase);
        Mockito.when(productPurchaseMapper.insert(productPurchase)).thenReturn(1);
        Mockito.when(purchaseTrackService.savePurchaseTrack(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        ProductPurchase orderNo = orderService.order(orderVo);
        Assert.assertEquals("PD2020122400001", orderNo.getPurchaseNo());
    }

    /**
     * 未发布的产品下单异常测试
     *
     * @throws BusinessException
     */
    @Test(expected = BusinessException.class)
    public void testOrder_product_unpublished() {
        OrderVo orderVo = new OrderVo();
        orderVo.setIsRenew(0);
        orderVo.setPayType(0);
        orderVo.setDiscountAmount(BigDecimal.ZERO);
        orderVo.setPaymentAmount(BigDecimal.ZERO);
        orderVo.setProductCode("FW00001");
        orderVo.setProductId("f5f5ddde-235b-4b8d-bc7e-35f17b2fb083");
        orderVo.setProductGrade("30");
        orderVo.setProductQuantity(1);
        orderVo.setTenantId("143");
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseNo("PD2020122400001");
        ProductServices productService = new ProductServices();
        productService.setProductStatus(2);
        Mockito.when(productServicesMapper.selectByPrimaryKey("f5f5ddde-235b-4b8d-bc7e-35f17b2fb083")).thenReturn(productService);
        Mockito.when(orderServiceUtils.getProductPurchase(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(productPurchase);
        Mockito.when(productPurchaseMapper.insert(productPurchase)).thenReturn(1);
        Mockito.when(purchaseTrackService.savePurchaseTrack(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        orderService.order(orderVo);
    }

    /**
     * 下单异常测试
     *
     * @throws BusinessException
     */
    @Test(expected = Exception.class)
    public void testOrder_Exception() throws BusinessException {
        OrderVo orderVo = new OrderVo();
        orderVo.setIsRenew(0);
        orderVo.setPayType(0);
        orderVo.setDiscountAmount(BigDecimal.ZERO);
        orderVo.setPaymentAmount(BigDecimal.ZERO);
        orderVo.setProductCode("FW00001");
        orderVo.setProductId("f5f5ddde-235b-4b8d-bc7e-35f17b2fb083");
        orderVo.setProductGrade("30");
        orderVo.setProductQuantity(1);
        orderVo.setTenantId("143");
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseNo("PD2020122400001");
        Mockito.when(orderServiceUtils.getProductPurchase(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(productPurchase);
        Mockito.when(productPurchaseMapper.insert(productPurchase)).thenThrow(new Exception());
        ProductPurchase orderNo = orderService.order(orderVo);
        Assert.assertEquals("PD2020122400001", orderNo.getPurchaseNo());
    }

    /**
     * 测试下单有订单详情信息
     *
     * @throws Exception
     */
    @Test
    public void testOrder_with_detail() throws BusinessException {
        OrderVo orderVo = new OrderVo();
        orderVo.setIsRenew(0);
        orderVo.setPayType(0);
        orderVo.setDiscountAmount(BigDecimal.ZERO);
        orderVo.setPaymentAmount(BigDecimal.ZERO);
        orderVo.setProductCode("FW00001");
        orderVo.setProductId("f5f5ddde-235b-4b8d-bc7e-35f17b2fb083");
        orderVo.setProductGrade("30");
        orderVo.setProductQuantity(1);
        orderVo.setTenantId("143");
        List<ProductOrderDetailVo> products = new ArrayList<>();
        ProductOrderDetailVo productDetail = new ProductOrderDetailVo();
        productDetail.setTenantId("143");
        productDetail.setVemsName("测试售货机");
        productDetail.setAvailableUnit("天/台");
        productDetail.setBeginTime("2020-12-25 12:00");
        productDetail.setProductId("f5f5ddde-235b-4b8d-bc7e-35f17b2fb083");
        productDetail.setAvailableValue(30);
        products.add(productDetail);
        orderVo.setProducts(products);
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseNo("PD2020122400001");
        Mockito.when(productDetailMapper.insertBatch(Mockito.any())).thenReturn(1);
        Mockito.when(orderServiceUtils.getProductPurchase(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(productPurchase);
        Mockito.when(purchaseTrackService.savePurchaseTrack(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        Mockito.when(productPurchaseMapper.insert(productPurchase)).thenReturn(1);
        ProductPurchase orderNo = orderService.order(orderVo);
        Assert.assertEquals("PD2020122400001", orderNo.getPurchaseNo());
    }

    /**
     * 测试订单其他类型
     *
     * @throws Exception
     */
    @Test
    public void testOrder_with_prodcut_M() throws BusinessException {
        OrderVo orderVo = new OrderVo();
        orderVo.setIsRenew(0);
        orderVo.setPayType(0);
        orderVo.setDiscountAmount(BigDecimal.ZERO);
        orderVo.setPaymentAmount(BigDecimal.ZERO);
        orderVo.setProductCode("FW00002");
        orderVo.setProductId("f5f5ddde-235b-4b8d-bc7e-35f17b2fb083");
        orderVo.setProductGrade("30");
        orderVo.setProductQuantity(1);
        orderVo.setTenantId("143");
        List<ProductOrderDetailVo> products = new ArrayList<>();
        ProductOrderDetailVo productDetail = new ProductOrderDetailVo();
        productDetail.setTenantId("143");
        productDetail.setAvailableUnit("M");
        productDetail.setBeginTime("2020-12-25 12:00");
        productDetail.setProductId("f5f5ddde-235b-4b8d-bc7e-35f17b2fb083");
        productDetail.setAvailableValue(300);
        products.add(productDetail);
        orderVo.setProducts(products);
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseNo("PD2020122400001");
        Mockito.when(productDetailMapper.insertBatch(Mockito.any())).thenReturn(1);
        Mockito.when(orderServiceUtils.getProductPurchase(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(productPurchase);
        Mockito.when(productPurchaseMapper.insert(productPurchase)).thenReturn(1);
        Mockito.when(purchaseTrackService.savePurchaseTrack(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        ProductPurchase orderNo = orderService.order(orderVo);
        Assert.assertEquals("PD2020122400001", orderNo.getPurchaseNo());
    }

    /**
     * 测试正常的续费流程
     *
     * @throws Exception
     */
    @Test
    public void testRenew_normal() {
        RenewOrderVo orderVo = new RenewOrderVo();
        orderVo.setIsRenew(1);
        orderVo.setPayType(0);
        orderVo.setDiscountAmount(BigDecimal.ZERO);
        orderVo.setPaymentAmount(BigDecimal.ZERO);
        orderVo.setProductCode("FW00001");
        orderVo.setProductId("f5f5ddde-235b-4b8d-bc7e-35f17b2fb083");
        orderVo.setProductGrade("30");
        orderVo.setProductQuantity(1);
        orderVo.setTenantId("143");
        orderVo.setProducts("1222");
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseNo("PD2020122400001");
        List<ProductAvailable> productAvailables = new ArrayList<>();
        Mockito.when(productAvailableMapper.selectByVemsIds(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn(productAvailables);
        Mockito.when(orderServiceUtils.getProductPurchase(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(productPurchase);
        Mockito.when(productPurchaseMapper.insert(productPurchase)).thenReturn(1);
        Mockito.when(purchaseTrackService.savePurchaseTrack(Mockito.any(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        ProductPurchase orderNo = orderService.renew(orderVo);
        Assert.assertEquals("PD2020122400001", orderNo.getPurchaseNo());
    }
}
