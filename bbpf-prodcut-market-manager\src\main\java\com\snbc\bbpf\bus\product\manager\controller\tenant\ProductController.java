/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.controller.rrq
 * @ClassName: RRQProductController
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 人人取运营系统产品服务接口
 * @Author: ouyang，liangjunbin
 * @CreateDate: 2020/8/24 14:55
 * @UpdateUser: ouyang
 * @UpdateDate: 2020/10/10 14:55
 */
package com.snbc.bbpf.bus.product.manager.controller.tenant;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.manager.exception.Errors;
import com.snbc.bbpf.bus.product.manager.resp.product.ServiceOrderPage;
import com.snbc.bbpf.bus.product.manager.service.ProductService;
import com.snbc.bbpf.bus.product.manager.utils.ProductQueryUtil;
import com.snbc.bbpf.bus.product.manager.utils.ResultUtil;
import com.snbc.bbpf.bus.product.manager.utils.ResultVoUtil;
import com.snbc.bbpf.bus.product.market.common.dto.product.ServicePPDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ProductPageQueryEx;
import com.snbc.bbpf.bus.product.market.common.vo.productex.ServiceMarketQueryProduct;
import com.snbc.bbpf.bus.product.manager.config.CallResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController

@RequestMapping("/console/v1/tenant/productservice")
public class ProductController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProductController.class);
    @Autowired
    private ProductService productService;

    /**
     * @author: wjc
     * 功能描述: <br>
     * 查询产品服务市场列表
     * @date: 2021/11/18 13:21
     * @param: productId
     * @return: ServicePDto
     */
    
    @PutMapping(value = "/sevicesListWithPage", produces = {"application/json"})
    public CommonResp sevicesListWithPage(@RequestBody ServiceMarketQueryProduct query) {
        ProductPageQuery pageQuery = new ProductPageQuery();
        pageQuery.setPageNum(query.getPageNum());
        pageQuery.setPageSize(query.getPageSize());
        pageQuery.setProductStatus(ProductQueryUtil.ONE);
        pageQuery.setProductType(query.getProductType());
        pageQuery.setGradeTypeNo(query.getGradeTypeNo());
        return CommonResp.builder().head(ResultVoUtil.success()).body(productService.sevicesListWithPage(pageQuery)).build();
    }

    /**
     * @author: LiangJB
     * 功能描述: <br>
     * 2.5.1查询已购产品服务列表
     * @date: 2020/8/12 13:21
     * @param: productId
     * @return: ServicePPDto
     */
    
    @GetMapping(value = "/sevicesOrderListWithPage", produces = {"application/json"})
    public ServiceOrderPage sevicesOrderListWithPage(ProductPageQueryEx recordEx) {
        CallResponse result = new CallResponse();
        ServiceOrderPage serviceOrderPage = new ServiceOrderPage();
        ProductPageQuery pageQuery= ProductQueryUtil.convertProductPageQuery(recordEx);
        PageInfo<ServicePPDto> servicePPDtoLst = productService.sevicesOrderListWithPage(pageQuery);
        result.setCode(Errors.SUCCESS.getCode());
        result.setMessage(Errors.SUCCESS.getMessage());
        serviceOrderPage.setBody(servicePPDtoLst);
        serviceOrderPage.setHead(result);
        return serviceOrderPage;
    }

    /**
     * @author: wjc1
     * 功能描述: <br>
     * f服务市场查询产品服务详情
     * @date: 2021/12/09 13:21
     * @param: productId
     * @return: com.snbc.vems.product.dto.product.productDetailDto
     */
    
    @GetMapping(value = "/seviceStatusDetail", produces = {"application/json"})
    public CommonResp seviceRRQStatusDetail(@RequestParam("productId") String productId,@RequestParam("tenantId") String tenantId) {
        return CommonResp.builder().head(ResultVoUtil.success()).body(productService.seviceTypeDetail(productId,tenantId)).build();
    }
    /**
     * @author: liangjb
     * 功能描述: <br>
     * 获取线下支付配置详情
     * @date: 2021/12/09 13:21
     * @param: productId
     * @return: string
     */
    
    @GetMapping(value = "/getOfflineDetail", produces = {"application/json"})
    public CommonResp getOfflineDetail() {
        return CommonResp.builder().head(ResultVoUtil.success()).body(productService.getOfflineDetail()).build();
    }
}
