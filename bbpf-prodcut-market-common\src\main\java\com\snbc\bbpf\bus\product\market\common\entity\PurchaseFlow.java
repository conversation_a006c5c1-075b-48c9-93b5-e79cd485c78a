package com.snbc.bbpf.bus.product.market.common.entity;

import java.time.LocalDateTime;
/**
 * 购买生命周期表
 */
public class PurchaseFlow {
    /**
     * 流水编号
     */
    private String flowId;
    /**
     * 订单号
     */
    private String purchaseNo;
    /**
     * 订单状态
     */
    private Integer purchaseStatus;
    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

    public String getFlowId() {
        return flowId;
    }
    /**
     * 设置 flowId
     * @param flowId
     */
    public void setFlowId(String flowId) {
        this.flowId = flowId == null ? null : flowId.trim();
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }
    /**
     * 设置 purchaseNo
     * @param purchaseNo
     */
    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo == null ? null : purchaseNo.trim();
    }

    public Integer getPurchaseStatus() {
        return purchaseStatus;
    }
    /**
     * 设置 purchaseStatus
     * @param purchaseStatus
     */
    public void setPurchaseStatus(Integer purchaseStatus) {
        this.purchaseStatus = purchaseStatus;
    }

    public LocalDateTime getOperateTime() {
        return operateTime;
    }
    /**
     * 设置 operateTime
     * @param operateTime
     */
    public void setOperateTime(LocalDateTime operateTime) {
        this.operateTime = operateTime;
    }
}
