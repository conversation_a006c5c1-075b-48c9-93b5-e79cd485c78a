/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.exception;

import java.text.MessageFormat;

/** 
* @ClassName: BusinessExceptionAssert 
* @Description: 
* <AUTHOR> newbeiyang.com
* @date 2020年6月28日 下午3:12:29 
* @version V1.0 
*/
public interface BusinessExceptionAssert extends IResponseEnum, Assert {
	@Override
	default BusinessException objIsNullException(Object... args)  {
		String msg = MessageFormat.format(this.getMessage(), args);
		return new BusinessException(this,msg);
	}
	@Override
	default BusinessException objNotEqualException(Object... args)  {
		String msg = MessageFormat.format(this.getMessage(), args);
		return new BusinessException(this,msg);
	}
	
}
