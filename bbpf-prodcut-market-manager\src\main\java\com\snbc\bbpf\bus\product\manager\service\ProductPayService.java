package com.snbc.bbpf.bus.product.manager.service;

import com.snbc.bbpf.bus.product.manager.config.payconfig.request.AliQRCodeRequest;
import com.snbc.bbpf.bus.product.manager.config.payconfig.request.RefundRequest;
import com.snbc.bbpf.bus.product.manager.config.payconfig.request.WechatQRCodeRequest;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.pay.entity.response.TradeCloseResponse;
import com.snbc.pay.entity.response.TradeRefundResponse;

import java.security.NoSuchAlgorithmException;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.service
 * @ClassName: ProductPayConfigService
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 获取支付配置
 * @Author: MR.LI
 * @CreateDate: 2020/8/24 15:21
 * @UpdateUser: MR.LI
 * @UpdateDate: 2020/8/24 15:21
 */
public interface ProductPayService {

    /**
     * 获取支付宝二维码
     * @param aliQRCodeRequest
     * @return
     * @throws NoSuchAlgorithmException
     * @throws BusinessException
     */
    String getAlipayQRCode(AliQRCodeRequest aliQRCodeRequest) throws NoSuchAlgorithmException, BusinessException;
    /**
     * 获取微信二维码
     * @param wechatQRCodeRequest
     * @return
     * @throws NoSuchAlgorithmException
     * @throws BusinessException
     */
    String getWechatQRCode(WechatQRCodeRequest wechatQRCodeRequest) throws NoSuchAlgorithmException, BusinessException;

    /**
     * 退阿里的订单
     * @param tradeRefundRequest
     */
    TradeRefundResponse refundAliOrder(RefundRequest tradeRefundRequest) throws Exception;
    /**
     * 退wechat的订单
     * @param tradeRefundRequest
     */
    TradeRefundResponse refundWechatOrder(RefundRequest tradeRefundRequest) throws Exception;

    /**
     * 关闭阿里的订单
     * @param orderNum
     * @return
     */
    TradeCloseResponse closeAliOrder(String orderNum) throws Exception;
    /**
     * 关闭Wechat的订单
     * @param orderNum
     * @return
     */
    TradeCloseResponse closeWechatOrder(String orderNum) throws Exception;

    boolean lock(String key, int value, long releaseTime);

    void deleteLock(String key);

    void updateProductStatus(String key,String value);
}
