/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.controller
 * @ClassName: InvoiceController
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 人人取系统发票接口类
 * @Author: wangsong
 * @CreateDate: 2020/8/24 14:55
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/8/24 14:55
 */
package com.snbc.bbpf.bus.product.manager.controller.tenant;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.product.manager.config.CommonResp;
import com.snbc.bbpf.bus.product.manager.resp.invoice.InvoceExpressDeliResp;
import com.snbc.bbpf.bus.product.manager.resp.invoice.InvoiceCommonResp;
import com.snbc.bbpf.bus.product.manager.resp.invoice.InvoiceDetailResp;
import com.snbc.bbpf.bus.product.manager.resp.invoice.InvoicePurchasePageResp;
import com.snbc.bbpf.bus.product.manager.resp.invoice.RRQInvoiceListPage;
import com.snbc.bbpf.bus.product.manager.service.InvoiceDeliveryService;
import com.snbc.bbpf.bus.product.manager.service.InvoiceService;
import com.snbc.bbpf.bus.product.manager.utils.ResultVoUtil;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoceExpressDeliDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceDetailDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceListRRQDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoicePurchaseDto;
import com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoiceTitleDto;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.ApplyInvoiceParam;
import com.snbc.bbpf.bus.product.market.common.vo.invoice.InvoiceParamQuery;
import com.snbc.bbpf.buslog.annotations.Buslog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.io.IOException;

@RestController
@RequestMapping("/console/v1/tenant/invoice")
public class InvoiceController {

    private static final Logger LOGGER = LoggerFactory.getLogger(InvoiceController.class);

    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private InvoiceDeliveryService invoiceDeliveryService;

    /***
      * @Description:    查询发票列表
      * @Author:         wangsong
      * @param :         invoiceListDto
      * @CreateDate:     2020/8/24 16:38
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/8/24 16:38
      * @return :        CallResponse
     */
    
    @RequestMapping(value = "/invoiceListWithPage", method = RequestMethod.GET)
    public RRQInvoiceListPage invoiceListWithPage(InvoiceParamQuery invoiceParamQuery,
                                                  
                                                  @RequestParam String tenantId) throws IOException {
        RRQInvoiceListPage rrqInvoiceListPage = new RRQInvoiceListPage();
        invoiceParamQuery.setTenantId(tenantId);
        PageInfo<InvoiceListRRQDto> invoiceVoPageInfo = invoiceService.invoiceListWithPageForRRQ(invoiceParamQuery);
        rrqInvoiceListPage.setBody(invoiceVoPageInfo);
        rrqInvoiceListPage.setHead(ResultVoUtil.success());
        return rrqInvoiceListPage;
    }

    /**
      * @Description:    发票详情
      * @Author:         wangsong
      * @param :         invoiceApplyId
      * @CreateDate:     2020/8/25 13:54
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/8/25 13:54
      * @return :        CallResponse
     */
    
    @RequestMapping(value = "/invoiceDetail",method = RequestMethod.GET)
    public InvoiceDetailResp invoiceDetail(
                                           @RequestParam(value = "invoiceApplyId") String invoiceApplyId) throws Exception {
        InvoiceDetailResp invoiceDetailResp = new InvoiceDetailResp();
        InvoiceDetailDto invoiceDetailDto = invoiceService.invoiceDetail(invoiceApplyId);
        invoiceDetailResp.setBody(invoiceDetailDto);
        invoiceDetailResp.setHead(ResultVoUtil.success());
        return invoiceDetailResp;
    }

    /***
      * @Description:    发票订单信息
      * @Author:         wangsong
      * @param :         invoiceApplyId
      * @param :         pageSize
      * @param :         pageNum
      * @CreateDate:     2020/8/25 18:32
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/8/25 18:32
      * @return :        CallResponse
     */
    
    @RequestMapping(value = "/invoicePurchaseListWithPage",method = RequestMethod.GET)
    public InvoicePurchasePageResp invoicePurchaseListWithPage(
                                                    @RequestParam(value = "invoiceApplyId",required = false)String invoiceApplyId,
                                                    @RequestParam(value = "tenantId")String tenantId,
                                                    @RequestParam(value = "sortField",required = false)String sortField,
                                                    @RequestParam(value = "sequence",required = false)String sequence,
                                                    @RequestParam(value = "userId",required = false)String userId,
                                                    @RequestParam(value = "pageSize",defaultValue = "10")Integer pageSize,
                                                    @RequestParam(value = "pageNum",defaultValue = "1")Integer pageNum){
        InvoicePurchasePageResp invoicePurchasePageresp = new InvoicePurchasePageResp();
        PageInfo<InvoicePurchaseDto> invoicePurchaseDtoPageInfo =
                invoiceService.invoicePurchaseListWithPage(invoiceApplyId, pageSize, pageNum,tenantId,userId,sortField,sequence);
        invoicePurchasePageresp.setHead(ResultVoUtil.success());
        invoicePurchasePageresp.setBody(invoicePurchaseDtoPageInfo);
        return invoicePurchasePageresp;
    }

    /***
      * @Description:    发票申请接口
      * @Author:         wangsong
      * @param :         invoiceApply
      * @CreateDate:     2020/8/26 19:16
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/8/26 19:16
      * @return :        CallResponse
     */
    
    @RequestMapping(value = "/applyInvoice",method = RequestMethod.POST)
    @Buslog(opration = "applyInvoice", target = "invoiceManage", zh = "申请金额为［￥${invoiceAmount}］的发票",
    en = "The application is for an invoice amounting to [￥${invoiceAmount}]")
    public InvoiceCommonResp applyInvoice(@Valid @RequestBody ApplyInvoiceParam applyInvoiceParam)
            throws Exception {
        InvoiceCommonResp invoiceCommonResp = new InvoiceCommonResp();
        invoiceService.applyInvoice(applyInvoiceParam);
        invoiceCommonResp.setHead(ResultVoUtil.success());
        return invoiceCommonResp;
    }

    
    @RequestMapping(value = "/invoiceTitle",method = RequestMethod.GET)
    public CommonResp<InvoiceTitleDto> queryinvoiceTitle(
                                   @RequestParam(value = "tenantId")String tenantId) throws IOException {
        InvoiceTitleDto invoiceTitleDto = invoiceService.getInvoiceTitle(tenantId);
        CommonResp<InvoiceTitleDto> commonResp = new CommonResp<>();
        commonResp.setHead(ResultVoUtil.success());
        commonResp.setBody(invoiceTitleDto);
        return commonResp;
    }

    /***
      * @Description:    查看发票物流信息
      * @Author:         wangsong
      * @CreateDate:     2020/8/26 20:56
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/8/26 20:56
      * @return :        void
     */
    
    @RequestMapping(value = "/logisticsDetail",method = RequestMethod.GET)
    public InvoceExpressDeliResp logisticsDetail(
                                        @RequestParam(value = "invoiceApplyId")String invoiceApplyId) throws IOException {
        InvoceExpressDeliResp invoceExpressDeliResp = new InvoceExpressDeliResp();
        InvoceExpressDeliDto invoceExpressDeliDto = invoiceDeliveryService.logisticsDetail(invoiceApplyId);
        invoceExpressDeliResp.setBody(invoceExpressDeliDto);
        invoceExpressDeliResp.setHead(ResultVoUtil.success());
        return invoceExpressDeliResp;
    }
}
