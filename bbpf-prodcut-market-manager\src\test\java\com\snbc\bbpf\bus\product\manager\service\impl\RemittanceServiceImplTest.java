package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.manager.config.Constant;
import com.snbc.bbpf.bus.product.manager.exception.BusinessException;
import com.snbc.bbpf.bus.product.manager.service.ProductPayService;
import com.snbc.bbpf.bus.product.manager.service.PurchasePayTrackService;
import com.snbc.bbpf.bus.product.manager.utils.OrderServiceUtils;
import com.snbc.bbpf.bus.product.market.common.dto.order.RemittanceDto;
import com.snbc.bbpf.bus.product.market.common.dto.product.ProductSimpleDto;
import com.snbc.bbpf.bus.product.market.common.entity.ProductDetail;
import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseOffline;
import com.snbc.bbpf.bus.product.market.common.entity.PurchasePayTrack;
import com.snbc.bbpf.bus.product.market.common.entity.PurchaseTrack;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductDetailMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductQueryMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseOfflineMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.PurchaseTrackMapper;
import com.snbc.bbpf.bus.product.market.common.vo.RemittanceVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * RemittanceServiceImpl 单元测试
 */
@DisplayName("汇款单服务实现类测试")
class RemittanceServiceImplTest {

    @Mock
    private PurchasePayTrackService purchasePayTrackService;
    
    @Mock
    private PurchaseOfflineMapper purchaseOfflineMapper;
    
    @Mock
    private ProductPurchaseMapper productPurchaseMapper;
    
    @Mock
    private PurchaseTrackMapper purchaseTrackMapper;
    
    @Mock
    private OrderServiceUtils orderServiceUtils;
    
    @Mock
    private ProductPayService productPayService;
    
    @Mock
    private ProductDetailMapper productDetailMapper;
    
    @Mock
    private ProductQueryMapper productQueryMapper;

    @InjectMocks
    private RemittanceServiceImpl remittanceService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("获取汇款单信息 - 成功")
    void testGetRemittance_Success() {
        // Given
        String purchaseNo = "PO123456";
        PurchaseOffline purchaseOffline = new PurchaseOffline();
        purchaseOffline.setOfflineId("offline123");
        purchaseOffline.setPurchaseNo(purchaseNo);
        purchaseOffline.setRemitTime(LocalDateTime.now());
        purchaseOffline.setRemitProof("proof123");
        purchaseOffline.setRemitBankNo("bank123");
        purchaseOffline.setRemitTradeNo("trade123");
        
        when(purchaseOfflineMapper.selectByPurchaseNo(purchaseNo)).thenReturn(purchaseOffline);
        
        // When
        RemittanceDto result = remittanceService.getRemittance(purchaseNo);
        
        // Then
        assertNotNull(result);
        assertEquals(purchaseOffline.getOfflineId(), result.getId());
        assertEquals(purchaseOffline.getPurchaseNo(), result.getPurchaseNo());
        assertEquals(purchaseOffline.getRemitTime(), result.getRemitTime());
        assertEquals(purchaseOffline.getRemitProof(), result.getRemitProof());
        assertEquals(purchaseOffline.getRemitBankNo(), result.getRemitBankNo());
        assertEquals(purchaseOffline.getRemitTradeNo(), result.getRemitTradeNo());
    }

    @Test
    @DisplayName("获取汇款单信息 - 未找到")
    void testGetRemittance_NotFound() {
        // Given
        String purchaseNo = "NONEXISTENT";
        
        when(purchaseOfflineMapper.selectByPurchaseNo(purchaseNo)).thenReturn(null);
        
        // When
        RemittanceDto result = remittanceService.getRemittance(purchaseNo);
        
        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("汇款单提交 - 成功")
    void testRemittanceSubmit_Success() {
        // Given
        RemittanceVo remittanceVo = new RemittanceVo();
        remittanceVo.setPurchaseNo("PO123456");
        remittanceVo.setRemitTime("2023-12-01");
        remittanceVo.setRemitBankName("测试银行");
        remittanceVo.setRemitBankNo("*********");
        remittanceVo.setRemitTradeNo("TRADE123");
        remittanceVo.setRemitProof("proof.jpg");
        
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseNo("PO123456");
        productPurchase.setPurchaseStatus(1); // 未支付状态
        
        PurchaseTrack purchaseTrack = new PurchaseTrack();
        
        when(productPurchaseMapper.selectByPurchaseNo("PO123456")).thenReturn(productPurchase);
        when(purchaseOfflineMapper.selectByPurchaseNo("PO123456")).thenReturn(null);
        when(purchaseOfflineMapper.insert(any(PurchaseOffline.class))).thenReturn(1);
        when(purchasePayTrackService.selectPayTracks(any(PurchasePayTrack.class))).thenReturn(Collections.emptyList());
        when(productPurchaseMapper.updateByPurchaseNoSelective(any(ProductPurchase.class))).thenReturn(1);
        when(orderServiceUtils.getPurchaseTrackEntity()).thenReturn(purchaseTrack);
        when(purchaseTrackMapper.insertSelective(any(PurchaseTrack.class))).thenReturn(1);
        
        // When
        ProductPurchase result = remittanceService.remittanceSubmit(remittanceVo);
        
        // Then
        assertNotNull(result);
        assertEquals("PO123456", result.getPurchaseNo());
        assertEquals(Constant.PURCHASE_STATUS_WAITCONFIRM, result.getPurchaseStatus());
        assertEquals(Constant.PAY_TYPE_OFFLINE, result.getPayType());
    }

    @Test
    @DisplayName("汇款单提交 - 订单不存在")
    void testRemittanceSubmit_OrderNotExist() {
        // Given
        RemittanceVo remittanceVo = new RemittanceVo();
        remittanceVo.setPurchaseNo("NONEXISTENT");
        
        when(productPurchaseMapper.selectByPurchaseNo("NONEXISTENT")).thenReturn(null);
        
        // When & Then
        assertThrows(BusinessException.class, () -> {
            remittanceService.remittanceSubmit(remittanceVo);
        });
    }

    @Test
    @DisplayName("汇款单提交 - 订单已取消")
    void testRemittanceSubmit_OrderCancelled() {
        // Given
        RemittanceVo remittanceVo = new RemittanceVo();
        remittanceVo.setPurchaseNo("PO123456");
        
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseNo("PO123456");
        productPurchase.setPurchaseStatus(Constant.PURCHASE_STATUS_CANCEL);
        
        when(productPurchaseMapper.selectByPurchaseNo("PO123456")).thenReturn(productPurchase);
        
        // When & Then
        assertThrows(BusinessException.class, () -> {
            remittanceService.remittanceSubmit(remittanceVo);
        });
    }

    @Test
    @DisplayName("汇款单提交 - 订单已支付")
    void testRemittanceSubmit_OrderPaid() {
        // Given
        RemittanceVo remittanceVo = new RemittanceVo();
        remittanceVo.setPurchaseNo("PO123456");
        
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseNo("PO123456");
        productPurchase.setPurchaseStatus(2); // 已支付状态
        
        when(productPurchaseMapper.selectByPurchaseNo("PO123456")).thenReturn(productPurchase);
        
        // When & Then
        assertThrows(BusinessException.class, () -> {
            remittanceService.remittanceSubmit(remittanceVo);
        });
    }

    @Test
    @DisplayName("汇款单提交 - 汇款单已存在")
    void testRemittanceSubmit_RemittanceExists() {
        // Given
        RemittanceVo remittanceVo = new RemittanceVo();
        remittanceVo.setPurchaseNo("PO123456");
        
        ProductPurchase productPurchase = new ProductPurchase();
        productPurchase.setPurchaseNo("PO123456");
        productPurchase.setPurchaseStatus(1);
        
        PurchaseOffline existingOffline = new PurchaseOffline();
        existingOffline.setPurchaseNo("PO123456");
        
        when(productPurchaseMapper.selectByPurchaseNo("PO123456")).thenReturn(productPurchase);
        when(purchaseOfflineMapper.selectByPurchaseNo("PO123456")).thenReturn(existingOffline);
        
        // When & Then
        assertThrows(BusinessException.class, () -> {
            remittanceService.remittanceSubmit(remittanceVo);
        });
    }

    @Test
    @DisplayName("更新Redis产品状态 - 成功")
    void testUpdateProductStatusOnRedis_Success() {
        // Given
        String productId = "product123";
        String purchaseNo = "PO123456";
        
        ProductDetail productDetail = new ProductDetail();
        productDetail.setTenantId("tenant123");
        productDetail.setVemsId("vems123");
        
        ProductSimpleDto productSimpleDto = new ProductSimpleDto();
        productSimpleDto.setProductCode("PROD001");
        
        List<ProductDetail> productDetails = Arrays.asList(productDetail);
        
        when(productDetailMapper.selectByPurchaseNo(purchaseNo)).thenReturn(productDetails);
        when(productQueryMapper.getProductSimpleByProductId(productId)).thenReturn(productSimpleDto);
        
        // When
        remittanceService.updateProductStatusOnRedis(productId, purchaseNo);
        
        // Then
        String expectedRedisKey = Constant.PRODUCT_AVAILABLE_PREFIX + "_tenant123_vems123_PROD001";
        verify(productPayService).updateProductStatus(expectedRedisKey, Constant.PRODUCT_AVAILABLE_AVAILABLE);
    }

    @Test
    @DisplayName("更新Redis产品状态 - 无产品详情")
    void testUpdateProductStatusOnRedis_NoProductDetails() {
        // Given
        String productId = "product123";
        String purchaseNo = "PO123456";
        
        when(productDetailMapper.selectByPurchaseNo(purchaseNo)).thenReturn(Collections.emptyList());
        
        // When
        remittanceService.updateProductStatusOnRedis(productId, purchaseNo);
        
        // Then
        verify(productPayService, never()).updateProductStatus(anyString(), anyString());
    }

    @Test
    @DisplayName("更新Redis产品状态 - VemsId为空")
    void testUpdateProductStatusOnRedis_EmptyVemsId() {
        // Given
        String productId = "product123";
        String purchaseNo = "PO123456";
        
        ProductDetail productDetail = new ProductDetail();
        productDetail.setTenantId("tenant123");
        productDetail.setVemsId("");
        
        ProductSimpleDto productSimpleDto = new ProductSimpleDto();
        productSimpleDto.setProductCode("PROD001");
        
        List<ProductDetail> productDetails = Arrays.asList(productDetail);
        
        when(productDetailMapper.selectByPurchaseNo(purchaseNo)).thenReturn(productDetails);
        when(productQueryMapper.getProductSimpleByProductId(productId)).thenReturn(productSimpleDto);
        
        // When
        remittanceService.updateProductStatusOnRedis(productId, purchaseNo);
        
        // Then
        String expectedRedisKey = Constant.PRODUCT_AVAILABLE_PREFIX + "_tenant123_PROD001";
        verify(productPayService).updateProductStatus(expectedRedisKey, Constant.PRODUCT_AVAILABLE_AVAILABLE);
    }
}