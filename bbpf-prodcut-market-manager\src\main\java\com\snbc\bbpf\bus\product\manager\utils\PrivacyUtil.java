package com.snbc.bbpf.bus.product.manager.utils;

import com.github.pagehelper.util.StringUtil;
import com.snbc.bbpf.bus.product.manager.config.Constant;
import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.utils
 * @ClassName: PrivacyUtil
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 脱敏数据处理
 * @Author: wangsong
 * @CreateDate: 2020/10/27 17:08
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/27 17:08
 */
public class PrivacyUtil {

    private static final Integer FOUR = 4;
    private static final Integer THREE = 3;
    //用于匹配手机号码
    private static final String REGEXMOBILEPHONE = "^0?1[1-9]\\d{9}$";
    //用于匹配固定电话号码
    private static final String REGEXFIXEDPHONE = "^[0-9]{5,17}$";
    private static final String AREAFIXEDPHONE = "^[0-9]{0,3}-[0-9]{3,17}$";
    private static final Pattern PATTERNMOBILEPHONE;
    private static final Pattern PATTERNFIXEDPHONE;
    private static final Pattern PATTERNAREAFIXEDPHONE;

    static {
        PATTERNFIXEDPHONE = Pattern.compile(REGEXFIXEDPHONE);
        PATTERNMOBILEPHONE = Pattern.compile(REGEXMOBILEPHONE);
        PATTERNAREAFIXEDPHONE = Pattern.compile(AREAFIXEDPHONE);
    }


    /***
     * @Author: wangsong
     * @param :         bankCardNum
     * @CreateDate: 2020/8/27 16:15
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 16:15
     * @return :        java.lang.String
     */
    public static String encryBankCardNum(String bankCardNum) {
        if (StringUtil.isEmpty(bankCardNum)) {
            return "";
        }
        if (bankCardNum.length() > FOUR) {
            String startNum = bankCardNum.substring(0, FOUR);
            String endNum = bankCardNum.substring(bankCardNum.length() - FOUR);
            bankCardNum = startNum + "******" + endNum;
        }
        return bankCardNum;
    }

    /***
     * @Description: 纳税人识别码隐私处理
     * @Author: wangsong
     * @param :         taxRegisterNo
     * @CreateDate: 2020/9/28 15:53
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/10/28 15:53
     * @return :        java.lang.String
     */
    public static String encryTaxRegisterNo(String taxRegisterNo) {
        if (StringUtil.isEmpty(taxRegisterNo)) {
            return "";
        }
        if (taxRegisterNo.length() > Constant.TEN) {
            String startNum = taxRegisterNo.substring(0, FOUR);
            String endNum = taxRegisterNo.substring(taxRegisterNo.length() - FOUR);
            taxRegisterNo = startNum + "********" + endNum;
        }
        return taxRegisterNo;
    }

    /**
     * 脱敏规则——手机号码(手机号码隐藏中间4位数)
     *
     * @param telNum
     * @return
     * @Description:
     * @ReturnType String
     * @author: wangsong
     * @Created 2020年11月2日 上午10:25:10
     */
    public static String encryPhoneNum(String telNum) {
        if (StringUtils.isNotBlank(telNum)) {
            if (isCellPhone(telNum)) {
                return telNum.substring(0, THREE) + "****" + telNum.substring(telNum.length() - FOUR);
            }
            if (isFixedPhone(telNum)) {
                return "****" + telNum.substring(telNum.length() - FOUR);
            }
        }
        return telNum;
    }

    public static boolean isCellPhone(String number) {
        Matcher match = PATTERNMOBILEPHONE.matcher(number);
        return match.matches();
    }

    /**
     * 判断是否为固定电话号码
     *
     * @param number 固定电话号码
     * @return
     */
    public static boolean isFixedPhone(String number) {
        Matcher fixedmatch = PATTERNFIXEDPHONE.matcher(number);
        Matcher areaFixedmatch = PATTERNAREAFIXEDPHONE.matcher(number);
        return fixedmatch.matches() || areaFixedmatch.matches();
    }
}
