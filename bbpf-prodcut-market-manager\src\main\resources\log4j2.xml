<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" name="XMLConfigTest" packages="org.apache.logging.log4j.test" monitorInterval="10" >
    <Properties>
        <Property name="pattern">%d{yyyy-MM-dd HH:mm:ss SSS} [%-5level] [${MODULE_NAME}] [%-40t] [%-36X{userId}] [%-40.40c{39}] [%-40M] [%-5L] --- %m%n</Property>
        <property name="MODULE_NAME">bbpf-bus-product-market</property>
        <property name="LOG_HOME">/app/data/snbclogs/bbpf/bbpf-bus-product-market</property>
    </Properties>

    <Appenders>
        <Console name="STDOUT">
            <PatternLayout>
                <pattern>${pattern}</pattern>
            </PatternLayout>
         </Console>
        <RollingFile name="RF" fileName="${LOG_HOME}/${MODULE_NAME}.log"
                     filePattern="${LOG_HOME}/%d{yyyy-MM}/${MODULE_NAME}_%d{yyyy-MM-dd-HH}_%i.log.gz">
            <PatternLayout
                    pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true"
                                           interval="1" />
                <SizeBasedTriggeringPolicy size="100MB"/>
                <CronTriggeringPolicy schedule="0 0 * * * ? *"/> <!-- 这里是每小时监测一次 -->
            </Policies>
            <DefaultRolloverStrategy max="24">
                <Delete basePath="${LOG_HOME}" maxDepth="3">
                    <IfFileName glob="*/${MODULE_NAME}-*.log.gz"/>
                    <IfAny>
                        <IfAccumulatedFileSize exceeds="2GB" /><!-- 文件总大小超过2G -->
                        <IfAccumulatedFileCount exceeds="5000" /><!-- 总文件超过5000个 -->
                    </IfAny>
                    <IfLastModified age="P30D"/><!-- 这里保留30天 -->
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </Appenders>
    <GRPCLogClientAppender name="grpc-log">
        <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} [%traceId] - %msg%n"/>
    </GRPCLogClientAppender>
    <Loggers>
        <Logger name="com.snbc.bbpf" level="INFO" additivity="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Logger name="com.snbc.bbpf.bus.system" level="INFO" additivity="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Logger name="org.springframework" level="WARN" additivity="false" includeLocation="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Root level="INFO">
            <AppenderRef ref="STDOUT"/>
            <AppenderRef ref="RF"/>
        </Root>
    </Loggers>

</Configuration>