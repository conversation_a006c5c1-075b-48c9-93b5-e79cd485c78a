package com.snbc.bbpf.bus.product.manager.utils;


import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.Reader;
import java.io.StringWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class StreamUtil {
    private static final int DEFAULT_BUFFER_SIZE = 8192;
    private static final int BYTE_SIZE = 4096;

    /**
     * 功能描述: <br>
     * 〈处理io流〉
     *
     * @throws
     * @param: in
     * @param: out
     * @return: void
     * @author: gs
     * @date: 2019-08-06 16:15
     */
    public static void io(InputStream in, OutputStream out) throws IOException {
        io(in, out, -1);
    }

    public static void io(InputStream in, OutputStream out, int bufferSize) throws IOException {
        if (bufferSize == -1) {
            bufferSize = DEFAULT_BUFFER_SIZE;
        }

        byte[] buffer = new byte[bufferSize];
        int amount;

        while ((amount = in.read(buffer)) >= 0) {
            out.write(buffer, 0, amount);
        }
    }

    public static void io(Reader in, Writer out) throws IOException {
        io(in, out, -1);
    }

    public static void io(Reader in, Writer out, int bufferSize) throws IOException {
        if (bufferSize == -1) {
            bufferSize = DEFAULT_BUFFER_SIZE >> 1;
        }

        char[] buffer = new char[bufferSize];
        int amount;

        while ((amount = in.read(buffer)) >= 0) {
            out.write(buffer, 0, amount);
        }
    }

    public static OutputStream synchronizedOutputStream(OutputStream out) {
        return new SynchronizedOutputStream(out);
    }

    public static OutputStream synchronizedOutputStream(OutputStream out, Object lock) {
        return new SynchronizedOutputStream(out, lock);
    }

    public static String readText(InputStream in) throws IOException {
        return readText(in, null, -1);
    }

    public static String readText(InputStream in, String encoding) throws IOException {
        return readText(in, encoding, -1);
    }

    /**
     * 功能描述: <br>
     * 〈读取流数据〉
     *
     * @throws
     * @param: in
     * @param: encoding
     * @param: bufferSize
     * @return: java.lang.String
     * @author: gs
     * @date: 2019-08-06 16:16
     */
    public static String readText(InputStream in, String encoding, int bufferSize)
            throws IOException {
        Reader reader = (encoding == null) ? new InputStreamReader(in, StandardCharsets.UTF_8) : new InputStreamReader(in,
                encoding);

        return readText(reader, bufferSize);
    }

    public static String readText(Reader reader) throws IOException {
        return readText(reader, -1);
    }

    public static String readText(Reader reader, int bufferSize) throws IOException {
        StringWriter writer = new StringWriter();

        io(reader, writer, bufferSize);
        return writer.toString();
    }



    /**
     * 功能描述: <br>
     * 〈获取stream流〉
     *
     * @throws
     * @param: sInputString
     * @return: java.io.InputStream
     * @author: gs
     * @date: 2019-08-06 16:16
     */
    public static InputStream getStringStream(String sInputString) {
        ByteArrayInputStream tInputStringStream = null;
        if (sInputString != null && !"".equals(sInputString.trim())) {
            tInputStringStream = new ByteArrayInputStream(sInputString.getBytes(StandardCharsets.UTF_8));
        }
        return tInputStringStream;
    }

    public static final String inputStream2String(InputStream in) throws IOException {
        if (in == null) {
            return "";
        }
        StringBuilder out = new StringBuilder();
        byte[] b = new byte[BYTE_SIZE];
        for (int n; (n = in.read(b)) != -1; ) {
            out.append(new String(b, 0, n, StandardCharsets.UTF_8));
        }
        return out.toString();
    }
}
