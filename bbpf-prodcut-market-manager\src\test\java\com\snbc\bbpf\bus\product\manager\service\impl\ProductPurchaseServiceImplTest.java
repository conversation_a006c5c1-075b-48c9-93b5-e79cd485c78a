package com.snbc.bbpf.bus.product.manager.service.impl;

import com.snbc.bbpf.bus.product.market.common.entity.ProductPurchase;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * ProductPurchaseServiceImpl 单元测试
 */
@DisplayName("产品采购服务实现类测试")
class ProductPurchaseServiceImplTest {

    @Mock
    private ProductPurchaseMapper productPurchaseMapper;

    @InjectMocks
    private ProductPurchaseServiceImpl productPurchaseService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("根据采购单号查询 - 成功")
    void testSelectByPurchaseNo_Success() {
        // Given
        String purchaseNo = "PO123456";
        ProductPurchase expectedPurchase = new ProductPurchase();
        expectedPurchase.setProductPurchaseId("purchase123");
        expectedPurchase.setPurchaseNo(purchaseNo);
        expectedPurchase.setTenantId("tenant123");
        expectedPurchase.setUserId("user123");
        expectedPurchase.setPurchaseAmount(new BigDecimal("100.00"));
        
        when(productPurchaseMapper.selectByPurchaseNo(purchaseNo)).thenReturn(expectedPurchase);
        
        // When
        ProductPurchase result = productPurchaseService.selectByPurchaseNo(purchaseNo);
        
        // Then
        assertNotNull(result);
        assertEquals(expectedPurchase.getProductPurchaseId(), result.getProductPurchaseId());
        assertEquals(expectedPurchase.getPurchaseNo(), result.getPurchaseNo());
        assertEquals(expectedPurchase.getTenantId(), result.getTenantId());
        assertEquals(expectedPurchase.getUserId(), result.getUserId());
        assertEquals(expectedPurchase.getPurchaseAmount(), result.getPurchaseAmount());
    }

    @Test
    @DisplayName("根据采购单号查询 - 未找到数据")
    void testSelectByPurchaseNo_NotFound() {
        // Given
        String purchaseNo = "NONEXISTENT";
        
        when(productPurchaseMapper.selectByPurchaseNo(purchaseNo)).thenReturn(null);
        
        // When
        ProductPurchase result = productPurchaseService.selectByPurchaseNo(purchaseNo);
        
        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("根据采购单号查询 - 空采购单号")
    void testSelectByPurchaseNo_NullPurchaseNo() {
        // Given
        String purchaseNo = null;
        
        when(productPurchaseMapper.selectByPurchaseNo(purchaseNo)).thenReturn(null);
        
        // When
        ProductPurchase result = productPurchaseService.selectByPurchaseNo(purchaseNo);
        
        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("根据采购单号查询 - 空字符串采购单号")
    void testSelectByPurchaseNo_EmptyPurchaseNo() {
        // Given
        String purchaseNo = "";
        
        when(productPurchaseMapper.selectByPurchaseNo(purchaseNo)).thenReturn(null);
        
        // When
        ProductPurchase result = productPurchaseService.selectByPurchaseNo(purchaseNo);
        
        // Then
        assertNull(result);
    }
}