/**
 * @ProjectName: vems-product-manager
 * @Package: com.snbc.vems.product.controller.rrq
 * @ClassName: RRQInvoiceControllerTest
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 人人取发票测试类
 * @Author: wangsong
 * @CreateDate: 2020/10/12 11:23
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/10/12 11:23
 */
package com.snbc.bbpf.bus.product.manager.controller.tenant;

import com.snbc.bbpf.bus.product.manager.service.InvoiceService;
import com.snbc.bbpf.bus.product.manager.service.impl.InvoiceServiceImpl;
import com.snbc.bbpf.bus.product.market.common.mapper.InvoiceTitleMapper;
import com.snbc.bbpf.bus.product.market.common.mapper.ProductPurchaseMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.ArrayList;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
public class RRQInvoiceControllerTest {

    @InjectMocks
    private InvoiceController rrqInvoiceController;
    private MockMvc mockMvc;
    @Mock
    ProductPurchaseMapper productPurchaseMapper;
    @Mock
    InvoiceService invoiceService = new InvoiceServiceImpl();
    @Mock
    InvoiceTitleMapper invoiceTitleMapper;
    @Before
    public void setUp(){
        rrqInvoiceController = new InvoiceController();
        MockitoAnnotations.initMocks(this);
        // 构建mvc环境
        mockMvc = MockMvcBuilders.standaloneSetup(rrqInvoiceController).build();
    }

    private String paramJson = "{\n" +
            "    \"tenantId\": \"143\",\n" +
            "    \"productId\": \"143\",\n" +
            "    \"invoiceDeliveryId\": 1,\n" +
            "    \"applyType\": 2,\n" +
            "    \"invoiceType\": 2,\n" +
            "    \"invoiceTitle\": \"企业\",\n" +
            "    \"invoiceContent\": \"增值服务\",\n" +
            "    \"applyUser\": \"1\",\n" +
            "    \"invoiceCarrier\": 2,\n" +
            "    \"invoiceAmount\": 400,\n" +
            "\t\"productPurchaseId\":[\"10\",\"11\"],\n" +
            "    \"expressCompany\": null,\n" +
            "    \"expressNo\": null,\n" +
            "    \"receiverName\": \"张三\",\n" +
            "    \"receiverTel\": \"***********\",\n" +
            "    \"taxRegisterNo\": \"**************\",\n" +
            "    \"bankName\": \"招商银行\",\n" +
            "    \"bankNo\": \"37001111222233330137\",\n" +
            "    \"registerAddress\": \"北京市海淀区\",\n" +
            "    \"registerPhonenum\": \"***********\",\n" +
            "    \"mail\": \"<EMAIL>\",\n" +
            "    \"tenantName\": \"农夫山泉\"\n" +
            "}";

    /***
      * @Description:    申请发票测试
      * @Author:         wangsong
      * @CreateDate:     2020/10/13 18:59
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/10/13 18:59
      * @return :        void
     */
    @Test
    public void test_applyInvoice() throws Exception {
        ArrayList<BigDecimal> amountList = new ArrayList<>();
        amountList.add(new BigDecimal(1.00));
        amountList.add(new BigDecimal(2.00));
        Mockito.when(productPurchaseMapper.selectNotIssued(Mockito.any(),Mockito.any())).thenReturn(amountList);
        Mockito.when(invoiceTitleMapper.selectInvoiceByTenantId(Mockito.any())).thenReturn(1);
        Assert.assertEquals(this.mockMvc.perform(post("/console/v1/tenant/invoice/applyInvoice")
                .contentType(MediaType.APPLICATION_JSON)
                .content(paramJson))
                .andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString().contains("0000"),true);
    }
}
