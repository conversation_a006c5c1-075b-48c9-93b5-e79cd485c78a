package com.snbc.bbpf.bus.product.manager.resp.vems;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 售货机查询列表响应结果
 * <AUTHOR>
 *
 */

@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2017-04-18T03:12:18.193Z")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VemsResp {
    /**
     * 响应头信息
     */
    
    @JsonProperty("header")
    private HeaderResp header = null;
    /**
     * 响应数据
     */
    
    @JsonProperty("body")
    private VemList body = null;

}

