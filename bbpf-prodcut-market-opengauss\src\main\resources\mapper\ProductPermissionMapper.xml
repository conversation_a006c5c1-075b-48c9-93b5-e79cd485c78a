<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductPermissionMapper" >
  <!--批量插入 -->
  <insert id="insertPPermissionList" parameterType="java.util.List">
    insert into t_product_permission (id,permission_id, product_id)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      <trim prefix=" (" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=VARCHAR},
        #{item.permissionId,jdbcType=VARCHAR},
        #{item.productId,jdbcType=VARCHAR}
      </trim>
    </foreach>
  </insert>
  <delete id="deleteByProductIds" parameterType="java.lang.String" >
    delete from t_product_permission
    where product_id in
    <foreach collection="array" item="arr" index="no" open="("
             separator="," close=")">
      #{arr}
    </foreach>
  </delete>
  <delete id="deleteBypermissionIds" parameterType="java.lang.String" >
    delete from t_product_permission
    where permission_id in
    <foreach collection="array" item="arr" index="no" open="("
             separator="," close=")">
      #{arr}
    </foreach>
  </delete>
  <delete id="deleteByProductId" parameterType="java.lang.String" >
    delete from t_product_permission
    where product_id  = #{publicId,jdbcType=VARCHAR}
  </delete>
  <select id="selelctPermissionByProductId" parameterType="java.lang.String" resultType="com.snbc.bbpf.bus.product.market.common.entity.Permission">
      SELECT
          t1.permission_id as permissionId ,t1.parent_id as parentId,t1.permission_type as permissionType
      FROM
          t_permission t1
      LEFT JOIN t_product_permission t2 on t1.permission_id = t2.permission_id
      WHERE 1=1
--       and	(permission_type = 3 or permission_type = 4)
      and t2.product_id = #{productId,jdbcType=VARCHAR}
      ORDER BY
          t1.order_by
  </select>
</mapper>
