package com.snbc.bbpf.bus.product.market.common.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/*
* @author: liangJB
     * 功能描述: <br>
     * 赠送设备使用时长日报   2020-11-10
     * @date: 2020/8/12 13:13
     * 商户名称 赠送设备数(台) 赠送时长(天) 总金额(元) 赠送时间 赠送人   赠送原因
字段名称	类型	是否必填	描述
tenantName	String	是	商户名称
purchase_time	string	是	赠送时间
product_quantity	int	是	台数
purchase_amount	Decimal	是	总金额(元)
product_grade	Decimal	是	赠送时长(天)
give_userName	Decimal	是	赠送人
give_reason	giveReason	是	赠送原因
* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VemsProductDRPCountDto {
    
    private String tenantName;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchaseTime;
    
    private Integer productQuantity;
    
    private BigDecimal purchaseAmount;
    
    private String productGade;
    
    private String giveUserName;
    
    private String giveReason;
}
