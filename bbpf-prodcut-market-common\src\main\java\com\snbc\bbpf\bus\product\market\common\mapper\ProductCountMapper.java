package com.snbc.bbpf.bus.product.market.common.mapper;


import com.snbc.bbpf.bus.product.market.common.dto.order.ProductAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPAllCount;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ShopMRPCountIdDto;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountPageQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductCountQuery;
import com.snbc.bbpf.bus.product.market.common.vo.product.ProductMRPCount;
import com.snbc.bbpf.bus.product.market.common.vo.product.RPOrderQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductCountMapper {

    /**
     * @author: liangJB
     * 功能描述: <br>
     * 按类型进行服务统计列表展示
     * @date: 2020/8/12 13:26
     * @param: record
     * @return: ProductCountDto
     */
    List<ProductCountDto> getSeviceCountList(ProductCountPageQuery productCountPageQuery);

    /**
     * @author: liangJB
     * 功能描述: <br>
     * 按类型进行服务统计
     * @date: 2020/8/12 13:26
     * @param: productId
     * @return:  com.snbc.vems.product.dto.product.ProductAllCount
     */
    ProductAllCount getSeviceCountByType(ProductCountQuery productCountQuery);

    /**
     * @author: liangJB
     * 功能描述: <br>
     * 订单月报表统一统计
     * @date: 2020/8/12 13:26
     * @param: record
     * @return: com.snbc.vems.product.dto.product.ShopMRPAllCount
     */
    ShopMRPAllCount getSeviceMRPCountSum(ProductMRPCount productMRPCount);
    /**
     * @author: liangJB
     * 功能描述: <br>
     * 订单月报表统一获取数据查询
     * 查询条件为开始日期、结束时期、商户名称
     * 执行逻辑为大于等于开始日期  小于等于结束日期
     * @date: 2020/8/12 13:26
     * @param: record
     * @return: com.snbc.vems.product.dto.product.ShopMRPAllCount
     */
    List<ShopMRPCountDto> getSeviceMRPCountReport(ProductMRPCount productMRPCount);
    /**
     * @author: liangJB
     * 统计金额
     * @param record
     * @return
     */
    ShopMRPAllCount getSeviceDRPCountSum(ProductMRPCount productMRPCount);
    /**
     * @author: liangJB
     * 统计报表数导出接口
     * @param record
     * @return
     */
    List<ShopMRPCountDto> getSeviceDRPCountReport(ProductMRPCount productMRPCount);
    /**
     * 统计报表日月导出接口
     * @param rpOrderQuery
     * @return
     */
    List<ShopMRPCountIdDto> getSeviceRPByTenant(RPOrderQuery rpOrderQuery);
    /**
     * @author: liangJB
     * 插入日报表
     * @param record
     * @return
     */
    int insertSeviceRPByTenant(RPOrderQuery rpOrderQuery);
    /**
     * 插入月报表
     * @param record
     * @return
     */
    int insertSeviceRPByTenantM(RPOrderQuery rpOrderQuery);
    /**
     * @author: liangJB
     * 删除日报表
     * @param reportDay
     * @return
     */
    int deleteSeviceRPByTenant(@Param("reportDay")String reportDay);
    /**
     * @author: liangJB
     * 删除月报表
     * @param reportDay
     * @return
     */
    int deleteSeviceRPByTenantM(@Param("reportDay")String reportDay);

}
