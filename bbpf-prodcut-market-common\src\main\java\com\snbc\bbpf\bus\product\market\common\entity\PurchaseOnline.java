package com.snbc.bbpf.bus.product.market.common.entity;

import java.time.LocalDateTime;
/**
 *线上交易表类型实体类
 */
public class PurchaseOnline {
    //ID
    private String onlineId;
    /**
     * 购买支付id
     */
    private String productPurchaseId;

    private String purchaseMemeberId;
    /**
     * 通道
     */
    private Integer purchaseChannel;
    /**
     * 流水
     */
    private String purchaseChannelNo;
    /**
     * 状态
     */
    private String purchaseStatus;
    /**
     * 交易时间
     */
    private LocalDateTime purchaseTime;

    private String callbackContent;
    /**
     * 购买支付id
     */
    public String getOnlineId() {
        return onlineId;
    }
    /**
     * 设置 onlineId
     * @param onlineId
     */
    public void setOnlineId(String onlineId) {
        this.onlineId = onlineId == null ? null : onlineId.trim();
    }

    public String getProductPurchaseId() {
        return productPurchaseId;
    }
    /**
     * 设置 productPurchaseId
     * @param productPurchaseId
     */
    public void setProductPurchaseId(String productPurchaseId) {
        this.productPurchaseId = productPurchaseId == null ? null : productPurchaseId.trim();
    }

    public String getPurchaseMemeberId() {
        return purchaseMemeberId;
    }
    /**
     * 设置 purchaseMemeberId
     * @param purchaseMemeberId
     */
    public void setPurchaseMemeberId(String purchaseMemeberId) {
        this.purchaseMemeberId = purchaseMemeberId == null ? null : purchaseMemeberId.trim();
    }

    public Integer getPurchaseChannel() {
        return purchaseChannel;
    }

    public void setPurchaseChannel(Integer purchaseChannel) {
        this.purchaseChannel = purchaseChannel;
    }

    public String getPurchaseChannelNo() {
        return purchaseChannelNo;
    }
    /**
     * 设置 purchaseChannelNo
     * @param purchaseChannelNo
     */
    public void setPurchaseChannelNo(String purchaseChannelNo) {
        this.purchaseChannelNo = purchaseChannelNo == null ? null : purchaseChannelNo.trim();
    }

    public String getPurchaseStatus() {
        return purchaseStatus;
    }
    /**
     * 设置 purchaseStatus
     * @param purchaseStatus
     */
    public void setPurchaseStatus(String purchaseStatus) {
        this.purchaseStatus = purchaseStatus == null ? null : purchaseStatus.trim();
    }

    public LocalDateTime getPurchaseTime() {
        return purchaseTime;
    }
    /**
     * 设置 purchaseTime
     * @param purchaseTime
     */
    public void setPurchaseTime(LocalDateTime purchaseTime) {
        this.purchaseTime = purchaseTime;
    }

    public String getCallbackContent() {
        return callbackContent;
    }
    /**
     * 设置 confirmRemitTime
     * @param callbackContent
     */
    public void setCallbackContent(String callbackContent) {
        this.callbackContent = callbackContent == null ? null : callbackContent.trim();
    }
}
