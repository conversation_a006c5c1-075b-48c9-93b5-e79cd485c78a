/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @ClassName: BusLogVo
 * @Description: bus log entity
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/21
 * copyright 2020 barm Inc. All rights reserver
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenantLogVo {


     /**
     * 操作类型
     */
    private String logType;


    /** */
    private String remark;
    /**
     * 中文日志内容
     */
    private String zhContent;
    /**
     * 英文日志内容
     */
    private String enContent;
    /**
     * 组织机构名称
     */
    private String tenantName;

    /**
     * 组织机构ID
     */
    private String tenantId;
    /**
     * 日志时间
     */
    private String createTime;
    /**
     * 日志ID
     */
    private String logId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 操作员
     */
    private String userId;
    /**
     * 操作模块
     */
    private String logTarget;

    /**
     * 登录IP
     */
    private String ip;

}
