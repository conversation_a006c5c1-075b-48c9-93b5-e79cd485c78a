/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.product.manager.enums;

/**
 * @ClassName: SysCodeEnum
 * 系统编码
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/20 13:32
 */
public enum SysCodeEnum {
    BOSS("boss", "bbpfboss"),
    TENANT("tenant", "bbpftenant");

    private String status;
    private String statusName;

    SysCodeEnum(String status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }
    public String getStatus() {
        return status;
    }
    public String getStatusName() {
        return statusName;
    }
}
