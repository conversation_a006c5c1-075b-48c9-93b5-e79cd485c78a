package com.snbc.bbpf.bus.product.market.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: Message
 * @Description: 消息类
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/6/9
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class Message {
    private String msgId;
    //消息渠道1系统2邮件3短信4微信5钉钉
    private Integer msgChannelCode;
    //系统编号
    private String sysCode;
    //发送人编号
    private String senderId;
    //发送人姓名
    private String senderName;
    //编码类型
    private String codeType;
    //跳转的url
    private String gotoUrl;
    //消息类型1私信2广播
    private Integer msgModel;
    private String msgTitle;
    private String[] receiveNos;
    // 短信专用
    private String[] receiverPhone;
    private String sendUrl;
    //wjc1 add 20211216
    private int messageType;
    private String receiverGroup;
    //wjc1 add 阿里云短信
    private String templateCode;
    // 模板中变量json
    private String[] templateParamJsonArr;
}
