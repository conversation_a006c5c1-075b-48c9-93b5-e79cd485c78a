<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.ProductDetailMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.ProductDetail">
        <id column="detail_id" jdbcType="VARCHAR" property="detailId"/>
        <result column="product_purchase_id" jdbcType="VARCHAR" property="productPurchaseId"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="vems_id" jdbcType="VARCHAR" property="vemsId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="vems_name" jdbcType="VARCHAR" property="vemsName"/>
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime"/>
        <result column="due_time" jdbcType="TIMESTAMP" property="dueTime"/>
        <result column="available_value" jdbcType="INTEGER" property="availableValue"/>
        <result column="available_unit" jdbcType="VARCHAR" property="availableUnit"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="line_name" jdbcType="VARCHAR" property="lineName"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="product_quantity" property="productQuantity" jdbcType="INTEGER" />
        <result column="purchase_amount" property="purchaseAmount" jdbcType="DECIMAL" />
        <result column="discount_amount" property="discountAmount" jdbcType="DECIMAL" />
        <result column="payment_amount" property="paymentAmount" jdbcType="DECIMAL" />
        <result column="activation_time" jdbcType="TIMESTAMP" property="activationTime"/>
        <result column="is_renew" jdbcType="INTEGER" property="isRenew" />
    </resultMap>
    <sql id="Base_Column_List">
    detail_id, product_purchase_id, tenant_id, vems_id, product_id, vems_name, begin_time,
    due_time, available_value, available_unit, create_time, update_time,line_name, area_name,
    product_quantity,purchase_amount,discount_amount,payment_amount,activation_time,is_renew
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_product_detail
        where detail_id = #{detailId,jdbcType=VARCHAR}
    </select>
    <select id="selectByPurchaseNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_product_detail
        where product_purchase_id =
        ( SELECT product_purchase_id FROM t_product_purchase where purchase_no=
        #{purchaseNo,jdbcType=VARCHAR})
    </select>
    <insert id="insert" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductDetail">
    insert into t_product_detail (detail_id, product_purchase_id, tenant_id,
      vems_id, product_id, vems_name,
      begin_time, due_time, available_value,
      available_unit, create_time, update_time,line_name, area_name,
      product_quantity,purchase_amount,discount_amount,payment_amount,activation_time,is_renew
      )
    values (#{detailId,jdbcType=VARCHAR}, #{productPurchaseId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR},
      #{vemsId,jdbcType=VARCHAR}, #{productId,jdbcType=VARCHAR}, #{vemsName,jdbcType=VARCHAR},
      #{beginTime,jdbcType=TIMESTAMP}, #{dueTime,jdbcType=TIMESTAMP}, #{availableValue,jdbcType=INTEGER},
      #{availableUnit,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      , #{lineName,jdbcType=VARCHAR}, #{areaName,jdbcType=VARCHAR},#{productQuantity,jdbcType=INTEGER},
      #{purchaseAmount,jdbcType=DECIMAL},#{discountAmount,jdbcType=DECIMAL},#{paymentAmount,jdbcType=DECIMAL}
      , #{activationTime,jdbcType=TIMESTAMP},#{isRenew,jdbcType=INTEGER}
      )
  </insert>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into t_product_detail (detail_id, product_purchase_id, tenant_id,
        vems_id, product_id, vems_name,
        begin_time, due_time, available_value,
        available_unit, create_time, update_time,line_name, area_name,
        product_quantity,purchase_amount,discount_amount,payment_amount,
        activation_time,is_renew
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.detailId,jdbcType=VARCHAR},
                #{item.productPurchaseId,jdbcType=VARCHAR},
                #{item.tenantId,jdbcType=VARCHAR},
                #{item.vemsId,jdbcType=VARCHAR},
                #{item.productId,jdbcType=VARCHAR},
                 #{item.vemsName,jdbcType=VARCHAR},
                #{item.beginTime,jdbcType=TIMESTAMP},
                #{item.dueTime,jdbcType=TIMESTAMP},
                 #{item.availableValue,jdbcType=INTEGER},
                #{item.availableUnit,jdbcType=VARCHAR},
                 #{item.createTime,jdbcType=TIMESTAMP},
                 #{item.updateTime,jdbcType=TIMESTAMP},
                  #{item.lineName,jdbcType=VARCHAR},
                 #{item.areaName,jdbcType=VARCHAR},
                #{item.productQuantity,jdbcType=INTEGER},
                #{item.purchaseAmount,jdbcType=DECIMAL},
                #{item.discountAmount,jdbcType=DECIMAL},
                #{item.paymentAmount,jdbcType=DECIMAL},
                #{item.activationTime,jdbcType=TIMESTAMP},
                #{item.isRenew,jdbcType=INTEGER}
            </trim>
        </foreach>
    </insert>
    <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductDetail">
        insert into t_product_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="detailId != null">
                detail_id,
            </if>
            <if test="productPurchaseId != null">
                product_purchase_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="vemsId != null">
                vems_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="vemsName != null">
                vems_name,
            </if>
            <if test="beginTime != null">
                begin_time,
            </if>
            <if test="dueTime != null">
                due_time,
            </if>
            <if test="availableValue != null">
                available_value,
            </if>
            <if test="availableUnit != null">
                available_unit,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="areaName!=null">
                area_name,
            </if>
            <if test="lineName!=null">
                line_name,
            </if>
            <if test="productQuantity!=null">
                product_quantity,
            </if>
            <if test="purchaseAmount!=null">
                purchase_amount,
            </if>
            <if test="discountAmount!=null">
                discount_amount,
            </if>
            <if test="paymentAmount!=null">
                payment_amount,
            </if>
            <if test="activationTime!=null">
                activation_time,
            </if>
            <if test="isRenew!=null">
                is_renew,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="detailId != null">
                #{detailId,jdbcType=VARCHAR},
            </if>
            <if test="productPurchaseId != null">
                #{productPurchaseId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="vemsId != null">
                #{vemsId,jdbcType=VARCHAR},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=VARCHAR},
            </if>
            <if test="vemsName != null">
                #{vemsName,jdbcType=VARCHAR},
            </if>
            <if test="beginTime != null">
                #{beginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dueTime != null">
                #{dueTime,jdbcType=TIMESTAMP},
            </if>
            <if test="availableValue != null">
                #{availableValue,jdbcType=INTEGER},
            </if>
            <if test="availableUnit != null">
                #{availableUnit,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="areaName != null">
                #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="lineName!=null">
                #{lineName,jdbcType=VARCHAR},
            </if>
            <if test="productQuantity!=null">
                #{product_quantity,jdbcType=INTEGER},
            </if>
            <if test="purchaseAmount!=null">
                #{purchase_amount,jdbcType=DECIMAL},
            </if>
            <if test="discountAmount!=null">
                #{discount_amount,jdbcType=DECIMAL},
            </if>
            <if test="paymentAmount!=null">
                #{payment_amount,jdbcType=DECIMAL},
            </if>
            <if test="activationTime != null">
                #{activationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isRenew!=null">
                #{isRenew,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductDetail">
        update t_product_detail
        <set>
            <if test="productPurchaseId != null">
                product_purchase_id = #{productPurchaseId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="vemsId != null">
                vems_id = #{vemsId,jdbcType=VARCHAR},
            </if>
            <if test="productId != null">
                product_id = #{productId,jdbcType=VARCHAR},
            </if>
            <if test="vemsName != null">
                vems_name = #{vemsName,jdbcType=VARCHAR},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dueTime != null">
                due_time = #{dueTime,jdbcType=TIMESTAMP},
            </if>
            <if test="availableValue != null">
                available_value = #{availableValue,jdbcType=INTEGER},
            </if>
            <if test="availableUnit != null">
                available_unit = #{availableUnit,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="areaName != null">
                area_name = #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="lineName != null">
                line_name = #{lineName,jdbcType=VARCHAR},
            </if>
            <if test="productQuantity!=null">
                product_quantity=#{productQuantity,jdbcType=INTEGER},
            </if>
            <if test="purchaseAmount!=null">
                purchase_amount=#{purchaseAmount,jdbcType=DECIMAL},
            </if>
            <if test="discountAmount!=null">
                discount_amount=#{discountAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentAmount!=null">
                payment_amount=#{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="activationTime != null">
                activation_time = #{activationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isRenew!=null">
                is_renew = #{isRenew,jdbcType=INTEGER},
            </if>
        </set>
        where detail_id = #{detailId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.bus.product.market.common.entity.ProductDetail">
    update t_product_detail
    set product_purchase_id = #{productPurchaseId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      vems_id = #{vemsId,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=VARCHAR},
      vems_name = #{vemsName,jdbcType=VARCHAR},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      due_time = #{dueTime,jdbcType=TIMESTAMP},
      available_value = #{availableValue,jdbcType=INTEGER},
      available_unit = #{availableUnit,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      area_name = #{areaName,jdbcType=VARCHAR},
      line_name = #{lineName,jdbcType=VARCHAR},
      product_quantity=#{productQuantity,jdbcType=INTEGER},
      purchase_amount=#{purchaseAmount,jdbcType=DECIMAL},
      discount_amount=#{discountAmount,jdbcType=DECIMAL},
      payment_amount=#{paymentAmount,jdbcType=DECIMAL},
      activation_time=#{activationTime,jdbcType=TIMESTAMP},
      is_renew = #{isRenew,jdbcType=INTEGER}
    where detail_id = #{detailId,jdbcType=VARCHAR}
  </update>
</mapper>