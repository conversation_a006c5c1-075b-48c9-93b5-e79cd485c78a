package com.snbc.bbpf.bus.product.manager.converter;

import com.snbc.bbpf.bus.product.market.common.dto.order.ProductOrderListDto;
import com.snbc.bbpf.bus.product.market.common.dto.order.ProductShopOrderDto;
import com.snbc.bbpf.commons.objs.IConvert;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * ProductOrderListDto 到 ProductShopOrderDto 的转换器
 */
@Mapper
public interface ProductOrderListDtoToProductShopOrderDtoConverter extends IConvert<ProductOrderListDto, ProductShopOrderDto> {
    ProductOrderListDtoToProductShopOrderDtoConverter INSTANCE = Mappers.getMapper(ProductOrderListDtoToProductShopOrderDtoConverter.class);
}