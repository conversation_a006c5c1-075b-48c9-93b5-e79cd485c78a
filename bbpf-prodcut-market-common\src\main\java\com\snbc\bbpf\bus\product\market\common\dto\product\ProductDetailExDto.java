package com.snbc.bbpf.bus.product.market.common.dto.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/*
* @author: liangJB
     * 功能描述: <br>
     * 产品明细类
     * @date: 2020/8/12 13:13
参数名	参数位置	类型	描述	是否必填	示例
productId	页面	string	服务id	是
productImage	页面	string	产品服务图片地址	是
productBrief	页面	String	项目简介	是
productDetail	页面	String	项目详情	是
serviceRule	页面	String	服务条款	是
aftersaleRule	页面	String	售后条款	是
refundRule	页面	String	退款条款	是
tutorial	页面	String	使用手册地址	是
productTypeName	String	是	服务类型
chargeTypeName	String	是	收费方式

ProductGradeLst	页面	List<ProductGrade>	产品服务阶梯	是
* */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductDetailExDto extends  ProductDetailDto{
    
    private String productTypeName;
    
    private String productGradeUnit;
    
    private String chargeTypeName;

}
