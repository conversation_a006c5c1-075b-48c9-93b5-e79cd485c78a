<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.bus.product.market.common.mapper.InvoiceDeliveryMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.bus.product.market.common.entity.InvoiceDelivery">
        <id column="delivery_id" jdbcType="VARCHAR" property="deliveryId" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
        <result column="tel" jdbcType="VARCHAR" property="tel" />
        <result column="province_id" jdbcType="VARCHAR" property="provinceId" />
        <result column="city_id" jdbcType="VARCHAR" property="cityId" />
        <result column="area_id" jdbcType="VARCHAR" property="areaId" />
        <result column="address" jdbcType="VARCHAR" property="address" />
        <result column="express_company" jdbcType="VARCHAR" property="expressCompany" />
        <result column="express_no" jdbcType="VARCHAR" property="expressNo" />
    </resultMap>
    <sql id="Base_Column_List">
        delivery_id, tenant_id, receiver_name, tel, province_id, city_id, area_id, address,express_company,express_no
    </sql>
    <insert id="insertSelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.InvoiceDelivery">
        insert into t_invoice_delivery
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deliveryId != null">
                delivery_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="receiverName != null">
                receiver_name,
            </if>
            <if test="tel != null">
                tel,
            </if>
            <if test="provinceId != null">
                province_id,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="expressCompany != null">
                express_company,
            </if>
            <if test="expressNo">
                express_no
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deliveryId != null">
                #{deliveryId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="receiverName != null">
                #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                #{tel,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                #{provinceId,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null">
                #{areaId,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="expressCompany != null">
                #{expressCompany,jdbcType=VARCHAR},
            </if>
            <if test="expressNo != null">
                #{expressNo,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>
    <select id="selectExpressDelivery" parameterType="java.lang.String" resultMap = "BaseResultMap">
       SELECT
            t1.delivery_id,
            t1.tenant_id,
            t1.receiver_name,
            t1.tel,
            t1.province_id,
            t1.city_id,
            t1.area_id,
            t1.address,
            t1.express_company AS expressCompany,
            t1.express_no AS expressNo
        FROM
            t_invoice_delivery t1
        LEFT JOIN t_invoice_apply t2 on t1.delivery_id = t2.invoice_delivery_id
        WHERE
        t2.invoice_apply_id = #{invoiceApplyId,jdbcType=VARCHAR}
    </select>
    <select id="selectExpressInfo" parameterType="java.lang.String" resultType="com.snbc.bbpf.bus.product.market.common.dto.invoice.InvoceExpressDeliDto">
          SELECT
            t1.express_company AS expressCompany,
            t1.express_no AS expressNo
        FROM
            t_invoice_delivery t1
        LEFT JOIN t_invoice_apply t2 on t1.delivery_id = t2.invoice_delivery_id
        WHERE
        t2.invoice_apply_id = #{invoiceApplyId,jdbcType=VARCHAR}
    </select>
    <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.bus.product.market.common.entity.InvoiceDelivery">
        update t_invoice_delivery
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="receiverName != null">
                receiver_name = #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                tel = #{tel,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null">
                area_id = #{areaId,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="expressCompany != null">
                express_company = #{expressCompany,jdbcType=VARCHAR},
            </if>
            <if test="expressNo != null">
                express_no = #{expressNo,jdbcType=VARCHAR}
            </if>
        </set>
        where delivery_id = #{deliveryId,jdbcType=VARCHAR}
    </update>
    <update id="updateExpressInfo" parameterType="com.snbc.bbpf.bus.product.market.common.vo.invoice.ExperssInfo">
        UPDATE t_invoice_delivery
        SET express_company = #{expressCompany,jdbcType=VARCHAR},express_no = #{expressNo,jdbcType=VARCHAR}
        where delivery_id = ( SELECT
            invoice_delivery_id
        FROM
            t_invoice_apply
        WHERE
            invoice_apply_id = #{invoiceId,jdbcType=VARCHAR})
    </update>
    <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.bus.product.market.common.entity.InvoiceDelivery">
        update t_invoice_delivery
        set tenant_id = #{tenantId,jdbcType=VARCHAR},
        receiver_name = #{receiverName,jdbcType=VARCHAR},
        tel = #{tel,jdbcType=VARCHAR},
        province_id = #{provinceId,jdbcType=VARCHAR},
        city_id = #{cityId,jdbcType=VARCHAR},
        area_id = #{areaId,jdbcType=VARCHAR},
        address = #{address,jdbcType=VARCHAR},
        express_company = #{expressCompany,jdbcType=VARCHAR},
        express_no = #{expressNo,jdbcType=VARCHAR}
        where delivery_id = #{deliveryId,jdbcType=VARCHAR}
    </update>
    <select id="selectByIds" resultMap="BaseResultMap" parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from t_invoice_delivery
        where 1=1
        <if test="list != null and list.size() > 0">
            and delivery_id in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>