import csv
import sys

# 读取CSV文件并分析覆盖率
with open('bbpf-prodcut-market-manager/target/site/jacoco/jacoco.csv', 'r', encoding='utf-8') as f:
    reader = csv.DictReader(f)
    classes = []
    
    for row in reader:
        instruction_missed = int(row['INSTRUCTION_MISSED'])
        instruction_covered = int(row['INSTRUCTION_COVERED'])
        total_instructions = instruction_missed + instruction_covered
        
        if total_instructions > 0:
            coverage = (instruction_covered / total_instructions) * 100
        else:
            coverage = 0
            
        classes.append({
            'class': row['CLASS'],
            'package': row['PACKAGE'],
            'coverage': coverage,
            'total_instructions': total_instructions,
            'missed_instructions': instruction_missed
        })
    
    # 按覆盖率排序
    classes.sort(key=lambda x: x['coverage'])
    
    print('=== 覆盖率分析报告 ===')
    print(f'总类数: {len(classes)}')
    
    # 统计覆盖率分布
    zero_coverage = len([c for c in classes if c['coverage'] == 0])
    low_coverage = len([c for c in classes if 0 < c['coverage'] < 50])
    medium_coverage = len([c for c in classes if 50 <= c['coverage'] < 90])
    high_coverage = len([c for c in classes if c['coverage'] >= 90])
    
    print(f'0%覆盖率: {zero_coverage}个类')
    print(f'1-49%覆盖率: {low_coverage}个类')
    print(f'50-89%覆盖率: {medium_coverage}个类')
    print(f'90%+覆盖率: {high_coverage}个类')
    print()
    
    print('=== 需要优先补充测试的类（覆盖率最低的20个）===')
    for i, cls in enumerate(classes[:20]):
        print(f'{i+1:2d}. {cls["class"]:30s} - {cls["coverage"]:5.1f}% ({cls["missed_instructions"]:4d}条未覆盖指令)')
    
    print('\n=== 重点关注的Service类（覆盖率为0%）===')
    service_classes = [c for c in classes if 'service.impl' in c['package'] and c['coverage'] == 0]
    for cls in service_classes:
        print(f'- {cls["class"]:40s} ({cls["missed_instructions"]:4d}条未覆盖指令)')
    
    print('\n=== 重点关注的Controller类（覆盖率低于50%）===')
    controller_classes = [c for c in classes if 'controller' in c['package'] and c['coverage'] < 50]
    for cls in controller_classes:
        print(f'- {cls["class"]:40s} - {cls["coverage"]:5.1f}% ({cls["missed_instructions"]:4d}条未覆盖指令)')