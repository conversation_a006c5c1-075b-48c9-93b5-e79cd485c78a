package com.snbc.bbpf.bus.product.market.common.vo;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import jakarta.validation.constraints.NotNull;

@Data
public class ProductPublishBaseVo {

    @NotNull(message = "服务名称不能为空")
    
    private String productName;

    @NotEmpty(message = "产品服务id不能为空")
    
    private String productId;
    //产品图片地址
    @NotEmpty(message = "产品图片地址不能为空")
    
    private String productImage;
    //项目简介
    @NotEmpty(message = "产品服务id不能为空")
    
    private String productBrief;
    //项目详情
    @NotEmpty(message = "产品服务id不能为空")
    
    private String productDetail;
    //服务条款
    @NotEmpty(message = "服务条款不能为空")
    
    private String serviceRule;
    //售后条款
    @NotEmpty(message = "售后条款不能为空")
    
    private String aftersaleRule;
    //退款条款
    @NotEmpty(message = "退款条款不能为空")
    
    private String refundRule;
    //使用手册地址
    @NotEmpty(message = "使用手册地址不能为空")
    
    private String tutorial;
}
