package com.snbc.bbpf.bus.product.market.common.mapper;

import com.snbc.bbpf.bus.product.market.common.entity.PurchaseCoupon;
import org.apache.ibatis.annotations.Mapper;

/**
 * @ProjectName: VEMS-PRODUCT
 * @Package: com.snbc.vems.product.mapper
 * @ClassName: PurchaseCouponMapper
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 购买优惠券 mapper
 * @CreateDate: 2020/8/24 15:21
 * @UpdateDate: 2020/8/24 15:21
 */
@Mapper
public interface PurchaseCouponMapper {
    int deleteByPrimaryKey(String purchaseCouponId);

    int insert(PurchaseCoupon purchaseCoupon);

    int insertSelective(PurchaseCoupon purchaseCoupon);


    PurchaseCoupon selectByPrimaryKey(String purchaseCouponId);


    int updateByPrimaryKeySelective(PurchaseCoupon purchaseCoupon);

    int updateByPrimaryKey(PurchaseCoupon purchaseCoupon);
}
